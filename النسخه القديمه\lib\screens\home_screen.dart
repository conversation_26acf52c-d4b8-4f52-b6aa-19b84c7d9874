import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/providers/transaction_provider.dart';
import 'package:wardlytec_app/screens/auth/login_screen.dart';
import 'package:wardlytec_app/screens/company_selection_screen.dart';
import 'package:wardlytec_app/screens/network_selection_screen.dart';
import 'package:wardlytec_app/widgets/app_drawer.dart';
import 'package:wardlytec_app/screens/transaction_history_screen.dart';
import 'package:wardlytec_app/widgets/single_tap_button.dart';
import 'package:wardlytec_app/widgets/connectivity_wrapper.dart';
import 'package:wardlytec_app/services/rewards_notification_service.dart';
import 'package:wardlytec_app/services/service_status_service.dart';
import 'package:wardlytec_app/services/transaction_monitor_service.dart';
import 'package:wardlytec_app/screens/service_unavailable_screen.dart';
import 'package:wardlytec_app/screens/admin/admin_control_screen.dart';
import 'package:wardlytec_app/utils/network_helper.dart';

import 'package:wardlytec_app/utils/app_theme.dart';



class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  bool _showWelcomeCard = true;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  Timer? _refreshTimer;

  // متغيرات الدخول المخفي للمسؤول
  int _logoTapCount = 0;
  Timer? _resetTapTimer;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0), // تبدأ من الجانب الأيمن
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    // استرداد سجل المعاملات عند تحميل الشاشة
    _fetchTransactions();
    // إظهار بطاقة الترحيب
    _showWelcomeCardAnimation();
    // فحص وإظهار إشعارات المكافآت
    _checkRewardsNotifications();
    // بدء مراقبة الإشعارات
    _startNotificationMonitoring();

    // إعداد تحديث دوري كل 30 ثانية
    _startPeriodicRefresh();
  }



  @override
  void dispose() {
    _slideController.dispose();
    _refreshTimer?.cancel();
    _resetTapTimer?.cancel();
    // إيقاف مراقبة الإشعارات
    TransactionMonitorService.stopMonitoring();
    super.dispose();
  }

  /// بدء مراقبة الإشعارات للمستخدم الحالي
  Future<void> _startNotificationMonitoring() async {
    try {
      final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
      if (authProvider.currentUser != null) {
        await TransactionMonitorService.startMonitoring(
          authProvider.currentUser!.id.toString(),
        );
      }
    } catch (e) {
      // تجاهل الأخطاء في مراقبة الإشعارات لعدم تعطيل التطبيق
      if (mounted) {
        print('⚠️ تحذير: فشل في بدء مراقبة الإشعارات: $e');
      }
    }
  }

  /// معالجة الضغطات المخفية على الشعار للدخول كمسؤول
  void _handleLogoTap() {
    _logoTapCount++;

    // إعادة تعيين العداد بعد 3 ثوان
    _resetTapTimer?.cancel();
    _resetTapTimer = Timer(const Duration(seconds: 3), () {
      _logoTapCount = 0;
    });

    // إذا تم الضغط 7 مرات متتالية خلال 3 ثوان
    if (_logoTapCount >= 7) {
      _logoTapCount = 0;
      _resetTapTimer?.cancel();
      _showAdminAccessDialog();
    }
  }

  /// عرض حوار الوصول للمسؤول
  void _showAdminAccessDialog() {
    final TextEditingController pinController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.admin_panel_settings, color: Colors.orange),
              SizedBox(width: 8),
              Text('دخول المسؤول'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'أدخل رمز PIN للوصول إلى لوحة تحكم المسؤول',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: pinController,
                obscureText: true,
                keyboardType: TextInputType.number,
                maxLength: 6,
                decoration: const InputDecoration(
                  labelText: 'رمز PIN',
                  hintText: 'أدخل رمز PIN المكون من 6 أرقام',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.lock),
                ),
                onSubmitted: (value) {
                  _verifyAdminPin(value);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                _verifyAdminPin(pinController.text);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('دخول'),
            ),
          ],
        );
      },
    );
  }

  /// التحقق من رمز PIN للمسؤول
  void _verifyAdminPin(String pin) {
    // رمز PIN افتراضي للمسؤول (يجب تغييره في الإنتاج)
    const String adminPin = '123456';

    if (pin == adminPin) {
      Navigator.of(context).pop(); // إغلاق الحوار

      // الانتقال إلى صفحة تحكم المسؤول
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (_) => const AdminControlScreen(),
        ),
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text('مرحباً بك في لوحة تحكم المسؤول'),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.error, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text('رمز PIN غير صحيح'),
            ],
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  // إظهار بطاقة الترحيب المنزلقة
  void _showWelcomeCardAnimation() {
    // تأخير بسيط للتأكد من تحميل الشاشة
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _slideController.forward();
        // إخفاء البطاقة بعد 4 ثواني
        Future.delayed(const Duration(seconds: 4), () {
          if (mounted && _showWelcomeCard) {
            _hideWelcomeCard();
          }
        });
      }
    });
  }

  // إخفاء بطاقة الترحيب
  void _hideWelcomeCard() {
    _slideController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _showWelcomeCard = false;
        });
      }
    });
  }

  // الحصول على رسالة الترحيب
  Map<String, String> _getWelcomeMessage(String userName) {
    return {
      'title': '✨ شحن وتوريد بدون عمولة 💸',
      'subtitle': '',
    };
  }

  // استرداد سجل المعاملات
  Future<void> _fetchTransactions() async {
    if (!mounted) return;

    try {
      final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

      if (authProvider.currentUser != null) {
        await transactionProvider.fetchUserTransactions(authProvider.currentUser!.id.toString());
      }
    } catch (e) {
      print('خطأ في استرداد المعاملات: $e');
    }
  }

  // فحص وإظهار إشعار التجديد
  void _checkRewardsNotifications() {
    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser != null && mounted) {
      RewardsNotificationService.checkAndShowRenewalNotification(context, currentUser);
    }
  }

  // بدء التحديث الدوري للمعاملات
  void _startPeriodicRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _fetchTransactions();
      }
    });
  }



  // عرض رسالة تأكيد تسجيل الخروج
  Future<void> _showLogoutConfirmation() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'تسجيل الخروج',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: const Text(
            'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
            style: TextStyle(fontSize: 16),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            // زر الإلغاء
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
            ),
            // زر التأكيد محمي من الضغط المتعدد
            SingleTapButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              loadingText: 'جاري الحذف...',
              child: const Text('تسجيل الخروج'),
            ),
          ],
        );
      },
    );

    // إذا أكد المستخدم، قم بتسجيل الخروج
    if (result == true) {
      await _logout();
    }
  }

  // تسجيل الخروج
  Future<void> _logout() async {
    // إيقاف مراقبة الإشعارات قبل تسجيل الخروج
    await TransactionMonitorService.stopMonitoring();

    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
    await authProvider.signOut();

    if (!mounted) return;

    // عرض رسالة تأكيد محسنة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Expanded(
              child: Text(
                'تم تسجيل الخروج بنجاح',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green[600],
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );

    // العودة إلى شاشة تسجيل الدخول
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (_) => const LoginScreen()),
      (route) => false,
    );
  }

  // الانتقال إلى صفحة اختيار الشبكة
  Future<void> _navigateToNetworkSelection() async {
    // فحص حالة خدمة الشحن أولاً
    final rechargeStatus = await ServiceStatusService.getRechargeServiceStatus();

    if (rechargeStatus != null && !rechargeStatus.isEnabled) {
      // الخدمة معطلة - عرض صفحة عدم التوفر
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => ServiceUnavailableScreen(
              serviceStatus: rechargeStatus,
              onRetry: () {
                Navigator.of(context).pop();
                // إعادة المحاولة
              },
            ),
          ),
        );
      }
      return;
    }

    // الخدمة متاحة - متابعة العملية العادية
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => const NetworkSelectionScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      showNoInternetCard: true, // تظهر بطاقة عدم الاتصال
      showQualityIndicator: false, // إخفاء مؤشر جودة الاتصال
      child: Scaffold(
      appBar: AppBar(
        title: GestureDetector(
          onTap: _handleLogoTap,
          child: const Text('وردلي تك'),
        ),
        elevation: 0,
        leading: Builder(
          builder: (context) => Container(
            margin: const EdgeInsets.all(8.0),
            child: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              radius: 22, // زيادة الحجم من الافتراضي (20) إلى 22
              child: IconButton(
                icon: const Icon(
                  Icons.menu,
                  color: Colors.white,
                  size: 24, // زيادة حجم الأيقونة من 20 إلى 24
                ),
                onPressed: () => Scaffold.of(context).openDrawer(),
                tooltip: 'القائمة',
                padding: const EdgeInsets.all(8), // تحسين المساحة الداخلية
              ),
            ),
          ),
        ),
        actions: [
          // زر سجل المعاملات - حجم أكبر
          Container(
            margin: const EdgeInsets.all(8.0),
            child: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              radius: 22, // زيادة الحجم من الافتراضي (20) إلى 22
              child: IconButton(
                icon: const Icon(
                  Icons.history,
                  color: Colors.white,
                  size: 24, // زيادة حجم الأيقونة من 20 إلى 24
                ),
                onPressed: () async {
                  // فحص الاتصال قبل عرض سجل المعاملات
                  final hasConnection = await NetworkHelper.checkConnectionWithDialog(
                    context,
                    title: 'يتطلب اتصال بالإنترنت',
                    message: 'عرض سجل المعاملات يتطلب اتصال بالإنترنت لجلب أحدث البيانات.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                  );

                  if (hasConnection) {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => const TransactionHistoryScreen(),
                      ),
                    );
                  }
                },
                tooltip: 'سجل المعاملات',
                padding: const EdgeInsets.all(8), // تحسين المساحة الداخلية
              ),
            ),
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: RefreshIndicator(
        onRefresh: () async {
          await _fetchTransactions();
          // إعادة تشغيل Timer للتحديث الدوري
          _refreshTimer?.cancel();
          _startPeriodicRefresh();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // بطاقة الترحيب المنزلقة
              if (_showWelcomeCard)
                SlideTransition(
                  position: _slideAnimation,
                  child: Consumer<SupabaseAuthProvider>(
                    builder: (context, authProvider, child) {
                      final currentUser = authProvider.currentUser;

                      if (currentUser == null) return const SizedBox.shrink();

                      final welcomeMessage = _getWelcomeMessage(
                        currentUser.name ?? 'عميل وردلي تك',
                      );

                      return Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Card(
                          elevation: 8,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppTheme.primaryColor,
                                  AppTheme.accentColor,
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Row(
                              children: [
                                // أيقونة المستخدم
                                Container(
                                  width: 60,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(30),
                                  ),
                                  child: Center(
                                    child: Text(
                                      currentUser.name?.substring(0, 1).toUpperCase() ?? 'و',
                                      style: const TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // نص الترحيب
                                Expanded(
                                  child: Text(
                                    welcomeMessage['title']!,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      shadows: [
                                        Shadow(
                                          offset: Offset(1, 1),
                                          blurRadius: 2,
                                          color: Colors.black26,
                                        ),
                                      ],
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),

              // بطاقة بدء التوريد
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      // أيقونة التوريد
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(40),
                        ),
                        child: const Icon(
                          Icons.account_balance_wallet,
                          size: 40,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // عنوان البطاقة
                      const Text(
                        'التوريد لشركات التوصيل',
                        style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      // وصف البطاقة
                      Text(
                        'تسهيل عملية توريد أموال شركة التوصيل التي تعمل بها بكل سهولة وأمان',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      // زر بدء التوريد
                      SingleTapButton(
                        onPressed: () async {
                          // فحص حالة خدمة التوريد أولاً
                          final supplyStatus = await ServiceStatusService.getSupplyServiceStatus();

                          if (supplyStatus != null && !supplyStatus.isEnabled) {
                            // الخدمة معطلة - عرض صفحة عدم التوفر
                            if (mounted) {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (_) => ServiceUnavailableScreen(
                                    serviceStatus: supplyStatus,
                                    onRetry: () {
                                      Navigator.of(context).pop();
                                      // إعادة المحاولة
                                    },
                                  ),
                                ),
                              );
                            }
                            return;
                          }

                          // الخدمة متاحة - متابعة العملية العادية
                          final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
                          final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

                          // الانتقال لشاشة اختيار الشركة
                          final result = await Navigator.of(context).push(
                            MaterialPageRoute(builder: (_) => const CompanySelectionScreen()),
                          );

                          // إعادة تحميل المعاملات عند العودة
                          if (result == true && mounted && authProvider.currentUser != null) {
                            await transactionProvider.fetchUserTransactions(authProvider.currentUser!.id.toString());
                            // إعادة تشغيل Timer للتحديث الدوري
                            _refreshTimer?.cancel();
                            _startPeriodicRefresh();
                          }
                        },
                        checkConnection: true, // فحص الاتصال قبل بدء التوريد
                        connectionTitle: 'يتطلب اتصال بالإنترنت',
                        connectionMessage: 'بدء عملية التوريد يتطلب اتصال بالإنترنت لإنشاء المعاملة وإرسالها.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                        icon: const Icon(Icons.arrow_forward, size: 20),
                        loadingText: 'جاري التحميل...',
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          minimumSize: const Size(double.infinity, 50),
                        ),
                        child: const Text(
                          'ابدأ التوريد',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // بطاقة شحن رصيد الهاتف
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      // أيقونة الشحن
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(40),
                        ),
                        child: const Icon(
                          Icons.phone_android,
                          size: 40,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // عنوان البطاقة
                      const Text(
                        'شحن رصيد الهاتف',
                        style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      // وصف البطاقة
                      Text(
                        'شحن رصيد خطوط فودافون وإتصالات وأورانج ووي بكل سهولة',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      // زر بدء الشحن
                      SingleTapButton(
                        onPressed: () => _navigateToNetworkSelection(),
                        checkConnection: true,
                        connectionTitle: 'يتطلب اتصال بالإنترنت',
                        connectionMessage: 'شحن رصيد الهاتف يتطلب اتصال بالإنترنت.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                        icon: const Icon(Icons.arrow_forward, size: 20),
                        loadingText: 'جاري التحميل...',
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          minimumSize: const Size(double.infinity, 50),
                        ),
                        child: const Text(
                          'ابدأ الشحن',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    ),
    );
  }


}