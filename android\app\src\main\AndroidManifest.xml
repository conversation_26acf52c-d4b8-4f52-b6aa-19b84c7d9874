<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- صلاحيات أساسية مطلوبة -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- صلاحيات الإعلانات (Google AdMob) -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <!-- صلاحيات الصور والكاميرا -->
    <!-- للإصدارات Android 13+ (API 33+) -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- للإصدارات الأقدم من Android 13 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />

    <!-- صلاحية الكاميرا -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- إزالة الأذونات غير الضرورية للأمان -->
    <!-- <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> -->
    <!-- <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> -->
    <!-- <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> -->

    <application
        android:label="وردلي تك"
        android:name=".MainApplication"
        android:icon="@mipmap/launcher_icon">

        <!-- إعدادات Google AdMob للإنتاج -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-9880594970188723~8556707868"/>

        <!-- إعدادات إضافية للأمان -->
        <meta-data
            android:name="com.google.android.gms.ads.flag.DELAY_APP_MEASUREMENT_INIT"
            android:value="true"/>

        <!-- إعدادات الشبكة للإعلانات -->
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
            android:value="true"/>
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
            android:value="true"/>
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
        <!-- إضافة دعم لفتح واتساب -->
        <package android:name="com.whatsapp" />
        <package android:name="com.whatsapp.w4b" />
        <!-- إضافة دعم لفتح Instapay -->
        <package android:name="com.instapay.android" />
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="instapay" />
        </intent>
        <!-- إضافة دعم لفتح فودافون كاش -->
        <package android:name="com.vodafone.wallet" />
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="vodafonecash" />
        </intent>
        <!-- إضافة دعم لفتح اتصالات كاش -->
        <package android:name="com.etisalat.flous" />
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="flous" />
        </intent>
        <!-- إضافة دعم لفتح أورنج كاش -->
        <package android:name="com.orange.orangecash" />
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="orangecash" />
        </intent>
        <!-- إضافة دعم لفتح الروابط -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="http" />
        </intent>
    </queries>
</manifest>
