import 'package:flutter/foundation.dart';

/// إعدادات الإنتاج للتطبيق
class ProductionConfig {
  // منع إنشاء instance من هذا الكلاس
  ProductionConfig._();

  /// التحقق من أن التطبيق في وضع الإنتاج
  static bool get isProduction => kReleaseMode;

  /// رابط سياسة الخصوصية
  static const String privacyPolicyUrl = String.fromEnvironment(
    'PRIVACY_POLICY_URL',
    defaultValue: 'https://example.com/privacy-policy',
  );

  /// رابط شروط الاستخدام
  static const String termsOfServiceUrl = String.fromEnvironment(
    'TERMS_OF_SERVICE_URL',
    defaultValue: 'https://example.com/terms-of-service',
  );

  /// معلومات التطبيق للنشر
  static const String appName = 'وردلي تك';
  static const String appNameEn = 'Wardly Tech';
  static const String appVersion = '1.0.0';
  static const int appBuildNumber = 1;

  /// وصف التطبيق
  static const String appDescription =
    'تطبيق يهدف لتسهيل عمليات الدفع الالكتروني في مصر';

  static const String appDescriptionEn =
    'Wardly Tech app for easy and secure money supply for delivery drivers in Egypt';

  /// معلومات المطور
  static const String developerName = 'Wardly Team';
  static const String developerEmail = '<EMAIL>';
  static const String supportEmail = '<EMAIL>';
  static const String privacyEmail = '<EMAIL>';

  /// روابط التواصل الاجتماعي
  static const String whatsappNumber = '+201201937252';

  /// إعدادات المتجر
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.wardly.app';
  static const String appStoreUrl = 'https://apps.apple.com/app/wardly/id123456789';

  /// فئات التطبيق
  static const String category = 'Finance';
  static const String contentRating = 'Everyone';
  static const List<String> keywords = [
    'توريد',
    'أموال',
    'طلبات',
    'مرسول',
    'مندوبين',
    'توصيل',
    'delivery',
    'drivers',
    'money transfer',
    'talabat',
    'mrsool',
    'آمن',
    'سريع'
  ];

  /// إعدادات الأمان للإنتاج
  static const bool enableCrashReporting = true;
  static const bool enableAnalytics = true;
  static const bool enablePerformanceMonitoring = true;

  /// التحقق من صحة إعدادات الإنتاج
  static bool validateProductionSettings() {
    if (!isProduction) {
      if (kDebugMode) {
        print('⚠️ التطبيق ليس في وضع الإنتاج');
      }
      return false;
    }

    // التحقق من وجود الروابط المطلوبة
    if (privacyPolicyUrl.contains('github.io') && !privacyPolicyUrl.contains('your-')) {
      if (kDebugMode) {
        print('✅ رابط سياسة الخصوصية صحيح');
      }
    } else {
      if (kDebugMode) {
        print('⚠️ يجب تحديث رابط سياسة الخصوصية');
      }
      return false;
    }

    return true;
  }

  /// طباعة معلومات الإنتاج
  static void printProductionInfo() {
    if (kDebugMode) {
      print('🚀 معلومات الإنتاج:');
      print('   📱 اسم التطبيق: $appName ($appNameEn)');
      print('   🔢 الإصدار: $appVersion ($appBuildNumber)');
      print('   🏷️ الفئة: $category');
      print('   🔗 سياسة الخصوصية: $privacyPolicyUrl');
      print('   📧 الدعم: $supportEmail');
      print('   📞 واتساب: $whatsappNumber');
    }
  }

  /// الحصول على معلومات التطبيق للمتجر
  static Map<String, dynamic> getStoreInfo() {
    return {
      'name': appName,
      'name_en': appNameEn,
      'description': appDescription,
      'description_en': appDescriptionEn,
      'version': appVersion,
      'build_number': appBuildNumber,
      'category': category,
      'content_rating': contentRating,
      'keywords': keywords,
      'developer_name': developerName,
      'developer_email': developerEmail,
      'support_email': supportEmail,
      'privacy_policy_url': privacyPolicyUrl,
      'terms_of_service_url': termsOfServiceUrl,
      'whatsapp': whatsappNumber,
    };
  }
}
