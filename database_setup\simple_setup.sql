-- إعد<PERSON> قاعدة البيانات المبسط لتطبيق وردلي تك
-- Simple database setup for Wardly Tech app
-- 🚀 شغل هذا الملف في Supabase SQL Editor

-- ========================================
-- 1. إنشاء جدول المستخدمين
-- ========================================

CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    phone_number VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100),
    secret_code VARCHAR(10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    favorite_accounts TEXT[] DEFAULT '{}',
    ad_views_remaining INTEGER DEFAULT 20,
    recharge_ad_views_remaining INTEGER DEFAULT 20
);

-- فهار<PERSON> للأداء
CREATE INDEX IF NOT EXISTS idx_users_phone_number ON users(phone_number);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- ========================================
-- 2. إنشاء جدول المعاملات
-- ========================================

CREATE TABLE IF NOT EXISTS transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    commission DECIMAL(10,2) DEFAULT 0,
    wallet_type VARCHAR(50) NOT NULL,
    talabat_account_number VARCHAR(50) NOT NULL,
    sender_wallet_number VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    transaction_type VARCHAR(20) DEFAULT 'supply',
    proof_image_url TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

-- ========================================
-- 3. إنشاء الدوال والـ Triggers
-- ========================================

-- دالة تحديث last_login
CREATE OR REPLACE FUNCTION update_last_login()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_login = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        NEW.completed_at = NOW();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers
DROP TRIGGER IF EXISTS trigger_update_last_login ON users;
CREATE TRIGGER trigger_update_last_login
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_last_login();

DROP TRIGGER IF EXISTS trigger_update_updated_at ON transactions;
CREATE TRIGGER trigger_update_updated_at
    BEFORE UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

-- ========================================
-- 4. إعداد Row Level Security (مؤقت للتطوير)
-- ========================================

-- تفعيل RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- سياسات مؤقتة للتطوير (يجب تشديدها لاحقاً)
DROP POLICY IF EXISTS "Allow all operations on users" ON users;
CREATE POLICY "Allow all operations on users" ON users FOR ALL USING (true);

DROP POLICY IF EXISTS "Allow all operations on transactions" ON transactions;
CREATE POLICY "Allow all operations on transactions" ON transactions FOR ALL USING (true);

-- ========================================
-- 5. بيانات تجريبية (اختيارية)
-- ========================================

INSERT INTO users (phone_number, name, secret_code) VALUES
('***********', 'أحمد محمد', '123456'),
('***********', 'فاطمة علي', '654321'),
('***********', 'محمد حسن', '111222'),
('***********', 'سارة أحمد', '999888'),
('***********', 'علي حسام', '777666')
ON CONFLICT (phone_number) DO NOTHING;

-- معاملة تجريبية واحدة
INSERT INTO transactions (
    user_id, amount, commission, wallet_type, 
    talabat_account_number, sender_wallet_number, 
    transaction_type, status
) VALUES
(1, 1000.00, 5.00, 'Vodafone Cash', '*********', '***********', 'supply', 'completed')
ON CONFLICT DO NOTHING;

-- ========================================
-- 6. التحقق من النتائج
-- ========================================

-- عرض المستخدمين
SELECT 
    id,
    phone_number as "رقم الهاتف",
    name as "الاسم",
    ad_views_remaining as "مرات التوريد",
    recharge_ad_views_remaining as "مرات الشحن",
    created_at as "تاريخ الإنشاء"
FROM users 
ORDER BY id;

-- عرض المعاملات
SELECT 
    t.id as "رقم المعاملة",
    u.name as "المستخدم",
    t.amount as "المبلغ",
    t.commission as "العمولة",
    t.transaction_type as "النوع",
    t.status as "الحالة"
FROM transactions t
JOIN users u ON t.user_id = u.id
ORDER BY t.created_at DESC;

-- رسالة النجاح
SELECT '🎉 تم إعداد قاعدة البيانات بنجاح!' as "النتيجة";
