import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class FAQScreen extends StatelessWidget {
  final bool isForLoggedInUsers;

  const FAQScreen({Key? key, this.isForLoggedInUsers = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأسئلة الشائعة'),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          const SizedBox(height: 16),
          
          // مقدمة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(
                    Icons.help_outline,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'الأسئلة الشائعة',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'إجابات على أكثر الأسئلة شيوعاً حول تطبيق وردلي تك',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),

          // الأسئلة والأجوبة - تختلف حسب نوع المستخدم
          ...isForLoggedInUsers ? _buildLoggedInUserFAQs(context) : _buildGuestFAQs(context),
          
          const SizedBox(height: 32),
          
          // بطاقة التواصل
          Card(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(
                    Icons.support_agent,
                    size: 32,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'لم تجد إجابة لسؤالك؟',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'تواصل معنا عبر واتساب وسنساعدك فوراً',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _launchWhatsApp();
                    },
                    icon: const Icon(Icons.chat, color: Colors.white),
                    label: const Text(
                      'تواصل معنا',
                      style: TextStyle(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  // بناء أسئلة الزوار
  List<Widget> _buildGuestFAQs(BuildContext context) {
    return [
      _buildFAQItem(
        context,
        '❓ ما هو تطبيق وردلي تك؟',
        'تطبيق وردلي تك هو حل ذكي لتسهيل خدمات الدفع الالكتروني في مصر. يمكنك من خلاله تحويل أموالك بسهولة وأمان.',
      ),

  _buildFAQItem(
        context,
        '❓ كيف أقوم بإنشاء حساب جديد؟',
        '1. أدخل اسمك ورقم هاتفك في الشاشة الرئيسية\n'
        '2. اضغط على "تفعيل حساب جديد"\n'
        '3. اضغط على طلب كلمه السر لإرسال رسالة واتساب للإدارة\n'
        '4. انتظر الرد برقم سري للتفعيل\n'
        '5. أدخل الرقم السري لتفعيل حسابك',
      ),

      _buildFAQItem(
        context,
        '🔑 نسيت الرقم السري، ماذا أفعل؟',
        'يمكنك:\n'
        '• استخدام زر "تسجيل الدخول" من الشاشة الرئيسية\n'
        '• التواصل مع الدعم الفني عبر واتساب\n'
        '• طلب رقم سري جديد من الإدارة',
      ),

      _buildFAQItem(
        context,
        '💰 كم تبلغ العمولة؟',
        'العمولة هي 0.5% فقط من المبلغ المحول. يمكنك إلغاء العمولة بالكامل عبر مشاهدة إعلان قصير.',
      ),

      _buildFAQItem(
        context,
        '🏦 ما هي المحافظ المدعومة؟',
        'ندعم جميع المحافظ الإلكترونية الشائعة مثل فودافون كاش، اورنچ كاش، اتصالات كاش، cib smart wallet، انستاباي، وكذلك البنوك المختلفة.',
      ),

      _buildFAQItem(
        context,
        '⏱️ كم يستغرق التحويل؟',
        'عادة ما يتم التحويل خلال دقائق معدودة بعد تأكيد العملية ورفع صورة إثبات التحويل.',
      ),

      _buildFAQItem(
        context,
        '🔒 هل التطبيق آمن؟',
        'نعم، التطبيق آمن تماماً. نستخدم أحدث تقنيات الحماية والتشفير لضمان أمان بياناتك وأموالك.',
      ),

      _buildFAQItem(
        context,
        '📞 كيف أتواصل مع الدعم؟',
        'يمكنك التواصل معنا عبر واتساب من خلال زر الدعم في التطبيق، أو من خلال القائمة الجانبية.',
      ),

      _buildFAQItem(
        context,
        '📺 كيف تعمل الإعلانات؟',
        'يمكنك مشاهدة إعلان قصير (15-30 ثانية) لإلغاء العمولة بالكامل. لديك عدد محدود من المرات شهرياً.',
      ),
    ];
  }

  // بناء أسئلة المستخدمين المسجلين
  List<Widget> _buildLoggedInUserFAQs(BuildContext context) {
    return [
    
      _buildFAQItem(
        context,
        '💳 ما هي طرق التحويل المتاحة للتوريد؟',
        'طرق التحويل المتاحة:\n\n'
        '🏧 **إيداع عن طريق ماكينة ATM:**\n'
        '• إيداع نقدي مباشر في ماكينة الصراف الآلي\n'
        '• سريع وآمن\n\n'
        '🏦 **تحويل بنكي:**\n'
        '• تحويل من حسابك البنكي\n'
        '• عبر تطبيق انستاباي أو تطبيق البنك\n\n'
        '📱 **المحافظ الرقمية:**\n'
        '• فودافون كاش، اتصالات كاش، اورنچ كاش\n'
        '•   InstaPay\n\n'
        '⚠️ **نصائح مهمة:**\n'
        '• تأكد من إدخال بيانات التحويل بدقة\n'
        '• احتفظ بإيصال التحويل\n'
        '• تواصل مع الدعم عند أي مشكلة\n'
        '• جميع التحويلات مؤمنة ومشفرة',
      ),

      _buildFAQItem(
        context,
        '📊 كيف أراجع سجل معاملاتي؟',
        'من القائمة الجانبية:\n'
        '• اضغط على "سجل المعاملات"\n'
        '• ستجد جميع معاملاتك مرتبة حسب التاريخ\n'
        '• يمكنك البحث والتصفية حسب الشركة أو التاريخ',
      ),

      _buildFAQItem(
        context,
        '🔒 هل بياناتي آمنة؟',
        'نعم، بياناتك محمية بالكامل:\n'
        '• تشفير قوي لجميع البيانات\n'
        '• خوادم آمنة ومحمية\n'
        '• لا نشارك بياناتك مع أطراف ثالثة\n'
        '• يمكنك مراجعة سياسة الخصوصية للمزيد',
      ),

      _buildFAQItem(
        context,
        '📱 التطبيق لا يعمل بشكل صحيح؟',
        'جرب الحلول التالية:\n'
        '• تأكد من اتصالك بالإنترنت\n'
        '• أعد تشغيل التطبيق\n'
        '• تأكد من تحديث التطبيق لآخر إصدار\n'
        '• إذا استمرت المشكلة، تواصل مع الدعم الفني',
      ),

      _buildFAQItem(
        context,
        '🎧 كيف أتواصل مع الدعم الفني؟',
        'يمكنك التواصل معنا عبر:\n'
        '• واتساب: من خلال زر "الدعم الفني" في القائمة\n'
        '• سيتم الرد عليك في أسرع وقت ممكن\n'
        '• أوقات العمل: من 9 صباحاً إلى 6 مساءً',
      ),

      _buildFAQItem(
        context,
        '🔄 كيف أحدث التطبيق؟',
        'لتحديث التطبيق:\n'
        '• افتح متجر التطبيقات (Google Play أو App Store)\n'
        '• ابحث عن "وردلي تك"\n'
        '• اضغط على "تحديث" إذا كان متاحاً\n'
        '• أو فعّل التحديث التلقائي',
      ),

      _buildFAQItem(
        context,
        '⚡ نصائح لاستخدام أفضل',
        '• احتفظ برقمك السري في مكان آمن\n'
        '• راجع معاملاتك بانتظام\n'
        '• تأكد من صحة البيانات قبل التأكيد\n'
        '• استخدم الوضع الليلي لراحة العينين\n'
        '• فعّل الإشعارات لمتابعة حالة معاملاتك',
      ),
    ];
  }

  Widget _buildFAQItem(BuildContext context, String question, String answer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              answer,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  // فتح واتساب
  Future<void> _launchWhatsApp() async {
    const String phoneNumber = '+201201937252';
    const String message = 'مرحباً، لدي سؤال حول تطبيق وردلي تك';
    final String url = 'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}';

    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      print('خطأ في فتح واتساب: $e');
    }
  }
}
