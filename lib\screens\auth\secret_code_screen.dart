import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/screens/home_screen.dart';
import 'package:wardlytec_app/screens/auth/existing_user_login_screen.dart';
import 'package:wardlytec_app/utils/app_theme.dart';
import 'package:wardlytec_app/widgets/countdown_timer.dart';
import 'package:wardlytec_app/config/app_config.dart';
import 'package:wardlytec_app/services/temp_data_cleanup_service.dart';
import 'package:wardlytec_app/supabase_config.dart';

class SecretCodeScreen extends StatefulWidget {
  final String customerName;
  final String phoneNumber;

  const SecretCodeScreen({
    Key? key,
    required this.customerName,
    required this.phoneNumber,
  }) : super(key: key);

  @override
  State<SecretCodeScreen> createState() => _SecretCodeScreenState();
}

class _SecretCodeScreenState extends State<SecretCodeScreen> {
  final TextEditingController _secretCodeController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isRequestingPassword = false;
  bool _showTimer = false; // متغير للتحكم في إظهار المؤقت
  bool _timerFinished = false; // متغير لتتبع انتهاء المؤقت
  bool _isPasswordEmpty = true; // متغير لتتبع حالة حقل كلمة المرور
  int _timerKey = 0; // مفتاح لإعادة إنشاء المؤقت
  String? _error;
  Duration? _remainingTime; // الوقت المتبقي للبيانات المؤقتة

  @override
  void initState() {
    super.initState();
    // إضافة listener لتتبع تغييرات حقل كلمة المرور
    _secretCodeController.addListener(_onPasswordChanged);
    // تحديث الوقت المتبقي للبيانات المؤقتة
    _updateRemainingTime();
  }

  // حفظ المستخدم في قاعدة البيانات عند نجاح تسجيل الدخول
  Future<Map<String, dynamic>> _saveUserToDatabase(String name, String phoneNumber, String secretCode) async {
    try {
      final supabase = SupabaseConfig.client;

      // أولاً: التحقق من وجود المستخدم
      final existingUsers = await supabase
          .from('users')
          .select('id,name,phone_number')
          .eq('phone_number', phoneNumber);

      if (existingUsers.isNotEmpty) {
        // المستخدم موجود مسبقاً - نحديث بياناته بدلاً من إنشاء جديد
        final existingUser = existingUsers.first;

        final updatedUser = await supabase
            .from('users')
            .update({
              'name': name,
              'secret_code': secretCode,
              'last_login': DateTime.now().toIso8601String(),
            })
            .eq('id', existingUser['id'])
            .select()
            .single();

        return {'success': true, 'data': updatedUser, 'action': 'updated'};
      }

      // إذا لم يكن المستخدم موجود، إنشاء جديد
      final newUser = await supabase
          .from('users')
          .insert({
            'name': name,
            'phone_number': phoneNumber,
            'secret_code': secretCode,
            'created_at': DateTime.now().toIso8601String(),
            'favorite_accounts': []
          })
          .select()
          .single();

      return {'success': true, 'data': newUser, 'action': 'created'};
    } catch (e) {
      // معالجة خطأ التكرار
      if (e.toString().contains('duplicate') || e.toString().contains('unique')) {
        return {
          'success': false,
          'error': 'رقم الهاتف مسجل مسبقاً. استخدم "تسجيل الدخول" بدلاً من إنشاء حساب جديد.',
          'error_type': 'duplicate_phone'
        };
      }
      return {'success': false, 'error': e.toString()};
    }
  }

  // تنظيف البيانات المؤقتة
  Future<void> _cleanupTempData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('temp_customer_name');
      await prefs.remove('temp_phone_number');
      await prefs.remove('temp_secret_code');
      await prefs.remove('temp_data_expiry');
    } catch (e) {
      // تجاهل أخطاء التنظيف
    }
  }

  @override
  void dispose() {
    _secretCodeController.removeListener(_onPasswordChanged);
    _secretCodeController.dispose();
    // لا نقوم بتنظيف البيانات المؤقتة هنا - ستنتهي صلاحيتها تلقائياً بعد 5 دقائق
    super.dispose();
  }

  // دالة لتتبع تغييرات حقل كلمة المرور
  void _onPasswordChanged() {
    final isEmpty = _secretCodeController.text.trim().isEmpty;
    if (_isPasswordEmpty != isEmpty) {
      setState(() {
        _isPasswordEmpty = isEmpty;
      });
    }
  }

  // تحديث الوقت المتبقي للبيانات المؤقتة
  Future<void> _updateRemainingTime() async {
    final remainingTime = await TempDataCleanupService.getRemainingTime();
    if (mounted) {
      setState(() {
        _remainingTime = remainingTime;
      });
    }
  }

  // دالة طلب كلمة السر عبر WhatsApp
  Future<void> _requestPassword() async {
    setState(() {
      _isRequestingPassword = true;
      _error = null;
    });

    try {
      // إنشاء رسالة طلب كلمة السر
      final message = '''مرحباً، أحتاج إلى كلمة السر الخاصة بحسابي في تطبيق وردلي:

👤 الاسم: ${widget.customerName}
📱 رقم الهاتف: ${widget.phoneNumber}

يرجى إرسال كلمة السر للدخول إلى التطبيق.

شكراً لكم.''';

      // رقم WhatsApp للمدير من الإعدادات الآمنة
      const adminWhatsApp = AppConfig.adminWhatsApp;

      // إنشاء رابط WhatsApp
      final whatsappUrl = 'https://wa.me/$adminWhatsApp?text=${Uri.encodeComponent(message)}';

      // فتح WhatsApp
      await _launchWhatsApp(whatsappUrl);

      // عرض رسالة نجاح وإظهار المؤقت
      if (mounted) {
        setState(() {
          _showTimer = true; // إظهار المؤقت بعد إرسال الطلب
          _timerFinished = false; // إعادة تعيين حالة المؤقت لبدء عد جديد
          _timerKey++; // تغيير مفتاح المؤقت لإعادة إنشائه
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تم إرسال طلب كلمة السر! سيتم الرد عليك عبر WhatsApp قريباً',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green[600],
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'فشل في إرسال الطلب: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRequestingPassword = false;
        });
      }
    }
  }

  // فتح WhatsApp
  Future<void> _launchWhatsApp(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'لا يمكن فتح WhatsApp';
      }
    } catch (e) {
      // للويب: فتح في نافذة جديدة
      await launchUrl(Uri.parse(url), mode: LaunchMode.platformDefault);
    }
  }

  // تسجيل الدخول بكلمة السر
  Future<void> _loginWithSecretCode() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    final secretCode = _secretCodeController.text.trim();

    try {
      // أولاً: التحقق من البيانات المؤقتة وصلاحيتها
      final prefs = await SharedPreferences.getInstance();
      final tempName = prefs.getString('temp_customer_name');
      final tempPhone = prefs.getString('temp_phone_number');
      final tempSecretCode = prefs.getString('temp_secret_code');
      final expiryTime = prefs.getInt('temp_data_expiry');

      // التحقق من انتهاء صلاحية البيانات المؤقتة
      if (expiryTime != null && DateTime.now().millisecondsSinceEpoch > expiryTime) {
        // البيانات منتهية الصلاحية - تنظيفها
        await _cleanupTempData();
        setState(() {
          _error = 'انتهت صلاحية البيانات المؤقتة. يرجى إنشاء حساب جديد.';
        });
        return;
      }

      // التحقق من صحة كلمة السر
      if (tempSecretCode != null && tempSecretCode == secretCode &&
          tempPhone == widget.phoneNumber && tempName == widget.customerName) {

        // كلمة السر صحيحة - الآن نحفظ في قاعدة البيانات
        final saveResult = await _saveUserToDatabase(tempName!, tempPhone!, tempSecretCode!);

        if (saveResult['success']) {
          // لا نعرض رسالة إنشاء الحساب - سنعرض رسالة تسجيل الدخول فقط

          // تسجيل الدخول باستخدام AuthProvider
          if (!mounted) return;
          final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
          final loginSuccess = await authProvider.loginWithSecretCode(
            phoneNumber: widget.phoneNumber,
            secretCode: secretCode,
            customerName: widget.customerName,
          );

          if (loginSuccess && mounted) {
            // مسح البيانات المؤقتة عند نجاح تسجيل الدخول
            await _cleanupTempData();

            // عرض رسالة نجاح محسنة
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          'تم تفعيل الحساب وتسجيل الدخول بنجاح!',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  backgroundColor: Colors.green[600],
                  duration: const Duration(seconds: 3),
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  margin: const EdgeInsets.all(16),
                ),
              );

              // الانتقال إلى الشاشة الرئيسية
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const HomeScreen()),
                (route) => false,
              );
            }
          } else {
            setState(() {
              _error = 'حدث خطأ في تسجيل الدخول بعد حفظ البيانات';
            });
          }
        } else {
          // معالجة خاصة لخطأ التكرار
          final errorType = saveResult['error_type'];
          final errorMessage = errorType == 'duplicate_phone'
              ? 'رقم الهاتف مسجل مسبقاً. استخدم "تسجيل الدخول" بدلاً من إنشاء حساب جديد.'
              : 'حدث خطأ في حفظ البيانات: ${saveResult['error']}';

          setState(() {
            _error = errorMessage;
          });

          // تم حذف الإشعار المكرر - Dialog في login_screen.dart يتولى هذه المهمة
        }
      } else {
        // كلمة السر غير صحيحة أو البيانات غير متطابقة
        setState(() {
          _error = 'كلمة السر غير صحيحة';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'حدث خطأ: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفعيل الحساب'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
              const SizedBox(height: 40),

              // أيقونة تفعيل الحساب
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.verified_user,
                  size: 50,
                  color: AppTheme.primaryColor,
                ),
              ),

              const SizedBox(height: 32),

              // عنوان الشاشة
              Text(
                'مرحباً بك!',
                style: Theme.of(context).textTheme.displaySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 8),

              // النص التوضيحي
              Text(
                'اطلب كلمة السر لتفعيل حسابك',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),


              
              const SizedBox(height: 32),
              
              // بيانات التسجيل
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'بيانات التسجيل',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Icon(
                          Icons.person,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'الاسم: ${widget.customerName}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.phone,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'الهاتف: ${widget.phoneNumber}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              


              const SizedBox(height: 32),

              // حقل كلمة السر
              TextFormField(
                controller: _secretCodeController,
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 8,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(6),
                ],
                decoration: InputDecoration(
                  labelText: 'كلمة السر',
                  hintText: '123456',
                  hintStyle: TextStyle(
                    color: Colors.grey.withOpacity(0.4),
                    fontSize: 16,
                  ),
                  prefixIcon: const Icon(Icons.vpn_key, color: AppTheme.primaryColor),
                  counterText: '',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: AppTheme.primaryColor, width: 2),
                  ),
                  errorText: _error,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال كلمة السر';
                  }
                  if (value.length < 4) {
                    return 'كلمة السر قصيرة جداً';
                  }
                  return null;
                },
                onChanged: (value) {
                  if (_error != null) {
                    setState(() {
                      _error = null;
                    });
                  }
                },
              ),



              const SizedBox(height: 32),

              // زر تسجيل الدخول
              ElevatedButton.icon(
                onPressed: (_isLoading || _isPasswordEmpty) ? null : _loginWithSecretCode,
                icon: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(
                          Icons.login,
                          size: 24,
                          color: Colors.white, // نفس لون أيقونة زر تفعيل الحساب
                        ),
                label: Text(
                  _isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: (_isLoading || _isPasswordEmpty)
                      ? Colors.grey[400]
                      : AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 18), // نفس padding زر تفعيل الحساب
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: (_isLoading || _isPasswordEmpty) ? 1 : 3,
                  minimumSize: const Size(double.infinity, 56),
                ),
              ),

              const SizedBox(height: 16),

              // مؤقت العد التنازلي - يظهر فقط بعد طلب كلمة السر
              if (_showTimer) ...[
                Center(
                  child: CountdownTimer(
                    key: ValueKey(_timerKey), // مفتاح لإعادة إنشاء المؤقت
                    onTimerFinished: () {
                      // انتهى المؤقت - يمكن الآن طلب كلمة سر جديدة
                      if (mounted) {
                        setState(() {
                          _timerFinished = true; // المؤقت انتهى
                          // _showTimer يبقى true - المؤقت لا يختفي
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: (_showTimer && !_timerFinished)
                        ? (Theme.of(context).brightness == Brightness.dark
                            ? Colors.blue[900]?.withOpacity(0.3)
                            : Colors.blue[50])
                        : (Theme.of(context).brightness == Brightness.dark
                            ? Colors.orange[900]?.withOpacity(0.3)
                            : Colors.orange[50]),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: (_showTimer && !_timerFinished)
                          ? (Theme.of(context).brightness == Brightness.dark
                              ? Colors.blue[400]!
                              : Colors.blue[200]!)
                          : (Theme.of(context).brightness == Brightness.dark
                              ? Colors.orange[400]!
                              : Colors.orange[200]!)
                    ),
                  ),
                  child: Text(
                    (_showTimer && !_timerFinished)
                        ? 'تم إرسال طلب كلمة السر. يرجى انتظار الرد خلال الوقت المحدد.'
                        : 'إذا لم تستلم كلمة المرور، اطلب مجدداً',
                    style: TextStyle(
                      fontSize: 12,
                      color: (_showTimer && !_timerFinished)
                          ? (Theme.of(context).brightness == Brightness.dark
                              ? Colors.blue[300]
                              : Colors.blue[700])
                          : (Theme.of(context).brightness == Brightness.dark
                              ? Colors.orange[300]
                              : Colors.orange[700]),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // زر طلب كلمة السر
              ElevatedButton.icon(
                onPressed: (_isRequestingPassword || (_showTimer && !_timerFinished)) ? null : _requestPassword,
                icon: _isRequestingPassword
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(
                        Icons.message,
                        size: 24,
                        color: Colors.white,
                      ),
                label: Text(
                  _isRequestingPassword
                      ? 'جاري الإرسال...'
                      : (_showTimer && !_timerFinished)
                          ? 'تم إرسال الطلب'
                          : 'طلب كلمة السر',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: (_isRequestingPassword || (_showTimer && !_timerFinished))
                      ? Colors.grey[400]
                      : AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 18),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: (_isRequestingPassword || (_showTimer && !_timerFinished)) ? 1 : 3,
                  minimumSize: const Size(double.infinity, 56),
                ),
              ),


              ],
            ),
          ),
        ),
      ),
    );
  }
}
