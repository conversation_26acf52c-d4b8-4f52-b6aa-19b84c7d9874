import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/providers/transaction_provider.dart';
import 'package:wardlytec_app/providers/theme_provider.dart';
import 'package:wardlytec_app/screens/splash_screen.dart';
import 'package:wardlytec_app/services/ad_manager.dart';
import 'package:wardlytec_app/services/temp_data_cleanup_service.dart';
import 'package:wardlytec_app/services/connectivity_service.dart';
import 'package:wardlytec_app/services/rewards_renewal_service.dart';
import 'package:wardlytec_app/services/local_notification_service.dart';
import 'package:wardlytec_app/utils/app_theme.dart';
import 'package:wardlytec_app/supabase_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // تهيئة متوازية للخدمات غير المترابطة لتحسين الأداء
    await Future.wait([
      SupabaseConfig.initialize(),
      AdManager.initialize(),
      TempDataCleanupService.cleanupExpiredTempData(),
      LocalNotificationService.initialize(),
    ]);
    print('✅ Core services initialized successfully');

    // فحص وتجديد المكافآت الشهرية (يحتاج Supabase)
    final renewedUsers = await RewardsRenewalService.runAutomaticRenewalCheck();
    if (renewedUsers.isNotEmpty) {
      print('✅ Rewards renewal completed for ${renewedUsers.length} users');
    } else {
      print('✅ Rewards renewal check completed - no users due for renewal');
    }

    // تهيئة خدمة مراقبة الاتصال
    ConnectivityService().initialize();
    print('✅ Connectivity service initialized');
  } catch (e) {
    print('❌ Initialization failed: $e');
    // في حالة فشل التهيئة، نستمر بتشغيل التطبيق مع إعدادات افتراضية
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SupabaseAuthProvider()),
        ChangeNotifierProvider(create: (_) => TransactionProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'وردلي تك',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.themeMode,
            locale: const Locale('ar', 'EG'),
            supportedLocales: const [Locale('ar', 'EG')],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            home: const SplashScreen(),
          );
        },
      ),
    );
  }
}

