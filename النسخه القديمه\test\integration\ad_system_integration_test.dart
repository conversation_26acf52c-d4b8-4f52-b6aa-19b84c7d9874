import 'package:flutter_test/flutter_test.dart';
import 'package:wardlytec_app/services/ad_manager.dart';
import 'package:wardlytec_app/utils/ad_connection_monitor.dart';

void main() {
  group('🔗 Ad System Integration Tests', () {
    setUp(() {
      // تنظيف قبل كل اختبار
      AdConnectionMonitor.dispose();
    });

    tearDown(() {
      // تنظيف بعد كل اختبار
      AdConnectionMonitor.dispose();
    });

    test('Complete ad system flow should work correctly', () async {
      // محاكاة التدفق الكامل لنظام الإعلانات
      
      // 1. حالة البداية
      bool hasWatchedAdInSession = false;
      bool isLoading = false;
      bool adButtonEnabled = true;
      int commission = 1;
      int remainingViews = 5;

      // التحقق من شرط تفعيل الزر
      bool shouldEnable = (commission > 0 && remainingViews > 0 && !hasWatchedAdInSession);
      expect(shouldEnable, true);

      // 2. بدء مشاهدة الإعلان
      isLoading = true;
      hasWatchedAdInSession = true; // تعيين أنه تم بدء المشاهدة

      // التحقق من تعطيل الزر أثناء التحميل
      shouldEnable = (commission > 0 && remainingViews > 0 && !hasWatchedAdInSession && !isLoading);
      expect(shouldEnable, false);

      // 3. فشل الإعلان - إعادة تعيين الحالة
      isLoading = false;
      hasWatchedAdInSession = false; // إعادة تعيين للمحاولة مرة أخرى

      // التحقق من إعادة تفعيل الزر
      shouldEnable = (commission > 0 && remainingViews > 0 && !hasWatchedAdInSession && !isLoading);
      expect(shouldEnable, true);

      // 4. محاولة ثانية - نجاح
      isLoading = true;
      hasWatchedAdInSession = true;

      // محاكاة نجاح الإعلان
      bool adWatchedSuccessfully = true;
      if (adWatchedSuccessfully) {
        // حفظ الحالة
        isLoading = false;
        // hasWatchedAdInSession يبقى true
      }

      // التحقق من عدم إمكانية المشاهدة مرة أخرى
      shouldEnable = (commission > 0 && remainingViews > 0 && !hasWatchedAdInSession && !isLoading);
      expect(shouldEnable, false);
    });

    test('AdConnectionMonitor integration should work', () async {
      bool connectionLostCalled = false;
      bool buttonReactivatedCalled = false;

      // بدء مراقبة الاتصال
      AdConnectionMonitor.startAdMonitoring(
        onConnectionLost: () {
          connectionLostCalled = true;
        },
        onAdButtonReactivated: () {
          buttonReactivatedCalled = true;
        },
      );

      // التحقق من بدء المراقبة
      expect(AdConnectionMonitor.isMonitoring, true);

      // انتظار قصير
      await Future.delayed(const Duration(milliseconds: 50));

      // إيقاف المراقبة
      AdConnectionMonitor.stopMonitoring();
      expect(AdConnectionMonitor.isMonitoring, false);
    });

    test('Session management should work correctly', () {
      // اختبار إدارة الجلسات
      final now = DateTime.now().millisecondsSinceEpoch;
      final sessionId = 'session_$now';
      final sessionKey = 'ad_watched_$sessionId';

      // التحقق من تنسيق المفاتيح
      expect(sessionId, startsWith('session_'));
      expect(sessionKey, startsWith('ad_watched_session_'));

      // اختبار تحليل timestamp
      final parts = sessionKey.split('_');
      expect(parts.length, 4); // ['ad', 'watched', 'session', 'timestamp']
      
      final timestamp = int.tryParse(parts.last);
      expect(timestamp, isNotNull);
      expect(timestamp, greaterThan(0));

      // اختبار منطق التنظيف
      final oldTimestamp = now - (25 * 60 * 60 * 1000); // 25 ساعة قديمة
      final shouldDelete = (now - oldTimestamp) > (24 * 60 * 60 * 1000);
      expect(shouldDelete, true);

      final newTimestamp = now - (1 * 60 * 60 * 1000); // ساعة واحدة قديمة
      final shouldKeep = (now - newTimestamp) <= (24 * 60 * 60 * 1000);
      expect(shouldKeep, true);
    });

    test('Error handling scenarios should work', () {
      // اختبار سيناريوهات معالجة الأخطاء
      
      // سيناريو 1: انقطاع الاتصال
      bool hasWatchedAdInSession = true;
      bool isLoading = true;
      
      // محاكاة معالجة انقطاع الاتصال
      hasWatchedAdInSession = false;
      isLoading = false;
      
      expect(hasWatchedAdInSession, false);
      expect(isLoading, false);

      // سيناريو 2: فشل تحميل الإعلان
      hasWatchedAdInSession = true;
      isLoading = true;
      
      // محاكاة معالجة فشل التحميل
      hasWatchedAdInSession = false;
      isLoading = false;
      
      expect(hasWatchedAdInSession, false);
      expect(isLoading, false);

      // سيناريو 3: timeout
      hasWatchedAdInSession = true;
      isLoading = true;
      
      // محاكاة معالجة timeout
      hasWatchedAdInSession = false;
      isLoading = false;
      
      expect(hasWatchedAdInSession, false);
      expect(isLoading, false);
    });

    test('Button state management should be consistent', () {
      // اختبار إدارة حالة الزر
      
      // حالة البداية
      bool isLoading = false;
      bool enabled = true;
      bool hasOnPressed = true;
      
      bool isDisabled = isLoading || !enabled || !hasOnPressed;
      expect(isDisabled, false);

      // أثناء التحميل
      isLoading = true;
      isDisabled = isLoading || !enabled || !hasOnPressed;
      expect(isDisabled, true);

      // إعادة تفعيل الزر
      isLoading = false;
      isDisabled = isLoading || !enabled || !hasOnPressed;
      expect(isDisabled, false);

      // تعطيل الزر
      enabled = false;
      isDisabled = isLoading || !enabled || !hasOnPressed;
      expect(isDisabled, true);
    });

    test('Notification messages should be correct', () {
      // اختبار رسائل الإشعارات
      const expectedFailureMessage = 'لم يتم إكمال مشاهدة الإعلان. تم إعادة تفعيل الزر - يمكنك المحاولة مرة أخرى';
      const expectedSuccessMessage = 'تم احتساب مشاهدة الإعلان بنجاح!';
      
      expect(expectedFailureMessage, contains('تم إعادة تفعيل الزر'));
      expect(expectedFailureMessage, contains('يمكنك المحاولة مرة أخرى'));
      expect(expectedSuccessMessage, contains('بنجاح'));
    });
  });
}
