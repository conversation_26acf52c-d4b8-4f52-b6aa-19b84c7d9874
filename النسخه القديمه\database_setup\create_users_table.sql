-- إنشاء جدول المستخدمين لتطبيق وردلي تك
-- Create users table for Wardly Tech app

CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    phone_number VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100),
    secret_code VARCHAR(10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    favorite_accounts TEXT[] DEFAULT '{}',
    ad_views_remaining INTEGER DEFAULT 20,
    recharge_ad_views_remaining INTEGER DEFAULT 20
);

-- إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_users_phone_number ON users(phone_number);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- إضا<PERSON>ة تعليقات للأعمدة
COMMENT ON TABLE users IS 'جدول المستخدمين لتطبيق وردلي تك';
COMMENT ON COLUMN users.id IS 'معرف المستخدم الفريد';
COMMENT ON COLUMN users.phone_number IS 'رقم الهاتف (فريد)';
COMMENT ON COLUMN users.name IS 'اسم المستخدم';
COMMENT ON COLUMN users.secret_code IS 'الرقم السري للمستخدم';
COMMENT ON COLUMN users.created_at IS 'تاريخ إنشاء الحساب';
COMMENT ON COLUMN users.last_login IS 'آخر تسجيل دخول';
COMMENT ON COLUMN users.favorite_accounts IS 'حسابات طلبات المفضلة';
COMMENT ON COLUMN users.ad_views_remaining IS 'عدد مرات مشاهدة الإعلان المتبقية للتوريد';
COMMENT ON COLUMN users.recharge_ad_views_remaining IS 'عدد مرات مشاهدة الإعلان المتبقية للشحن';

-- إنشاء دالة لتحديث last_login تلقائياً
CREATE OR REPLACE FUNCTION update_last_login()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_login = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث last_login عند تحديث البيانات
CREATE TRIGGER trigger_update_last_login
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_last_login();

-- إدراج بيانات تجريبية (اختيارية)
INSERT INTO users (phone_number, name, secret_code) VALUES
('***********', 'أحمد محمد', '123456'),
('***********', 'فاطمة علي', '654321'),
('***********', 'محمد حسن', '111222')
ON CONFLICT (phone_number) DO NOTHING;

-- عرض النتيجة
SELECT 
    id,
    phone_number as "رقم الهاتف",
    name as "الاسم",
    secret_code as "الرقم السري",
    ad_views_remaining as "مرات التوريد",
    recharge_ad_views_remaining as "مرات الشحن",
    created_at as "تاريخ الإنشاء"
FROM users 
ORDER BY id;
