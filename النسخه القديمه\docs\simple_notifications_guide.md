# 📱 دليل نظام الإشعارات المبسط

## 🎯 نظرة عامة

تم تبسيط نظام الإشعارات ليعمل تلقائياً بدون إعدادات مستخدم. جميع العملاء يتلقون الإشعارات التالية:

---

## 📋 أنواع الإشعارات

### **1. إشعارات المعاملات** 💰
- ✅ **عند تغيير حالة معاملة التوريد**
- ✅ **عند تغيير حالة معاملة الشحن**

**الحالات المشمولة:**
- `pending` → `approved` (تم قبول المعاملة)
- `approved` → `completed` (تم إنجاز المعاملة)
- `pending` → `rejected` (تم رفض المعاملة)

### **2. إشعارات الخدمات** ⚙️
- ✅ **عند تعطيل خدمة التوريد**
- ✅ **عند تعطيل خدمة الشحن**
- ✅ **عند تفعيل الخدمات مرة أخرى**

---

## 🔧 التثبيت والإعداد

### **1. التبعيات المطلوبة:**
```yaml
dependencies:
  flutter_local_notifications: ^17.2.2
  timezone: ^0.9.4
```

### **2. إعداد قاعدة البيانات:**
```sql
-- شغل هذا الملف في Supabase SQL Editor
-- database_setup/create_simple_notifications.sql
```

### **3. تهيئة النظام:**
```dart
// في main.dart
await LocalNotificationService.initialize();
```

---

## 🚀 كيفية العمل

### **مراقبة المعاملات:**
```dart
// يتم تلقائياً عند تسجيل الدخول
await TransactionMonitorService.startMonitoring(userId);
```

### **إرسال الإشعارات:**
```dart
// تلقائي عند تغيير حالة المعاملة
await LocalNotificationService.showTransactionUpdateNotification(
  transactionId: 'TXN123',
  oldStatus: 'قيد المراجعة',
  newStatus: 'مقبولة',
  transactionType: 'supply_transactions',
  amount: 100.0,
);

// تلقائي عند تعطيل خدمة
await LocalNotificationService.showServiceDisabledNotification(
  serviceName: 'supply_transactions',
  message: 'خدمة التوريد معطلة مؤقتاً للصيانة',
);
```

---

## 📱 أمثلة الإشعارات

### **إشعار تحديث معاملة:**
```
📱 العنوان: "✅ تم قبول معاملة التوريد"
📝 المحتوى: "المعاملة #TXN123 بقيمة 100.00 ج.م
تم تحديث الحالة إلى: مقبولة"
```

### **إشعار تعطيل خدمة:**
```
📱 العنوان: "⚠️ تعطيل خدمة التوريد"
📝 المحتوى: "خدمة التوريد معطلة مؤقتاً للصيانة.
نعتذر عن الإزعاج وسنعيد تفعيلها قريباً."
```

---

## 🗄️ قاعدة البيانات

### **الجداول:**
- `notification_logs` - سجل الإشعارات المرسلة فقط

### **الدوال المتاحة:**
- `log_notification()` - تسجيل إشعار جديد
- `get_notification_stats()` - إحصائيات الإشعارات
- `cleanup_old_notifications()` - تنظيف الإشعارات القديمة

---

## 🎯 المزايا

### **✅ البساطة:**
- لا توجد إعدادات معقدة
- جميع الإشعارات مفعلة تلقائياً
- تجربة موحدة لجميع المستخدمين

### **✅ الأداء:**
- أقل استهلاك للذاكرة
- أسرع في التحميل
- أقل تعقيد في الكود

### **✅ سهولة الصيانة:**
- كود أقل للصيانة
- أخطاء أقل
- تحديثات أسهل

---

## 🛠️ استكشاف الأخطاء

### **الإشعارات لا تظهر:**
1. تحقق من أذونات الإشعارات في الجهاز
2. تأكد من تهيئة `LocalNotificationService`
3. تحقق من اتصال الإنترنت

### **المراقبة لا تعمل:**
1. تأكد من تسجيل الدخول
2. تحقق من إعدادات Supabase Realtime
3. راجع console للأخطاء

---

## 📞 الدعم

للمساعدة:
1. راجع logs التطبيق
2. تحقق من حالة Supabase
3. تواصل مع فريق التطوير

---

**🎉 نظام إشعارات مبسط وفعال بدون تعقيدات! 🚀**
