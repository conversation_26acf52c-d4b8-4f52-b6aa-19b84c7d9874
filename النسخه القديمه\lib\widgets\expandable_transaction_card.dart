import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/transaction.dart';
import '../utils/app_theme.dart';
import '../utils/currency_formatter.dart';

class ExpandableTransactionCard extends StatefulWidget {
  final Transaction transaction;

  const ExpandableTransactionCard({
    Key? key,
    required this.transaction,
  }) : super(key: key);

  @override
  State<ExpandableTransactionCard> createState() => _ExpandableTransactionCardState();
}

class _ExpandableTransactionCardState extends State<ExpandableTransactionCard> {
  bool _isExpanded = false;
  Timer? _countdownTimer;
  Duration _remainingTime = const Duration(minutes: 5);

  Color _getStatusColor() {
    switch (widget.transaction.status) {
      case 'تم':
      case 'completed':
      case 'مكتمل':
        return AppTheme.successColor;
      case 'جاري':
      case 'pending':
        return AppTheme.pendingColor;
      case 'مرفوض':
      case 'cancelled':
      case 'ملغي':
        return AppTheme.errorColor;
      case 'معلق':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Color _getBackgroundColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    switch (widget.transaction.status) {
      case 'تم':
      case 'completed':
      case 'مكتمل':
        return isDark ? Colors.green.shade900.withOpacity(0.3) : Colors.green.shade50;
      case 'جاري':
      case 'pending':
        return isDark ? Colors.orange.shade900.withOpacity(0.3) : Colors.orange.shade50;
      case 'مرفوض':
      case 'cancelled':
      case 'ملغي':
        return isDark ? Colors.red.shade900.withOpacity(0.3) : Colors.red.shade50;
      case 'معلق':
        return isDark ? Colors.blue.shade900.withOpacity(0.3) : Colors.blue.shade50;
      default:
        return isDark ? Colors.grey.shade800.withOpacity(0.3) : Colors.grey.shade50;
    }
  }

  Color _getBorderColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    switch (widget.transaction.status) {
      case 'تم':
      case 'completed':
      case 'مكتمل':
        return isDark ? Colors.green.shade700 : Colors.green.shade300;
      case 'جاري':
      case 'pending':
        return isDark ? Colors.orange.shade700 : Colors.orange.shade300;
      case 'مرفوض':
      case 'cancelled':
      case 'ملغي':
        return isDark ? Colors.red.shade700 : Colors.red.shade300;
      case 'معلق':
        return isDark ? Colors.blue.shade700 : Colors.blue.shade300;
      default:
        return isDark ? Colors.grey.shade600 : Colors.grey.shade300;
    }
  }

  // لون خلفية زر التوسيع - بدون شفافية في الوضع الغامق
  Color _getExpandButtonBackgroundColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    switch (widget.transaction.status) {
      case 'تم':
      case 'completed':
      case 'مكتمل':
        return isDark ? Colors.green.shade800 : Colors.green.shade50;
      case 'جاري':
      case 'pending':
        return isDark ? Colors.orange.shade800 : Colors.orange.shade50;
      case 'مرفوض':
      case 'cancelled':
      case 'ملغي':
        return isDark ? Colors.red.shade800 : Colors.red.shade50;
      case 'معلق':
        return isDark ? Colors.blue.shade800 : Colors.blue.shade50;
      default:
        return isDark ? Colors.grey.shade700 : Colors.grey.shade50;
    }
  }

  // لون أيقونة زر التوسيع - أكثر وضوحاً في الوضع الغامق
  Color _getExpandIconColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    switch (widget.transaction.status) {
      case 'تم':
      case 'completed':
      case 'مكتمل':
        return isDark ? Colors.green.shade200 : Colors.green.shade600;
      case 'جاري':
      case 'pending':
        return isDark ? Colors.orange.shade200 : Colors.orange.shade600;
      case 'مرفوض':
      case 'cancelled':
      case 'ملغي':
        return isDark ? Colors.red.shade200 : Colors.red.shade600;
      case 'معلق':
        return isDark ? Colors.blue.shade200 : Colors.blue.shade600;
      default:
        return isDark ? Colors.grey.shade100 : Colors.grey.shade600;
    }
  }

  Color _getShadowColor() {
    switch (widget.transaction.status) {
      case 'تم':
      case 'completed':
      case 'مكتمل':
        return Colors.green.withValues(alpha: 0.1);
      case 'جاري':
      case 'pending':
        return Colors.orange.withValues(alpha: 0.1);
      case 'مرفوض':
      case 'cancelled':
      case 'ملغي':
        return Colors.red.withValues(alpha: 0.1);
      case 'معلق':
        return Colors.blue.withValues(alpha: 0.1);
      default:
        return Colors.grey.withValues(alpha: 0.1);
    }
  }

  String _getTransactionTitle() {
    if (widget.transaction.type == 'recharge') {
      // معاملة شحن - عرض كلمة "شحن" قبل اسم الشبكة
      final network = widget.transaction.network ?? 'رصيد';
      return 'شحن $network';
    } else {
      // معاملة توريد - عرض كلمة "توريد" قبل اسم الشركة
      return 'توريد ${widget.transaction.deliveryCompany}';
    }
  }

  // دالة لعرض المبلغ في الجزء العلوي
  String _getDisplayAmount() {
    if (widget.transaction.type == 'recharge') {
      // للشحن: عرض قيمة الشحن
      return CurrencyFormatter.formatAmountWithCurrency(widget.transaction.amount);
    } else {
      // للتوريد: عرض المبلغ المحول (totalAmount)
      return CurrencyFormatter.formatAmountWithCurrency(widget.transaction.totalAmount);
    }
  }

  // دالة لحساب المبلغ المورد الفعلي (للتوريد فقط)
  String _getActualSuppliedAmount() {
    final amount = widget.transaction.amount; // المبلغ الفعلي المورد (بعد خصم العمولة)
    return CurrencyFormatter.formatAmount(amount);
  }

  // دالة للحصول على اسم وسيلة الدفع بالعربية (للبطاقة)
  String _getPaymentMethodDisplayName() {
    final walletType = widget.transaction.walletType?.toLowerCase() ?? '';

    switch (walletType) {
      case 'vodafone cash':
        return 'فودافون كاش';
      case 'etisalat cash':
        return 'اتصالات كاش';
      case 'orange cash':
        return 'اورنچ كاش';
      case 'cib smart wallet':
        return 'محفظة CIB الذكية';
      case 'instapay':
        return 'إنستاباي';
      default:
        return widget.transaction.walletType ?? 'غير محدد';
    }
  }

  // دالة للحصول على اسم وسيلة الدفع بالعربية (للرسائل)
  String _getPaymentMethodForMessage() {
    return _getPaymentMethodDisplayName();
  }

  // دالة للحصول على اسم وسيلة الدفع للشحن
  String _getPaymentMethodForRecharge(String? paymentMethod) {
    final type = paymentMethod?.toLowerCase() ?? '';

    switch (type) {
      case 'vodafone cash':
        return 'فودافون كاش';
      case 'etisalat cash':
        return 'اتصالات كاش';
      case 'orange cash':
        return 'اورنچ كاش';
      case 'cib smart wallet':
        return 'محفظة CIB الذكية';
      case 'instapay':
        return 'إنستاباي';
      default:
        return paymentMethod ?? 'غير محدد';
    }
  }

  IconData _getWalletIcon() {
    if (widget.transaction.type == 'recharge') {
      // أيقونة موحدة لشركات الاتصالات
      return Icons.business;
    } else {
      // أيقونة موحدة لشركات التوصيل
      return Icons.local_shipping;
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.transaction.status == 'جاري' || widget.transaction.status == 'pending') {
      _initializeCountdown();
    }
  }

  // تهيئة العداد مع التحقق من الحالة المحفوظة
  void _initializeCountdown() async {
    final prefs = await SharedPreferences.getInstance();
    final finishedKey = 'countdown_finished_${widget.transaction.id}';
    final isFinished = prefs.getBool(finishedKey) ?? false;

    if (isFinished) {
      // العداد انتهى مسبقاً، اجعل الحالة "تواصل معنا"
      setState(() {
        _remainingTime = Duration.zero;
      });
      return;
    }

    // بدء العداد العادي
    _startCountdown();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  // فتح واتساب مع تفاصيل المعاملة
  Future<void> _contactSupport() async {
    final transaction = widget.transaction;
    final formattedDate = DateFormat('dd/MM/yyyy - hh:mm a', 'ar').format(transaction.date);

    // تحديد نص العمولة حسب القيمة (مثل رسالة البوت)
    final commissionText = transaction.commission == 0
        ? '💰• العمولة: ${CurrencyFormatter.formatAmountWithCurrency(transaction.commission)} (ملغاة بالإعلان ✅)'
        : '💰• العمولة: ${CurrencyFormatter.formatAmountWithCurrency(transaction.commission)}';

    // ترجمة حالة المعاملة للعربية
    String getStatusInArabic(String status) {
      switch (status.toLowerCase()) {
        case 'pending':
          return 'جاري المراجعة';
        case 'completed':
          return 'مكتملة';
        case 'cancelled':
          return 'ملغاة';
        case 'failed':
          return 'فشلت';
        default:
          return status;
      }
    }

    final arabicStatus = getStatusInArabic(transaction.status);
    String message;

    if (transaction.type == 'recharge') {
      // رسالة معاملة الشحن
      final isInstapay = transaction.paymentMethod?.toLowerCase() == 'instapay';
      final senderFieldLabel = isInstapay ? '👤 اسم صاحب حساب إنستاباي' : '📱 رقم المحفظة المرسلة';

      message = '''مرحباً، أحتاج مساعدة في هذه المعاملة:

📱 *طلب مساعدة في معاملة شحن*

🆔 *رقم المعاملة:* ${transaction.id}

━━━━━━━━━━━━━━━━


📱 *بيانات الشحن:*
🌐 الشبكة: ${transaction.network ?? 'غير محدد'}
📞 الرقم المراد شحنه: ${transaction.phoneNumber ?? 'غير محدد'}
📱 نوع الشحن: ${transaction.rechargeType ?? 'غير محدد'}
💰 قيمة الشحن: ${CurrencyFormatter.formatAmountWithCurrency(transaction.amount)}

━━━━━━━━━━━━━━━━

💳 *بيانات الدفع:*
💳 وسيلة الدفع: ${_getPaymentMethodForRecharge(transaction.paymentMethod)}
$senderFieldLabel: ${transaction.senderWallet ?? 'غير محدد'}
$commissionText
💸 إجمالي المبلغ: ${CurrencyFormatter.formatAmountWithCurrency(transaction.totalAmount)}

━━━━━━━━━━━━━━━━

📅 *الوقت:* $formattedDate
⏳ *الحالة:* $arabicStatus

━━━━━━━━━━━━━━━━

يرجى مساعدتي في هذه العملية.

شكراً لكم.''';
    } else {
      // رسالة معاملة التوريد
      final isInstapay = transaction.walletType.toLowerCase() == 'instapay';
      final senderFieldLabel = isInstapay ? '👤 اسم صاحب حساب إنستاباي' : '📱 رقم المحفظة المرسلة';

      message = '''مرحباً، أحتاج مساعدة في هذه المعاملة:

💰 *طلب مساعدة في معاملة توريد*

🆔 *رقم المعاملة:* ${transaction.id}

━━━━━━━━━━━━━━━━


📋 *بيانات التوريد:*
🏪 رقم حساب طلبات: ${transaction.talabatAccountNumber ?? 'غير محدد'}
💳 وسيلة الدفع: ${_getPaymentMethodForMessage()}
$senderFieldLabel: ${transaction.senderWalletNumber}

━━━━━━━━━━━━━━━━

💰 *ملخص المبالغ:*
💵 المبلغ المراد توريده: ${CurrencyFormatter.formatAmountWithCurrency(transaction.totalAmount)}
$commissionText
💸 الإجمالي: ${CurrencyFormatter.formatAmountWithCurrency(transaction.amount)}

━━━━━━━━━━━━━━━━

📅 *الوقت:* $formattedDate
⏳ *الحالة:* $arabicStatus

━━━━━━━━━━━━━━━━

يرجى مساعدتي في هذه العملية.

شكراً لكم.''';
    }

    const phoneNumber = '+************';
    final whatsappUrl = 'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}';

    try {
      final Uri uri = Uri.parse(whatsappUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      print('خطأ في فتح واتساب: $e');
    }
  }

  void _startCountdown() async {
    final prefs = await SharedPreferences.getInstance();
    final countdownKey = 'countdown_start_${widget.transaction.id}';
    final finishedKey = 'countdown_finished_${widget.transaction.id}';
    final now = DateTime.now();

    // التحقق من انتهاء العداد مسبقاً
    final isFinished = prefs.getBool(finishedKey) ?? false;
    if (isFinished) {
      if (mounted) {
        setState(() {
          _remainingTime = Duration.zero;
        });
      }
      return; // لا تبدأ العداد إذا انتهى مسبقاً
    }

    // التحقق من وجود وقت بداية محفوظ لهذه المعاملة
    final savedStartTime = prefs.getString(countdownKey);
    DateTime countdownStartTime;

    if (savedStartTime != null) {
      // استخدام الوقت المحفوظ
      countdownStartTime = DateTime.parse(savedStartTime);
    } else {
      // حفظ وقت البداية الحالي لهذه المعاملة
      countdownStartTime = now;
      await prefs.setString(countdownKey, countdownStartTime.toIso8601String());
    }

    // حساب الوقت المنقضي منذ بداية العداد
    final timePassed = now.difference(countdownStartTime);
    final countdownDuration = const Duration(minutes: 5);

    // حساب الوقت المتبقي في العداد
    if (timePassed < countdownDuration) {
      _remainingTime = countdownDuration - timePassed;
    } else {
      _remainingTime = Duration.zero;
      // حفظ حالة انتهاء العداد بشكل دائم
      await prefs.setBool(finishedKey, true);
      await prefs.remove(countdownKey);
      return;
    }

    // بدء العداد التنازلي
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_remainingTime.inSeconds > 0) {
            _remainingTime = Duration(seconds: _remainingTime.inSeconds - 1);
          } else {
            timer.cancel();
            // حفظ حالة انتهاء العداد بشكل دائم
            _markCountdownAsFinished();
          }
        });
      }
    });
  }

  // حفظ حالة انتهاء العداد بشكل دائم
  void _markCountdownAsFinished() async {
    final prefs = await SharedPreferences.getInstance();
    final countdownKey = 'countdown_start_${widget.transaction.id}';
    final finishedKey = 'countdown_finished_${widget.transaction.id}';

    // حفظ حالة الانتهاء وحذف وقت البداية
    await prefs.setBool(finishedKey, true);
    await prefs.remove(countdownKey);
  }

  String _getStatusText() {
    // إذا لم تكن المعاملة في حالة انتظار، أعرض الحالة كما هي
    if (widget.transaction.status != 'جاري' && widget.transaction.status != 'pending') {
      return _getLocalizedStatus(widget.transaction.status);
    }

    // إذا انتهى العداد، أعرض "تواصل معنا"
    if (_remainingTime.inSeconds <= 0) {
      return 'تواصل معنا';
    }

    // أعرض العداد التنازلي
    final minutes = _remainingTime.inMinutes;
    final seconds = _remainingTime.inSeconds % 60;
    return 'جاري المراجعة ${minutes}:${seconds.toString().padLeft(2, '0')}';
  }

  String _getLocalizedStatus(String status) {
    switch (status) {
      case 'completed':
        return 'تم';
      case 'pending':
        return 'جاري';
      case 'cancelled':
        return 'مرفوض';
      default:
        return status;
    }
  }

  // إنشاء معرف مختصر للمعاملة للمرجعية
  String _getShortTransactionId() {
    final id = widget.transaction.id;

    // إذا كان المعرف رقمي، نأخذ آخر 6 أرقام
    if (RegExp(r'^\d+$').hasMatch(id)) {
      if (id.length <= 6) {
        return '#$id';
      }
      return '#${id.substring(id.length - 6)}';
    }

    // إذا كان المعرف نصي (UUID أو غيره)، نأخذ أول 8 أحرف
    if (id.length <= 8) {
      return '#${id.toUpperCase()}';
    }
    return '#${id.substring(0, 8).toUpperCase()}';
  }

  // نسخ معرف المعاملة إلى الحافظة
  Future<void> _copyTransactionId() async {
    try {
      await Clipboard.setData(ClipboardData(text: widget.transaction.id));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'تم نسخ معرف المعاملة: ${_getShortTransactionId()}',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في نسخ معرف المعاملة'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('dd/MM/yyyy - hh:mm a');
    final formattedDate = dateFormat.format(widget.transaction.date);

    return Container(
      margin: const EdgeInsets.only(bottom: 32),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            decoration: BoxDecoration(
              color: _getBackgroundColor(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: _getBorderColor(context), width: 2),
              boxShadow: [
                BoxShadow(
                  color: _getShadowColor(),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: _getStatusColor().withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          _getWalletIcon(),
                          color: _getStatusColor(),
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getTransactionTitle(),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).textTheme.titleLarge?.color,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              formattedDate,
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context).textTheme.bodySmall?.color,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            _getDisplayAmount(),
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).textTheme.titleLarge?.color,
                            ),
                          ),
                          const SizedBox(height: 4),
                          GestureDetector(
                            onTap: _getStatusText() == 'تواصل معنا' ? _contactSupport : null,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: _getStatusColor().withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: _getStatusColor().withValues(alpha: 0.3)),
                              ),
                              child: Text(
                                _getStatusText(),
                                style: TextStyle(
                                  color: _getStatusColor(),
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // الجزء القابل للتوسيع (التفاصيل)
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  height: _isExpanded ? null : 0,
                  child: _isExpanded
                      ? Container(
                          width: double.infinity,
                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                          child: Column(
                            children: [
                              const Divider(height: 1),
                              const SizedBox(height: 16),
                              ..._buildTransactionDetails(),
                            ],
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ),

          // زر دائري في منتصف الحافة السفلية
          Positioned(
            bottom: -20,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getExpandButtonBackgroundColor(context),
                  shape: BoxShape.circle,
                  border: Border.all(color: _getBorderColor(context), width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: _getShadowColor(),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  icon: Icon(
                    _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: _getExpandIconColor(context),
                    size: 18,
                  ),
                  padding: EdgeInsets.zero,
                  tooltip: _isExpanded ? 'إخفاء التفاصيل' : 'عرض التفاصيل',
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildTransactionDetails() {
    List<Widget> details = [];

    if (widget.transaction.type == 'recharge') {
      // تفاصيل معاملة الشحن
      details.addAll([
        _buildDetailRow('رقم الهاتف:', widget.transaction.phoneNumber ?? 'غير محدد'),
        const SizedBox(height: 8),
        _buildDetailRow('الشبكة:', widget.transaction.network ?? 'غير محدد'),
        const SizedBox(height: 8),
        _buildDetailRow('نوع الشحن:', widget.transaction.rechargeType ?? 'غير محدد'),
        const SizedBox(height: 8),
        _buildDetailRow('قيمة الشحن:', CurrencyFormatter.formatAmountWithCurrency(widget.transaction.amount)),
        const SizedBox(height: 8),
        _buildDetailRow('طريقة الدفع:', _getPaymentMethodForRecharge(widget.transaction.paymentMethod)),
        const SizedBox(height: 8),
        // التمييز بين إنستاباي والمحافظ الأخرى في الشحن
        if (widget.transaction.paymentMethod?.toLowerCase() == 'instapay') ...[
          _buildDetailRow('اسم صاحب حساب إنستاباي:', widget.transaction.senderWallet ?? 'غير محدد'),
        ] else ...[
          _buildDetailRow('رقم المحفظة المُرسلة:', widget.transaction.senderWallet ?? 'غير محدد'),
        ],
        const SizedBox(height: 8),
      ]);
    } else {
      // تفاصيل معاملة التوريد
      details.addAll([
        _buildTransactionIdRow(),
        const SizedBox(height: 8),
        _buildDetailRow('رقم حساب طلبات:', widget.transaction.talabatAccountNumber ?? 'غير محدد'),
        const SizedBox(height: 8),
        // إضافة وسيلة الدفع
        _buildDetailRow('وسيلة الدفع:', _getPaymentMethodDisplayName()),
        const SizedBox(height: 8),
        // إضافة حقل اسم صاحب حساب إنستاباي إذا كانت المعاملة من نوع إنستاباي
        if (widget.transaction.walletType.toLowerCase() == 'instapay') ...[
          _buildDetailRow('اسم صاحب حساب إنستاباي:', widget.transaction.senderWalletNumber),
          const SizedBox(height: 8),
        ] else ...[
          _buildDetailRow('رقم المحفظة المُرسلة:', widget.transaction.senderWalletNumber),
          const SizedBox(height: 8),
        ],
        // إضافة المبلغ المحول قبل العمولة
        _buildDetailRow('المبلغ المحول:', '${widget.transaction.totalAmount == widget.transaction.totalAmount.roundToDouble() ? widget.transaction.totalAmount.toInt() : widget.transaction.totalAmount.toStringAsFixed(2)} جنيه'),
        const SizedBox(height: 8),
      ]);
    }

    // تفاصيل مشتركة
    details.addAll([
      _buildDetailRow('العمولة:', CurrencyFormatter.formatAmountWithCurrency(widget.transaction.commission)),
      const SizedBox(height: 8),
      // إضافة معلومات الإعلان إذا تم توفير العمولة
      if (widget.transaction.hasWatchedAd == true && widget.transaction.commission == 0) ...[
        _buildAdInfoRow('💸 تم توفير العمولة بمشاهدة الإعلان'),
        const SizedBox(height: 8),
      ],
      // إضافة المبلغ المورد الفعلي (للتوريد) أو الإجمالي (للشحن)
      if (widget.transaction.type != 'recharge') ...[
        _buildDetailRow('المبلغ المورد الفعلي:', '${_getActualSuppliedAmount()} جنيه'),
      ] else ...[
        _buildDetailRow('إجمالي المبلغ:', CurrencyFormatter.formatAmountWithCurrency(widget.transaction.totalAmount)),
      ],
    ]);

    return details;
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
        ),
      ],
    );
  }

  // بناء صف معرف المعاملة مع إمكانية النسخ
  Widget _buildTransactionIdRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'معرف المعاملة:',
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
        ),
        GestureDetector(
          onTap: () => _copyTransactionId(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.blue.shade800.withValues(alpha: 0.3)
                  : Colors.blue.shade50,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.blue.shade600
                    : Colors.blue.shade200,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getShortTransactionId(),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.blue.shade300
                        : Colors.blue.shade700,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.copy,
                  size: 14,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.blue.shade300
                      : Colors.blue.shade700,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // بناء صف معلومات الإعلان بتصميم مميز
  Widget _buildAdInfoRow(String text) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.green.shade800.withValues(alpha: 0.3)
            : Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.green.shade600
              : Colors.green.shade200,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            size: 16,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.green.shade300
                : Colors.green.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.green.shade300
                    : Colors.green.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
