import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wardlytec_app/services/connectivity_service.dart';
import 'package:wardlytec_app/utils/app_theme.dart';

/// Dialog يظهر عند عدم وجود اتصال بالإنترنت
class NoInternetDialog extends StatefulWidget {
  final VoidCallback? onRetry;
  final String? title;
  final String? message;

  const NoInternetDialog({
    Key? key,
    this.onRetry,
    this.title,
    this.message,
  }) : super(key: key);

  @override
  State<NoInternetDialog> createState() => _NoInternetDialogState();
}

class _NoInternetDialogState extends State<NoInternetDialog>
    with TickerProviderStateMixin {
  final ConnectivityService _connectivityService = ConnectivityService();
  bool _isRetrying = false;
  late AnimationController _animationController;
  Timer? _connectionCheckTimer;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    // بدء مراقبة الاتصال تلقائياً كل 3 ثوانٍ
    _startConnectionMonitoring();
  }

  @override
  void dispose() {
    _connectionCheckTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  // بدء مراقبة الاتصال تلقائياً
  void _startConnectionMonitoring() {
    _connectionCheckTimer = Timer.periodic(const Duration(seconds: 3), (timer) async {
      if (!mounted) {
        timer.cancel();
        return;
      }

      try {
        final bool isConnected = await _connectivityService.checkConnection();

        if (isConnected && mounted) {
          // تم استعادة الاتصال - إغلاق البطاقة تلقائياً
          timer.cancel();
          Navigator.of(context).pop(true);

          // تنفيذ callback إذا كان موجود
          if (widget.onRetry != null) {
            widget.onRetry!();
          }
        }
      } catch (e) {
        // في حالة الخطأ، استمر في المراقبة
        print('خطأ في مراقبة الاتصال: $e');
      }
    });
  }

  Future<void> _handleRetry() async {
    if (_isRetrying) return;

    setState(() {
      _isRetrying = true;
    });

    try {
      // فحص الاتصال
      final bool isConnected = await _connectivityService.checkConnection();

      if (isConnected) {
        // إغلاق الـ dialog
        if (mounted) {
          Navigator.of(context).pop(true);
          
          // تنفيذ callback إذا كان موجود
          if (widget.onRetry != null) {
            widget.onRetry!();
          }
        }
      } else {
        // إظهار رسالة أن الاتصال ما زال غير متوفر
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يزال الاتصال بالإنترنت غير متوفر'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      print('خطأ في إعادة المحاولة: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isRetrying = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.scale(
                scale: 0.8 + (0.2 * _animationController.value),
                child: Icon(
                  Icons.wifi_off,
                  color: Colors.red.shade400,
                  size: 28,
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.title ?? 'لا يوجد اتصال بالإنترنت',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.message ?? 
            'يتطلب هذا الإجراء اتصال بالإنترنت.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 8),
          Text(
            'تأكد من تفعيل الواي فاي أو بيانات الجوال',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
        ],
      ),
      actions: [
        // زر إعادة المحاولة في المنتصف
        Center(
          child: ElevatedButton.icon(
            onPressed: _isRetrying ? null : _handleRetry,
            icon: _isRetrying
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.refresh),
            label: Text(_isRetrying ? 'جاري...' : 'إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ),
      ],
    );
  }
}

/// دالة مساعدة لإظهار dialog عدم الاتصال
Future<bool?> showNoInternetDialog(
  BuildContext context, {
  VoidCallback? onRetry,
  String? title,
  String? message,
}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => NoInternetDialog(
      onRetry: onRetry,
      title: title,
      message: message,
    ),
  );
}
