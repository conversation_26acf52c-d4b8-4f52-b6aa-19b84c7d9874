import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/transaction_provider.dart';
import '../providers/supabase_auth_provider.dart';
import '../widgets/expandable_transaction_card.dart';
import '../widgets/connectivity_wrapper.dart';
import '../utils/app_theme.dart';
import '../services/connectivity_service.dart';


class TransactionHistoryScreen extends StatefulWidget {
  final int? initialTabIndex; // التبويب المحدد مسبقاً

  const TransactionHistoryScreen({
    super.key,
    this.initialTabIndex,
  });

  @override
  State<TransactionHistoryScreen> createState() => _TransactionHistoryScreenState();
}

class _TransactionHistoryScreenState extends State<TransactionHistoryScreen>
    with SingleTickerProviderStateMixin {
  bool _isInitialized = false;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    // تحديد التبويب الافتراضي
    final initialIndex = widget.initialTabIndex ?? 0;
    _tabController = TabController(
      length: 3,
      vsync: this,
      initialIndex: initialIndex.clamp(0, 2), // التأكد من أن الفهرس صحيح
    );
    _tabController.addListener(_onTabChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTransactions();
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _isInitialized = false;
      });
      _loadTransactions();
    }
  }

  Future<void> _loadTransactions() async {
    if (!mounted) return;

    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
    final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

    print('🔄 تحميل المعاملات - المستخدم: ${authProvider.currentUser?.id}');

    if (authProvider.currentUser != null) {
      await transactionProvider.fetchUserTransactions(authProvider.currentUser!.id.toString());
      print('✅ تم تحميل ${transactionProvider.transactions.length} معاملة');
    } else {
      print('❌ لا يوجد مستخدم مسجل دخول');
    }

    if (mounted) {
      setState(() {
        _isInitialized = true;
      });
    }
  }

  // فلترة المعاملات حسب النوع المختار
  List<dynamic> _getFilteredTransactions(List<dynamic> allTransactions) {
    switch (_tabController.index) {
      case 0: // توريد
        return allTransactions.where((transaction) =>
          transaction.type == 'transaction' ||
          transaction.type == null // للتوافق مع البيانات القديمة
        ).toList();
      case 1: // شحن
        return allTransactions.where((transaction) =>
          transaction.type == 'recharge'
        ).toList();
      case 2: // الكل
      default:
        return allTransactions;
    }
  }



  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      showNoInternetCard: true, // تظهر بطاقة عدم الاتصال
      showQualityIndicator: false, // إخفاء مؤشر جودة الاتصال
      requiredQuality: ActionType.normalBrowsing, // تتطلب تصفح عادي
      child: Scaffold(
      appBar: AppBar(
        title: const Text(
          'سجل المعاملات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48.0),
          child: Container(
            // خلفية برتقالية موحدة مع الـ AppBar
            decoration: BoxDecoration(
              color: AppTheme.primaryColor, // برتقالي موحد في جميع الأوضاع
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(0, 2),
                  blurRadius: 4,
                ),
              ],
            ),
            child: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(
                  icon: Icon(Icons.monetization_on),
                  text: 'توريد',
                ),
                Tab(
                  icon: Icon(Icons.phone_android),
                  text: 'شحن',
                ),
                Tab(
                  icon: Icon(Icons.list_alt),
                  text: 'الكل',
                ),
              ],
              // ألوان محسنة للخلفية الجديدة
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              indicatorWeight: 3,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 13,
              ),
              // إزالة الخلفية الافتراضية للـ TabBar
              indicator: const UnderlineTabIndicator(
                borderSide: BorderSide(color: Colors.white, width: 3),
              ),
            ),
          ),
        ),
      ),
      body: Consumer<TransactionProvider>(
        builder: (context, transactionProvider, child) {
          // إظهار التحميل فقط إذا لم يتم التهيئة بعد
          if (!_isInitialized && transactionProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (transactionProvider.error != null && _isInitialized) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل المعاملات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.headlineSmall?.color,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    transactionProvider.error!,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _isInitialized = false;
                      });
                      _loadTransactions();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة المحاولة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          final allTransactions = transactionProvider.transactions;
          final transactions = _getFilteredTransactions(allTransactions);

          if (transactions.isEmpty && _isInitialized) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    size: 64,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد معاملات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.headlineSmall?.color,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'لم تقم بأي معاملات حتى الآن',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _isInitialized = false;
                      });
                      _loadTransactions();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('تحديث'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          return Stack(
            children: [
              RefreshIndicator(
                onRefresh: () async {
                  setState(() {
                    _isInitialized = false;
                  });
                  await _loadTransactions();
                },
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: transactions.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: ExpandableTransactionCard(
                        transaction: transactions[index],
                      ),
                    );
                  },
                ),
              ),
              // مؤشر تحميل أثناء التحديث
              if (transactionProvider.isLoading && _isInitialized)
                Positioned(
                  top: 16,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppTheme.primaryColor,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'جاري التحديث...',
                            style: TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    ),
    );
  }
}
