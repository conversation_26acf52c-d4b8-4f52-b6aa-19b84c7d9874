import 'package:shared_preferences/shared_preferences.dart';

/// خدمة تنظيف البيانات المؤقتة المنتهية الصلاحية
class TempDataCleanupService {
  
  /// تنظيف البيانات المؤقتة المنتهية الصلاحية
  /// يتم استدعاؤها عند بدء التطبيق
  static Future<void> cleanupExpiredTempData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryTime = prefs.getInt('temp_data_expiry');
      
      // التحقق من وجود بيانات مؤقتة
      if (expiryTime != null) {
        final currentTime = DateTime.now().millisecondsSinceEpoch;
        
        // إذا انتهت الصلاحية، قم بالتنظيف
        if (currentTime > expiryTime) {
          await _cleanupAllTempData(prefs);
          print('🧹 تم تنظيف البيانات المؤقتة المنتهية الصلاحية');
        } else {
          // حساب الوقت المتبقي
          final remainingTime = Duration(milliseconds: expiryTime - currentTime);
          print('⏰ البيانات المؤقتة ستنتهي صلاحيتها خلال: ${remainingTime.inMinutes} دقيقة و ${remainingTime.inSeconds % 60} ثانية');
        }
      }
    } catch (e) {
      print('❌ خطأ في تنظيف البيانات المؤقتة: $e');
    }
  }
  
  /// تنظيف جميع البيانات المؤقتة
  static Future<void> _cleanupAllTempData(SharedPreferences prefs) async {
    await prefs.remove('temp_customer_name');
    await prefs.remove('temp_phone_number');
    await prefs.remove('temp_secret_code');
    await prefs.remove('temp_data_expiry');
  }
  
  /// التحقق من صلاحية البيانات المؤقتة
  static Future<bool> isTempDataValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryTime = prefs.getInt('temp_data_expiry');
      
      if (expiryTime == null) return false;
      
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      return currentTime <= expiryTime;
    } catch (e) {
      return false;
    }
  }
  
  /// الحصول على الوقت المتبقي للبيانات المؤقتة
  static Future<Duration?> getRemainingTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryTime = prefs.getInt('temp_data_expiry');
      
      if (expiryTime == null) return null;
      
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      if (currentTime > expiryTime) return null;
      
      return Duration(milliseconds: expiryTime - currentTime);
    } catch (e) {
      return null;
    }
  }
  
  /// تمديد صلاحية البيانات المؤقتة (إضافية - إذا احتجتها)
  static Future<bool> extendTempDataExpiry({Duration extension = const Duration(minutes: 5)}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentExpiryTime = prefs.getInt('temp_data_expiry');
      
      if (currentExpiryTime == null) return false;
      
      final newExpiryTime = DateTime.now().add(extension).millisecondsSinceEpoch;
      await prefs.setInt('temp_data_expiry', newExpiryTime);
      
      print('⏰ تم تمديد صلاحية البيانات المؤقتة لـ ${extension.inMinutes} دقيقة إضافية');
      return true;
    } catch (e) {
      print('❌ خطأ في تمديد صلاحية البيانات المؤقتة: $e');
      return false;
    }
  }
  
  /// حذف البيانات المؤقتة فوراً (للاستخدام عند الحاجة)
  static Future<void> forceCleanupTempData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await _cleanupAllTempData(prefs);
      print('🧹 تم حذف البيانات المؤقتة فوراً');
    } catch (e) {
      print('❌ خطأ في حذف البيانات المؤقتة: $e');
    }
  }
  
  /// الحصول على معلومات البيانات المؤقتة (للتشخيص)
  static Future<Map<String, dynamic>> getTempDataInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryTime = prefs.getInt('temp_data_expiry');
      final customerName = prefs.getString('temp_customer_name');
      final phoneNumber = prefs.getString('temp_phone_number');
      final hasSecretCode = prefs.getString('temp_secret_code') != null;
      
      if (expiryTime == null) {
        return {'exists': false};
      }
      
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final isValid = currentTime <= expiryTime;
      final remainingTime = isValid ? Duration(milliseconds: expiryTime - currentTime) : null;
      
      return {
        'exists': true,
        'isValid': isValid,
        'customerName': customerName,
        'phoneNumber': phoneNumber,
        'hasSecretCode': hasSecretCode,
        'expiryTime': DateTime.fromMillisecondsSinceEpoch(expiryTime),
        'remainingTime': remainingTime,
        'remainingMinutes': remainingTime?.inMinutes,
        'remainingSeconds': remainingTime?.inSeconds,
      };
    } catch (e) {
      return {'exists': false, 'error': e.toString()};
    }
  }
}
