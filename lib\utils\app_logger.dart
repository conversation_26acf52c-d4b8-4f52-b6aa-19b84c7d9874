import 'package:flutter/foundation.dart';

class AppLogger {
  static final List<String> _logs = [];
  static const int maxLogs = 100; // الحد الأقصى للسجلات المحفوظة

  // إضافة سجل جديد
  static void log(String message) {
    final timestamp = DateTime.now().toString().substring(0, 19);
    final logEntry = '[$timestamp] $message';
    
    // طباعة في الكونسول
    if (kDebugMode) {
      print(logEntry);
    }
    
    // حفظ في القائمة
    _logs.add(logEntry);
    
    // الحفاظ على الحد الأقصى للسجلات
    if (_logs.length > maxLogs) {
      _logs.removeAt(0);
    }
  }

  // الحصول على جميع السجلات
  static List<String> getAllLogs() {
    return List.from(_logs);
  }

  // الحصول على السجلات الأخيرة
  static List<String> getRecentLogs([int count = 20]) {
    if (_logs.length <= count) {
      return List.from(_logs);
    }
    return _logs.sublist(_logs.length - count);
  }

  // مسح جميع السجلات
  static void clearLogs() {
    _logs.clear();
  }

  // البحث في السجلات
  static List<String> searchLogs(String query) {
    return _logs.where((log) => log.toLowerCase().contains(query.toLowerCase())).toList();
  }

  // الحصول على سجلات البوتات فقط
  static List<String> getBotLogs() {
    return _logs.where((log) => 
      log.contains('🔔') || 
      log.contains('✅') || 
      log.contains('❌') || 
      log.contains('🔄') ||
      log.contains('بوت') ||
      log.contains('إشعار')
    ).toList();
  }

  // سجلات خاصة بالبوتات
  static void logBot(String message) {
    log('🤖 $message');
  }

  static void logSuccess(String message) {
    log('✅ $message');
  }

  static void logError(String message) {
    log('❌ $message');
  }

  static void logRetry(String message) {
    log('🔄 $message');
  }

  static void logInfo(String message) {
    log('ℹ️ $message');
  }
}
