# ⚡ إعداد سريع - نظام الإشعارات المحلية

## 🚀 خطوات التثبيت السريع

### 1️⃣ **تثبيت التبعيات**
```bash
flutter pub get
```

### 2️⃣ **إعداد قاعدة البيانات**
```sql
-- في Supabase SQL Editor، شغل:
database_setup/create_notifications_tables.sql
```

### 3️⃣ **اختبار النظام**
```
1. شغل التطبيق
2. سجل دخول
3. اذهب للقائمة الجانبية → إعدادات الإشعارات
4. اضغط "إرسال إشعار تجريبي"
```

---

## 🔔 كيفية عمل النظام

### **للمستخدمين:**
- 📱 **إشعارات تلقائية** عند تغيير حالة المعاملات
- ⚙️ **تحكم كامل** في أنواع الإشعارات المرغوبة
- 🧪 **اختبار الإشعارات** للتأكد من عمل النظام

### **للمطورين:**
- 🔄 **مراقبة فورية** لتغييرات قاعدة البيانات
- 📊 **سجل شامل** للإشعارات المرسلة
- 🛡️ **أمان متقدم** مع Row Level Security

---

## 📋 أنواع الإشعارات

| النوع | الوصف | افتراضي |
|-------|--------|---------|
| **تحديثات المعاملات** | تغيير حالة التوريد والشحن | ✅ مفعل |
| **تحديثات الخدمات** | تعطيل/تفعيل الخدمات | ✅ مفعل |
| **إشعارات الصيانة** | صيانة مجدولة وتحديثات | ✅ مفعل |
| **العروض والترويج** | عروض خاصة وخصومات | ❌ معطل |

---

## 🛠️ استكشاف الأخطاء السريع

### **الإشعارات لا تظهر؟**
1. تحقق من أذونات الإشعارات في الجهاز
2. اختبر بـ "إرسال إشعار تجريبي"
3. تأكد من تشغيل ملف SQL

### **المراقبة لا تعمل؟**
1. تحقق من اتصال الإنترنت
2. تأكد من تسجيل الدخول
3. أعد تشغيل التطبيق

---

## 📞 الدعم

إذا واجهت مشاكل:
1. راجع `docs/notification_system_guide.md` للدليل الشامل
2. تحقق من logs التطبيق
3. تواصل مع فريق التطوير

---

**✅ النظام جاهز للاستخدام بدون Firebase!**
