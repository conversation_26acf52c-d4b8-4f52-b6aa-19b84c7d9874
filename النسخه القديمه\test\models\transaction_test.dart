import 'package:flutter_test/flutter_test.dart';
import 'package:wardlytec_app/models/transaction.dart';

void main() {
  group('Transaction Model Tests', () {
    test('Transaction should be created with all required fields', () {
      final transaction = Transaction(
        id: 'tx_123',
        userId: 'user_456',
        talabatAccountNumber: '*********',
        senderWalletNumber: '***********',
        amount: 500.0,
        commission: 2.5,
        totalAmount: 502.5,
        walletType: 'Vodafone Cash',
        date: DateTime.now(),
        status: 'جاري',
      );

      expect(transaction.id, 'tx_123');
      expect(transaction.userId, 'user_456');
      expect(transaction.talabatAccountNumber, '*********');
      expect(transaction.senderWalletNumber, '***********');
      expect(transaction.amount, 500.0);
      expect(transaction.commission, 2.5);
      expect(transaction.totalAmount, 502.5);
      expect(transaction.walletType, 'Vodafone Cash');
      expect(transaction.status, 'جاري');
    });

    test('Transaction.fromMap should parse Supabase format correctly', () {
      final testData = {
        'user_id': 'user_789',
        'talabat_account_number': '*********',
        'sender_wallet_number': '01*********',
        'amount': 1000.0,
        'commission': 5.0,
        'total_amount': 1005.0,
        'wallet_type': 'Etisalat Cash',
        'created_at': '2023-01-01T12:00:00.000Z',
        'status': 'تم',
      };

      final transaction = Transaction.fromMap(testData, 'tx_456');

      expect(transaction.id, 'tx_456');
      expect(transaction.userId, 'user_789');
      expect(transaction.talabatAccountNumber, '*********');
      expect(transaction.senderWalletNumber, '01*********');
      expect(transaction.amount, 1000.0);
      expect(transaction.commission, 5.0);
      expect(transaction.totalAmount, 1005.0);
      expect(transaction.walletType, 'Etisalat Cash');
      expect(transaction.status, 'تم');
    });

    test('Transaction.fromMap should handle legacy format', () {
      final testData = {
        'userId': 'user_legacy',
        'talabatAccountNumber': '*********',
        'senderWalletNumber': '01*********',
        'amount': 750.0,
        'commission': 3.75,
        'totalAmount': 753.75,
        'walletType': 'Orange Cash',
        'date': '2023-01-01T12:00:00.000Z',
        'status': 'مرفوض',
      };

      final transaction = Transaction.fromMap(testData, 'tx_legacy');

      expect(transaction.userId, 'user_legacy');
      expect(transaction.talabatAccountNumber, '*********');
      expect(transaction.walletType, 'Orange Cash');
      expect(transaction.status, 'مرفوض');
    });

    test('Transaction.fromMap should handle missing fields with defaults', () {
      final testData = {
        'user_id': 'user_minimal',
      };

      final transaction = Transaction.fromMap(testData, 'tx_minimal');

      expect(transaction.id, 'tx_minimal');
      expect(transaction.userId, 'user_minimal');
      expect(transaction.talabatAccountNumber, '');
      expect(transaction.senderWalletNumber, '');
      expect(transaction.amount, 0.0);
      expect(transaction.commission, 0.0);
      expect(transaction.totalAmount, 0.0);
      expect(transaction.walletType, '');
      expect(transaction.status, 'جاري');
    });

    test('Transaction.toMap should convert to Supabase format', () {
      final transaction = Transaction(
        id: 'tx_test',
        userId: 'user_test',
        talabatAccountNumber: '*********',
        senderWalletNumber: '01*********',
        amount: 300.0,
        commission: 1.5,
        totalAmount: 301.5,
        walletType: ' ',
        date: DateTime.parse('2023-01-01T12:00:00.000Z'),
        status: 'جاري',
      );

      final map = transaction.toMap();

      expect(map['user_id'], 'user_test');
      expect(map['talabat_account_number'], '*********');
      expect(map['sender_wallet_number'], '01*********');
      expect(map['amount'], 300.0);
      expect(map['commission'], 1.5);
      expect(map['total_amount'], 301.5);
      expect(map['wallet_type'], ' ');
      expect(map['status'], 'جاري');
      expect(map['created_at'], '2023-01-01T12:00:00.000Z');
    });

    test('Transaction.copyWith should create modified copy', () {
      final original = Transaction(
        id: 'tx_original',
        userId: 'user_original',
        talabatAccountNumber: '*********',
        senderWalletNumber: '***********',
        amount: 500.0,
        commission: 2.5,
        totalAmount: 502.5,
        walletType: 'Vodafone Cash',
        date: DateTime.now(),
        status: 'جاري',
      );

      final modified = original.copyWith(
        status: 'تم',
        amount: 600.0,
        totalAmount: 603.0,
      );

      expect(modified.id, original.id);
      expect(modified.userId, original.userId);
      expect(modified.status, 'تم');
      expect(modified.amount, 600.0);
      expect(modified.totalAmount, 603.0);
      expect(modified.commission, original.commission);
    });
  });
}
