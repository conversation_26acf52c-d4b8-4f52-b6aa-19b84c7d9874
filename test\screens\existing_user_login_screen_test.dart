import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/screens/auth/existing_user_login_screen.dart';

void main() {
  group('ExistingUserLoginScreen Widget Tests', () {
    late SupabaseAuthProvider mockAuthProvider;

    setUp(() {
      mockAuthProvider = SupabaseAuthProvider();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<SupabaseAuthProvider>(
          create: (_) => mockAuthProvider,
          child: const ExistingUserLoginScreen(),
        ),
      );
    }

    testWidgets('Should display contact support message', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من وجود النص الأساسي
      expect(find.text('إذا نسيت كلمة السر، '), findsOneWidget);
      
      // التحقق من وجود الرابط القابل للضغط
      expect(find.text('تواصل معنا'), findsOneWidget);
    });

    testWidgets('Contact support link should be tappable', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // العثور على رابط "تواصل معنا"
      final contactLink = find.text('تواصل معنا');
      expect(contactLink, findsOneWidget);

      // التحقق من أن الرابط قابل للضغط
      final gestureDetector = find.ancestor(
        of: contactLink,
        matching: find.byType(GestureDetector),
      );
      expect(gestureDetector, findsOneWidget);
    });

    testWidgets('Should display phone and secret code fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من وجود حقول الإدخال
      expect(find.byType(TextFormField), findsNWidgets(2));
      
      // التحقق من وجود تسميات الحقول
      expect(find.text('رقم الهاتف'), findsOneWidget);
      expect(find.text('الرقم السري'), findsOneWidget);
    });

    testWidgets('Should display login button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من وجود زر تسجيل الدخول
      expect(find.text('دخول'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('Should validate phone number input', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // العثور على حقل رقم الهاتف
      final phoneField = find.byType(TextFormField).first;
      
      // إدخال رقم غير صحيح
      await tester.enterText(phoneField, '123');
      await tester.pump();

      // النقر على زر الدخول لتفعيل التحقق
      await tester.tap(find.text('دخول'));
      await tester.pump();

      // التحقق من رسالة الخطأ (إذا كانت موجودة)
      // يمكن تحسين هذا الاختبار حسب منطق التحقق المطبق
    });

    testWidgets('Should validate secret code input', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // العثور على حقل الرقم السري
      final secretCodeField = find.byType(TextFormField).last;
      
      // إدخال رقم سري قصير
      await tester.enterText(secretCodeField, '12');
      await tester.pump();

      // النقر على زر الدخول لتفعيل التحقق
      await tester.tap(find.text('دخول'));
      await tester.pump();

      // التحقق من رسالة الخطأ (إذا كانت موجودة)
    });

    testWidgets('Contact support should work with phone number', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // إدخال رقم هاتف
      final phoneField = find.byType(TextFormField).first;
      await tester.enterText(phoneField, '01234567890');
      await tester.pump();

      // النقر على "تواصل معنا"
      await tester.tap(find.text('تواصل معنا'));
      await tester.pump();

      // في الاختبار الحقيقي، يجب أن يفتح WhatsApp
      // هنا نتحقق فقط من أن الدالة تم استدعاؤها بدون أخطاء
    });

    testWidgets('Contact support should work without phone number', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // النقر على "تواصل معنا" بدون إدخال رقم هاتف
      await tester.tap(find.text('تواصل معنا'));
      await tester.pump();

      // يجب أن تعمل الدالة حتى بدون رقم هاتف
    });

    testWidgets('Should have proper styling for contact link', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // العثور على نص "تواصل معنا"
      final contactText = tester.widget<Text>(find.text('تواصل معنا'));

      // التحقق من التنسيق
      expect(contactText.style?.fontWeight, FontWeight.bold);
      expect(contactText.style?.decoration, isNull); // لا يوجد خط تحت النص
      // يمكن إضافة المزيد من اختبارات التنسيق
    });

    testWidgets('Should display page title', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من عنوان الصفحة في AppBar
      expect(find.text('تسجيل الدخول'), findsOneWidget);
    });

    testWidgets('Should have back button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من وجود زر الرجوع
      expect(find.byType(AppBar), findsOneWidget);
      // يمكن إضافة اختبار للتأكد من وجود زر الرجوع
    });
  });
}
