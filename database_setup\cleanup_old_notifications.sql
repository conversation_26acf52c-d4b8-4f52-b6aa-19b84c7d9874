-- ========================================
-- 🧹 تنظيف شامل لنظام الإشعارات القديم
-- ========================================
-- 🚀 شغل هذا الملف أولاً إذا كان لديك نظام إشعارات قديم

-- ========================================
-- 1. حذف جميع الدوال القديمة
-- ========================================

-- حذف دوال الإشعارات القديمة بجميع التوقيعات المحتملة
DROP FUNCTION IF EXISTS cleanup_old_notifications() CASCADE;
DROP FUNCTION IF EXISTS cleanup_old_notifications(INTEGER) CASCADE;
DROP FUNCTION IF EXISTS cleanup_old_notifications(INTERVAL) CASCADE;

DROP FUNCTION IF EXISTS log_notification(UUID, VARCHAR, TEXT, TEXT, JSONB) CASCADE;
DROP FUNCTION IF EXISTS log_notification(UUID, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS log_notification(UUID, VARCHAR, TEXT, TEXT) CASCADE;

DROP FUNCTION IF EXISTS get_notification_stats(UUID) CASCADE;
DROP FUNCTION IF EXISTS get_notification_stats() CASCADE;

DROP FUNCTION IF EXISTS get_user_notification_settings(UUID) CASCADE;
DROP FUNCTION IF EXISTS update_user_notification_setting(UUID, VARCHAR, BOOLEAN) CASCADE;
DROP FUNCTION IF EXISTS create_default_notification_settings(UUID) CASCADE;

-- ========================================
-- 2. حذف جميع السياسات القديمة
-- ========================================

-- حذف سياسات الأمان القديمة
DROP POLICY IF EXISTS "Users can view their own notification settings" ON user_notification_settings;
DROP POLICY IF EXISTS "Users can update their own notification settings" ON user_notification_settings;
DROP POLICY IF EXISTS "Users can insert their own notification settings" ON user_notification_settings;

DROP POLICY IF EXISTS "Users can view their own notification logs" ON notification_logs;
DROP POLICY IF EXISTS "System can insert notification logs" ON notification_logs;

-- ========================================
-- 3. حذف جميع الفهارس القديمة
-- ========================================

-- حذف فهارس الجداول القديمة
DROP INDEX IF EXISTS idx_user_notification_settings_user_id;
DROP INDEX IF EXISTS idx_user_notification_settings_type;
DROP INDEX IF EXISTS idx_notification_logs_user_id;
DROP INDEX IF EXISTS idx_notification_logs_type;
DROP INDEX IF EXISTS idx_notification_logs_sent_at;

-- ========================================
-- 4. حذف جميع الجداول القديمة
-- ========================================

-- حذف الجداول بالترتيب الصحيح (الجداول التابعة أولاً)
DROP TABLE IF EXISTS notification_logs CASCADE;
DROP TABLE IF EXISTS user_notification_settings CASCADE;

-- ========================================
-- 5. حذف أي triggers قديمة
-- ========================================

-- حذف triggers إذا كانت موجودة
DROP TRIGGER IF EXISTS update_user_notification_settings_updated_at ON user_notification_settings;
DROP TRIGGER IF EXISTS update_notification_logs_updated_at ON notification_logs;

-- حذف دوال triggers
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- ========================================
-- 6. تنظيف أي extensions غير مستخدمة
-- ========================================

-- لا نحذف extensions لأنها قد تكون مستخدمة في أماكن أخرى
-- فقط نتأكد من وجود ما نحتاجه
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ========================================
-- 7. رسالة تأكيد التنظيف
-- ========================================

DO $$
BEGIN
    RAISE NOTICE '🧹 تم تنظيف نظام الإشعارات القديم بنجاح!';
    RAISE NOTICE '✅ تم حذف جميع الجداول والدوال والسياسات القديمة';
    RAISE NOTICE '🚀 يمكنك الآن تشغيل ملف الإعداد الجديد';
    RAISE NOTICE '';
    RAISE NOTICE 'الخطوة التالية:';
    RAISE NOTICE '📁 شغل ملف: database_setup/quick_notifications_setup.sql';
END $$;
