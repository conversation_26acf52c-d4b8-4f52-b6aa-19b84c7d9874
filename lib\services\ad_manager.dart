import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:wardlytec_app/config/app_config.dart';

class AdManager {
  static String get rewardedAdUnitId {
    return AppConfig.getRewardedAdUnitId(Platform.isAndroid);
  }

  static String get bannerAdUnitId {
    if (Platform.isAndroid) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحقاً
      return 'ca-app-pub-3940256099942544/6300978111';
    } else if (Platform.isIOS) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحقاً
      return 'ca-app-pub-3940256099942544/2934735716';
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }

  static String get interstitialAdUnitId {
    if (Platform.isAndroid) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحقاً
      return 'ca-app-pub-3940256099942544/1033173712';
    } else if (Platform.isIOS) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحقاً
      return 'ca-app-pub-3940256099942544/4411468910';
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }

  // تهيئة الإعلانات
  static Future<void> initialize() async {
    print('🔧 بدء تهيئة نظام الإعلانات...');

    // طباعة تحذير أمني
    AppConfig.printSecurityWarning();

    // تهيئة AdMob (للمستقبل)
    final initializationStatus = await MobileAds.instance.initialize();

    print('✅ تم تهيئة Google Mobile Ads بنجاح');
    print('📊 حالة التهيئة: ${initializationStatus.adapterStatuses}');

    // طباعة حالة الإعلانات
    AppConfig.printAdMobStatus();

    // إعداد AdMob للإنتاج
    print('📱 نظام الإعلانات: Google AdMob');
    print('🎯 نوع الإعلانات: Rewarded Ads');

    // طباعة معلومات الجهاز
    final requestConfiguration = RequestConfiguration(
      testDeviceIds: [], // لا نريد أجهزة اختبار
    );
    MobileAds.instance.updateRequestConfiguration(requestConfiguration);

    print('🔧 تم تحديث إعدادات الطلب');
  }

  // إنشاء إعلان مكافأة
  static Future<RewardedAd?> createRewardedAd() async {
    print('🔄 بدء تحميل الإعلان...');
    print('📱 Ad Unit ID: $rewardedAdUnitId');
    print('🧪 استخدام إعلانات اختبار: ${AppConfig.useTestAds}');

    try {
      final completer = Completer<RewardedAd?>();

      RewardedAd.load(
        adUnitId: rewardedAdUnitId,
        request: const AdRequest(),
        rewardedAdLoadCallback: RewardedAdLoadCallback(
          onAdLoaded: (RewardedAd ad) {
            print('✅ تم تحميل الإعلان بنجاح');
            completer.complete(ad);
          },
          onAdFailedToLoad: (LoadAdError error) {
            print('❌ فشل في تحميل الإعلان:');
            print('   - كود الخطأ: ${error.code}');
            print('   - رسالة الخطأ: ${error.message}');
            print('   - المجال: ${error.domain}');
            print('   - السبب المحتمل: ${error.responseInfo}');

            // تشخيص أكثر تفصيلاً
            _diagnoseAdError(error);

            completer.complete(null);
          },
        ),
      );

      // انتظار لمدة 30 ثانية كحد أقصى
      return await completer.future.timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          print('⏰ انتهت مهلة تحميل الإعلان (30 ثانية)');
          return null;
        },
      );
    } catch (e) {
      print('❌ خطأ في تحميل الإعلان: $e');
      return null;
    }
  }

  // عرض إعلان مكافأة
  static void showRewardedAd(
    RewardedAd ad, {
    required Function() onAdWatched,
    required Function() onAdFailed,
  }) {
    bool hasEarnedReward = false; // متغير لتتبع ما إذا تم الحصول على المكافأة
    bool callbackExecuted = false; // متغير لمنع تنفيذ callback أكثر من مرة

    ad.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (RewardedAd ad) {
        print('📺 بدء عرض الإعلان');
      },
      onAdDismissedFullScreenContent: (RewardedAd ad) {
        print('❌ تم إغلاق الإعلان');
        ad.dispose();

        // التحقق من ما إذا تم الحصول على المكافأة قبل الإغلاق
        if (hasEarnedReward) {
          print('✅ تم إغلاق الإعلان بعد الحصول على المكافأة');
          // تم استدعاء onAdWatched() بالفعل في onUserEarnedReward
        } else {
          print('❌ تم إغلاق الإعلان بدون الحصول على المكافأة');
          if (!callbackExecuted) {
            callbackExecuted = true;
            onAdFailed();
          }
        }
      },
      onAdFailedToShowFullScreenContent: (RewardedAd ad, AdError error) {
        print('❌ فشل في عرض الإعلان: $error');
        ad.dispose();
        if (!callbackExecuted) {
          callbackExecuted = true;
          onAdFailed();
        }
      },
    );

    ad.setImmersiveMode(true);

    ad.show(onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
      print('🎉 تم مشاهدة الإعلان بنجاح!');
      print('💰 المكافأة المستلمة: ${reward.amount} ${reward.type}');
      hasEarnedReward = true; // تعيين أنه تم الحصول على المكافأة

      // استدعاء callback النجاح فوراً عند الحصول على المكافأة
      if (!callbackExecuted) {
        callbackExecuted = true;
        onAdWatched();
      }
    });
  }

  // تشخيص أخطاء الإعلانات
  static void _diagnoseAdError(LoadAdError error) {
    print('🔍 تشخيص مفصل للخطأ:');

    switch (error.code) {
      case 0:
        print('   📡 ERROR_CODE_INTERNAL_ERROR - خطأ داخلي في AdMob');
        print('   💡 الحل: حاول مرة أخرى لاحقاً');
        break;
      case 1:
        print('   🌐 ERROR_CODE_INVALID_REQUEST - طلب غير صالح');
        print('   💡 الحل: تحقق من معرف وحدة الإعلان');
        break;
      case 2:
        print('   📶 ERROR_CODE_NETWORK_ERROR - خطأ في الشبكة');
        print('   💡 الحل: تحقق من الاتصال بالإنترنت');
        break;
      case 3:
        print('   📭 ERROR_CODE_NO_FILL - لا توجد إعلانات متاحة');
        print('   💡 الحل: هذا طبيعي للإعلانات الحقيقية الجديدة');
        print('   ⏰ انتظر 24-48 ساعة أو استخدم إعلانات اختبار');
        break;
      default:
        print('   ❓ كود خطأ غير معروف: ${error.code}');
        break;
    }

    print('   🔗 معرف وحدة الإعلان المستخدم: $rewardedAdUnitId');
    print('   🧪 وضع الاختبار: ${AppConfig.useTestAds}');
  }

  // عرض إعلان مكافأة AdMob
  static Future<bool> showRewardedAdMob(BuildContext context) async {
    print('📱 عرض إعلان AdMob...');

    // إظهار رسالة للمطور في وضع التطوير
    if (AppConfig.useTestAds) {
      print('🧪 عرض إعلان تجريبي - لا توجد أرباح حقيقية');
    }

    try {
      final ad = await createRewardedAd();
      if (ad != null) {
        final completer = Completer<bool>();
        showRewardedAd(
          ad,
          onAdWatched: () => completer.complete(true),
          onAdFailed: () => completer.complete(false),
        );
        return await completer.future;
      } else {
        print('❌ فشل في تحميل الإعلان');
        return false;
      }
    } catch (e) {
      print('خطأ في عرض الإعلان: $e');
      return false;
    }
  }

  // دالة موحدة لعرض الإعلانات
  static Future<bool> showRewardAd(BuildContext context) async {
    // استخدام إعلانات AdMob
    return await showRewardedAdMob(context);
  }

  // طباعة حالة النظام
  static void printSystemStatus() {
    print('📊 حالة نظام الإعلانات:');
    print('   📱 النوع: Google AdMob');
    print('   🎯 نوع الإعلان: Rewarded Ads');
    print('   🧪 وضع الاختبار: ${AppConfig.useTestAds ? "مفعل" : "معطل"}');
    print('   🚀 الحالة: ${AppConfig.useTestAds ? "تطوير" : "إنتاج"}');
  }
}
