import 'package:flutter/material.dart';
import 'package:wardlytec_app/models/user.dart';

/// خدمة إشعارات المكافآت البسيطة
class RewardsNotificationService {

  /// عرض إشعار تجديد المكافآت
  static void showRenewalNotification(BuildContext context, UserModel user) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.card_giftcard,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '🎉 تم تجديد مكافآتك الشهرية! حصلت على ${user.monthlySupplyRewards} مرات توريد و ${user.monthlyRechargeRewards} مرات شحن',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green[600],
        duration: const Duration(seconds: 5),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }





  /// عرض إشعار نجاح مشاهدة الإعلان (محسن مع floating)
  static void showAdWatchedSuccessNotification(BuildContext context, {
    required bool isSupplyAd,
    required int remainingViews,
  }) {
    final message = isSupplyAd
        ? 'تم إلغاء العمولة بنجاح! متبقي $remainingViews مرات للتوريد'
        : 'تم إلغاء العمولة بنجاح! متبقي $remainingViews مرات للشحن';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green[600],
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  /// عرض إشعار فشل مشاهدة الإعلان (محسن مع floating)
  static void showAdWatchFailedNotification(BuildContext context, String reason) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.refresh,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'لم يتم إكمال مشاهدة الإعلان. تم إعادة تفعيل الزر - يمكنك المحاولة مرة أخرى',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange[600],
        duration: const Duration(seconds: 5),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }



  /// فحص وعرض إشعار التجديد إذا كان المستخدم مستحق
  static void checkAndShowRenewalNotification(BuildContext context, UserModel user) {
    // تأخير بسيط لضمان تحميل الشاشة أولاً
    Future.delayed(const Duration(milliseconds: 500), () {
      if (user.isDueForRenewal) {
        showRenewalNotification(context, user);
      }
    });
  }
}
