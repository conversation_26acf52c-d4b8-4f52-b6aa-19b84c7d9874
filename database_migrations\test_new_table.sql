-- اختبار الجدول الجديد
-- Test new table
-- 🧪 شغل هذا الكود بعد create_new_users_table.sql

-- ========================================
-- 1. اختبار إضافة مستخدم جديد
-- ========================================

SELECT '👤 اختبار إضافة مستخدم جديد' as "الاختبار 1";

-- إضافة مستخدم جديد
INSERT INTO users (phone_number, name, secret_code) 
VALUES ('***********', 'أحمد محمد', '123456');

-- فحص البيانات
SELECT 
    id,
    phone_number,
    name,
    ad_views_remaining,
    recharge_ad_views_remaining,
    monthly_supply_rewards,
    monthly_recharge_rewards,
    renewal_count,
    (next_rewards_renewal > NOW()) as "له تجديد مستقبلي"
FROM users 
WHERE phone_number = '***********';

-- ========================================
-- 2. اختبار دالة فحص المستحقين للتجديد
-- ========================================

SELECT '🔍 اختبار دالة فحص المستحقين للتجديد' as "الاختبار 2";

-- فحص المستحقين للتجديد
SELECT COUNT(*) as "عدد المستحقين للتجديد" FROM get_users_due_for_renewal();

-- إنشاء مستخدم مستحق للتجديد للاختبار
INSERT INTO users (phone_number, name, secret_code, next_rewards_renewal) 
VALUES ('***********', 'مستخدم مستحق', '111111', NOW() - INTERVAL '1 day');

-- فحص المستحقين مرة أخرى
SELECT * FROM get_users_due_for_renewal() WHERE phone_number = '***********';

-- ========================================
-- 3. اختبار دالة التجديد
-- ========================================

SELECT '🔄 اختبار دالة التجديد' as "الاختبار 3";

-- تجديد المكافآت للمستحقين
SELECT * FROM renew_user_rewards() WHERE phone_number = '***********';

-- فحص حالة المستخدم بعد التجديد
SELECT 
    phone_number,
    ad_views_remaining,
    recharge_ad_views_remaining,
    renewal_count,
    (next_rewards_renewal > NOW()) as "له تجديد مستقبلي"
FROM users 
WHERE phone_number = '***********';

-- ========================================
-- 4. اختبار الحسابات المفضلة (JSON)
-- ========================================

SELECT '💾 اختبار الحسابات المفضلة' as "الاختبار 4";

-- إضافة حسابات مفضلة
UPDATE users 
SET favorite_accounts = '[
    {"name": "فودافون كاش", "number": "***********"},
    {"name": "أورانج موني", "number": "***********"}
]'::jsonb
WHERE phone_number = '***********';

-- فحص الحسابات المفضلة
SELECT 
    phone_number,
    favorite_accounts
FROM users 
WHERE phone_number = '***********';

-- ========================================
-- 5. اختبار القيود والحماية
-- ========================================

SELECT '🔒 اختبار القيود والحماية' as "الاختبار 5";

-- اختبار قيد المكافآت الموجبة (يجب أن يفشل)
DO $$
BEGIN
    BEGIN
        UPDATE users SET ad_views_remaining = -1 WHERE phone_number = '***********';
        RAISE NOTICE '❌ خطأ: تم قبول قيمة سالبة للمكافآت';
    EXCEPTION WHEN check_violation THEN
        RAISE NOTICE '✅ نجح: تم رفض القيمة السالبة للمكافآت';
    END;
END $$;

-- اختبار قيد رقم الهاتف الفريد (يجب أن يفشل)
DO $$
BEGIN
    BEGIN
        INSERT INTO users (phone_number, name, secret_code) 
        VALUES ('***********', 'مستخدم مكرر', '999999');
        RAISE NOTICE '❌ خطأ: تم قبول رقم هاتف مكرر';
    EXCEPTION WHEN unique_violation THEN
        RAISE NOTICE '✅ نجح: تم رفض رقم الهاتف المكرر';
    END;
END $$;

-- ========================================
-- 6. إحصائيات النظام
-- ========================================

SELECT '📊 إحصائيات النظام' as "الاختبار 6";

-- إحصائيات عامة
SELECT 
    COUNT(*) as "إجمالي المستخدمين",
    COUNT(CASE WHEN next_rewards_renewal <= NOW() THEN 1 END) as "مستحقين للتجديد",
    AVG(ad_views_remaining) as "متوسط مكافآت التوريد",
    AVG(recharge_ad_views_remaining) as "متوسط مكافآت الشحن",
    AVG(renewal_count) as "متوسط التجديدات"
FROM users;

-- المستخدمين الجدد (آخر يوم)
SELECT 
    COUNT(*) as "مستخدمين جدد اليوم"
FROM users 
WHERE created_at >= CURRENT_DATE;

-- ========================================
-- 7. فحص الأداء
-- ========================================

SELECT '⚡ فحص الأداء' as "الاختبار 7";

-- فحص الفهارس
SELECT 
    indexname as "اسم الفهرس",
    indexdef as "تعريف الفهرس"
FROM pg_indexes 
WHERE tablename = 'users'
ORDER BY indexname;

-- حجم الجدول
SELECT 
    pg_size_pretty(pg_total_relation_size('users')) as "حجم الجدول الإجمالي",
    pg_size_pretty(pg_relation_size('users')) as "حجم البيانات",
    pg_size_pretty(pg_total_relation_size('users') - pg_relation_size('users')) as "حجم الفهارس"
;

-- ========================================
-- 8. تنظيف بيانات الاختبار
-- ========================================

SELECT '🧹 تنظيف بيانات الاختبار' as "الخطوة الأخيرة";

-- حذف المستخدمين التجريبيين
DELETE FROM users WHERE phone_number IN ('***********', '***********');

SELECT 'تم حذف المستخدمين التجريبيين' as "النتيجة";

-- ========================================
-- 9. ملخص نتائج الاختبار
-- ========================================

SELECT '✅ اكتملت جميع الاختبارات بنجاح!' as "النتيجة النهائية";

-- فحص نهائي للنظام
SELECT 
    'النظام جاهز للاستخدام' as "الحالة",
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'users') as "عدد الحقول",
    (SELECT COUNT(*) FROM pg_indexes WHERE tablename = 'users') as "عدد الفهارس",
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_name LIKE '%reward%') as "عدد الدوال",
    (SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_name = 'users_renewal_trigger') as "عدد الـ Triggers"
;
