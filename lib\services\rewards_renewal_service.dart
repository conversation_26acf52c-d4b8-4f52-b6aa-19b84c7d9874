import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:wardlytec_app/supabase_config.dart';

/// خدمة تجديد المكافآت الشهرية
class RewardsRenewalService {
  static final _client = SupabaseConfig.client;

  /// فحص وتجديد المكافآت للمستخدمين المستحقين
  static Future<List<Map<String, dynamic>>> checkAndRenewRewards() async {
    try {
      print('🔄 بدء فحص المستخدمين المستحقين لتجديد المكافآت...');

      // استدعاء دالة التجديد في قاعدة البيانات
      final response = await _client.rpc('renew_user_rewards');

      if (response != null && response is List && response.isNotEmpty) {
        print('✅ تم تجديد المكافآت لـ ${response.length} مستخدم');
        
        // طباعة تفاصيل التجديد
        for (var renewal in response) {
          print('👤 ${renewal['name']} (${renewal['phone_number']}):');
          print('   📈 التوريد: ${renewal['old_supply_rewards']} → ${renewal['new_supply_rewards']}');
          print('   📈 الشحن: ${renewal['old_recharge_rewards']} → ${renewal['new_recharge_rewards']}');
          print('   📅 تاريخ التجديد: ${renewal['renewal_date']}');
        }

        return List<Map<String, dynamic>>.from(response);
      } else {
        print('ℹ️ لا يوجد مستخدمين مستحقين للتجديد حالياً');
        return [];
      }
    } catch (e) {
      print('❌ خطأ في تجديد المكافآت: $e');
      return [];
    }
  }

  /// الحصول على المستخدمين المستحقين للتجديد خلال الأيام القادمة
  static Future<List<Map<String, dynamic>>> getUsersDueForRenewal() async {
    try {
      final response = await _client.rpc('get_users_due_for_renewal');

      if (response != null && response is List) {
        return List<Map<String, dynamic>>.from(response);
      }
      return [];
    } catch (e) {
      print('❌ خطأ في جلب المستخدمين المستحقين للتجديد: $e');
      return [];
    }
  }

  /// تجديد مكافآت مستخدم محدد يدوياً (للإدارة)
  static Future<bool> renewUserRewardsManually(int userId) async {
    try {
      print('🔄 تجديد مكافآت المستخدم $userId يدوياً...');

      await _client
          .from('users')
          .update({
            'ad_views_remaining': 10,
            'recharge_ad_views_remaining': 10,
            'last_rewards_renewal': DateTime.now().toIso8601String(),
            'next_rewards_renewal': DateTime.now().add(const Duration(days: 30)).toIso8601String(),
            'renewal_count': 'renewal_count + 1', // استخدام SQL expression
          })
          .eq('id', userId);

      print('✅ تم تجديد مكافآت المستخدم $userId بنجاح');
      return true;
    } catch (e) {
      print('❌ خطأ في تجديد مكافآت المستخدم $userId: $e');
      return false;
    }
  }

  /// الحصول على معلومات تجديد مستخدم محدد
  static Future<Map<String, dynamic>?> getUserRenewalInfo(int userId) async {
    try {
      final response = await _client
          .from('users')
          .select('''
            id,
            name,
            phone_number,
            ad_views_remaining,
            recharge_ad_views_remaining,
            last_rewards_renewal,
            next_rewards_renewal,
            renewal_count,
            monthly_supply_rewards,
            monthly_recharge_rewards,
            created_at
          ''')
          .eq('id', userId)
          .single();

      if (response != null) {
        final nextRenewal = DateTime.parse(response['next_rewards_renewal']);
        final now = DateTime.now();
        final daysUntilRenewal = nextRenewal.difference(now).inDays;

        return {
          ...response,
          'days_until_renewal': daysUntilRenewal,
          'is_due_for_renewal': daysUntilRenewal <= 0,
          'renewal_status': daysUntilRenewal <= 0 ? 'مستحق للتجديد' : 'غير مستحق بعد',
        };
      }
      return null;
    } catch (e) {
      print('❌ خطأ في جلب معلومات تجديد المستخدم $userId: $e');
      return null;
    }
  }

  /// فحص ما إذا كان المستخدم مستحق للتجديد
  static Future<bool> isUserDueForRenewal(int userId) async {
    try {
      final response = await _client
          .from('users')
          .select('next_rewards_renewal')
          .eq('id', userId)
          .single();

      if (response != null) {
        final nextRenewal = DateTime.parse(response['next_rewards_renewal']);
        return DateTime.now().isAfter(nextRenewal);
      }
      return false;
    } catch (e) {
      print('❌ خطأ في فحص استحقاق التجديد للمستخدم $userId: $e');
      return false;
    }
  }

  /// تحديث إعدادات المكافآت الشهرية لمستخدم
  static Future<bool> updateMonthlyRewardsSettings({
    required int userId,
    required int supplyRewards,
    required int rechargeRewards,
  }) async {
    try {
      await _client
          .from('users')
          .update({
            'monthly_supply_rewards': supplyRewards,
            'monthly_recharge_rewards': rechargeRewards,
          })
          .eq('id', userId);

      print('✅ تم تحديث إعدادات المكافآت الشهرية للمستخدم $userId');
      return true;
    } catch (e) {
      print('❌ خطأ في تحديث إعدادات المكافآت للمستخدم $userId: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات التجديد
  static Future<Map<String, dynamic>> getRenewalStatistics() async {
    try {
      // إجمالي المستخدمين
      final totalUsersResponse = await _client
          .from('users')
          .select('id')
          .count(CountOption.exact);

      // المستخدمين المستحقين للتجديد
      final dueUsersResponse = await _client
          .from('users')
          .select('id')
          .lte('next_rewards_renewal', DateTime.now().toIso8601String())
          .count(CountOption.exact);

      // المستخدمين الذين تم تجديد مكافآتهم اليوم
      final todayRenewalsResponse = await _client
          .from('users')
          .select('id')
          .gte('last_rewards_renewal', DateTime.now().subtract(const Duration(days: 1)).toIso8601String())
          .count(CountOption.exact);

      // متوسط عدد مرات التجديد
      final avgRenewalsResponse = await _client
          .from('users')
          .select('renewal_count');

      double avgRenewals = 0;
      if (avgRenewalsResponse != null && avgRenewalsResponse is List && avgRenewalsResponse.isNotEmpty) {
        final totalRenewals = avgRenewalsResponse
            .map((user) => (user['renewal_count'] ?? 0) as int)
            .reduce((a, b) => a + b);
        avgRenewals = totalRenewals / avgRenewalsResponse.length;
      }

      return {
        'total_users': totalUsersResponse.count ?? 0,
        'users_due_for_renewal': dueUsersResponse.count ?? 0,
        'renewals_today': todayRenewalsResponse.count ?? 0,
        'average_renewals_per_user': avgRenewals.toStringAsFixed(1),
        'last_updated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ خطأ في جلب إحصائيات التجديد: $e');
      return {
        'total_users': 0,
        'users_due_for_renewal': 0,
        'renewals_today': 0,
        'average_renewals_per_user': '0.0',
        'last_updated': DateTime.now().toIso8601String(),
        'error': e.toString(),
      };
    }
  }

  /// تشغيل فحص التجديد التلقائي (يتم استدعاؤها عند بدء التطبيق)
  static Future<List<Map<String, dynamic>>> runAutomaticRenewalCheck() async {
    if (kDebugMode) {
      print('🔄 تشغيل فحص التجديد التلقائي...');
    }

    try {
      final renewals = await checkAndRenewRewards();

      if (renewals.isNotEmpty) {
        if (kDebugMode) {
          print('✅ تم تجديد المكافآت لـ ${renewals.length} مستخدم تلقائياً');
        }

        // إرجاع قائمة المستخدمين الذين تم تجديد مكافآتهم
        return renewals;
      } else {
        if (kDebugMode) {
          print('ℹ️ لا يوجد مستخدمين مستحقين للتجديد حالياً');
        }
        return [];
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فحص التجديد التلقائي: $e');
      }
      return [];
    }
  }

  /// إرسال إشعار للمدير عن التجديدات (اختياري)
  static Future<void> _notifyAdminAboutRenewals(List<Map<String, dynamic>> renewals) async {
    // يمكن تنفيذ هذه الدالة لإرسال إشعار عبر Telegram Bot
    // عن المستخدمين الذين تم تجديد مكافآتهم
    if (kDebugMode) {
      print('📢 إشعار المدير: تم تجديد مكافآت ${renewals.length} مستخدم');
    }
  }
}
