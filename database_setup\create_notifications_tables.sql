-- إنشاء جداول نظام الإشعارات لتطبيق وردلي تك
-- Create notifications system tables for Wardly Tech app
-- 🚀 شغل هذا الملف في Supabase SQL Editor

-- ========================================
-- 1. إنشاء جدول إعدادات الإشعارات للمستخدمين
-- ========================================

CREATE TABLE IF NOT EXISTS user_notification_settings (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL,
    is_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- فهرس فريد لمنع التكرار
    UNIQUE(user_id, notification_type)
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_notification_settings_user_id ON user_notification_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notification_settings_type ON user_notification_settings(notification_type);

-- ========================================
-- 2. إنشاء جدول سجل الإشعارات المرسلة
-- ========================================

CREATE TABLE IF NOT EXISTS notification_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL,
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    payload JSONB,
    status VARCHAR(20) DEFAULT 'sent', -- sent, delivered, failed, read
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- معلومات إضافية
    related_transaction_id VARCHAR(100),
    related_service_name VARCHAR(50),
    device_info JSONB,
    
    -- فهرسة
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_logs_user_id ON notification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_logs_type ON notification_logs(notification_type);
CREATE INDEX IF NOT EXISTS idx_notification_logs_status ON notification_logs(status);
CREATE INDEX IF NOT EXISTS idx_notification_logs_sent_at ON notification_logs(sent_at);
CREATE INDEX IF NOT EXISTS idx_notification_logs_transaction_id ON notification_logs(related_transaction_id);

-- ========================================
-- 3. إنشاء دالة تحديث updated_at تلقائياً
-- ========================================

CREATE OR REPLACE FUNCTION update_notification_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء Trigger للتحديث التلقائي
DROP TRIGGER IF EXISTS trigger_update_notification_settings_updated_at ON user_notification_settings;
CREATE TRIGGER trigger_update_notification_settings_updated_at
    BEFORE UPDATE ON user_notification_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_notification_settings_updated_at();

-- ========================================
-- 4. إنشاء دوال مساعدة لإدارة الإشعارات
-- ========================================

-- دالة الحصول على إعدادات إشعارات المستخدم
CREATE OR REPLACE FUNCTION get_user_notification_settings(p_user_id UUID)
RETURNS TABLE(
    notification_type VARCHAR(50),
    is_enabled BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        uns.notification_type,
        uns.is_enabled
    FROM user_notification_settings uns
    WHERE uns.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة تحديث إعدادات الإشعارات
CREATE OR REPLACE FUNCTION update_user_notification_setting(
    p_user_id UUID,
    p_notification_type VARCHAR(50),
    p_is_enabled BOOLEAN
)
RETURNS BOOLEAN AS $$
BEGIN
    INSERT INTO user_notification_settings (user_id, notification_type, is_enabled)
    VALUES (p_user_id, p_notification_type, p_is_enabled)
    ON CONFLICT (user_id, notification_type)
    DO UPDATE SET 
        is_enabled = p_is_enabled,
        updated_at = NOW();
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة تسجيل إشعار مرسل
CREATE OR REPLACE FUNCTION log_notification(
    p_user_id UUID,
    p_notification_type VARCHAR(50),
    p_title TEXT,
    p_body TEXT,
    p_payload JSONB DEFAULT NULL,
    p_related_transaction_id VARCHAR(100) DEFAULT NULL,
    p_related_service_name VARCHAR(50) DEFAULT NULL
)
RETURNS BIGINT AS $$
DECLARE
    notification_id BIGINT;
BEGIN
    INSERT INTO notification_logs (
        user_id,
        notification_type,
        title,
        body,
        payload,
        related_transaction_id,
        related_service_name
    )
    VALUES (
        p_user_id,
        p_notification_type,
        p_title,
        p_body,
        p_payload,
        p_related_transaction_id,
        p_related_service_name
    )
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة تحديث حالة الإشعار
CREATE OR REPLACE FUNCTION update_notification_status(
    p_notification_id BIGINT,
    p_status VARCHAR(20)
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE notification_logs 
    SET 
        status = p_status,
        read_at = CASE WHEN p_status = 'read' THEN NOW() ELSE read_at END
    WHERE id = p_notification_id;
    
    RETURN FOUND;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 5. إعداد Row Level Security
-- ========================================

-- تفعيل RLS
ALTER TABLE user_notification_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان لجدول إعدادات الإشعارات
CREATE POLICY "Users can view their own notification settings" ON user_notification_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notification settings" ON user_notification_settings
    FOR ALL USING (auth.uid() = user_id);

-- سياسات الأمان لجدول سجل الإشعارات
CREATE POLICY "Users can view their own notification logs" ON notification_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert notification logs" ON notification_logs
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own notification logs" ON notification_logs
    FOR UPDATE USING (auth.uid() = user_id);

-- ========================================
-- 6. إدراج الإعدادات الافتراضية
-- ========================================

-- دالة إنشاء الإعدادات الافتراضية للمستخدم الجديد
CREATE OR REPLACE FUNCTION create_default_notification_settings()
RETURNS TRIGGER AS $$
BEGIN
    -- إدراج الإعدادات الافتراضية للمستخدم الجديد
    INSERT INTO user_notification_settings (user_id, notification_type, is_enabled) VALUES
    (NEW.id, 'transaction_updates', true),
    (NEW.id, 'service_updates', true),
    (NEW.id, 'promotional', false),
    (NEW.id, 'maintenance', true)
    ON CONFLICT (user_id, notification_type) DO NOTHING;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إنشاء Trigger لإنشاء الإعدادات الافتراضية عند تسجيل مستخدم جديد
DROP TRIGGER IF EXISTS trigger_create_default_notification_settings ON auth.users;
CREATE TRIGGER trigger_create_default_notification_settings
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION create_default_notification_settings();

-- ========================================
-- 7. دوال إحصائية مفيدة
-- ========================================

-- دالة الحصول على إحصائيات الإشعارات
CREATE OR REPLACE FUNCTION get_notification_stats(p_user_id UUID)
RETURNS TABLE(
    total_notifications BIGINT,
    unread_notifications BIGINT,
    notifications_today BIGINT,
    notifications_this_week BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_notifications,
        COUNT(*) FILTER (WHERE status != 'read') as unread_notifications,
        COUNT(*) FILTER (WHERE sent_at >= CURRENT_DATE) as notifications_today,
        COUNT(*) FILTER (WHERE sent_at >= CURRENT_DATE - INTERVAL '7 days') as notifications_this_week
    FROM notification_logs
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 8. تنظيف البيانات القديمة
-- ========================================

-- دالة تنظيف الإشعارات القديمة (أكثر من 90 يوم)
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM notification_logs 
    WHERE sent_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 9. اختبار النظام
-- ========================================

-- عرض الجداول المنشأة
SELECT '📊 جداول الإشعارات المنشأة:' as "النتيجة";
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_notification_settings', 'notification_logs');

-- عرض الدوال المنشأة
SELECT '🔧 دوال الإشعارات المنشأة:' as "النتيجة";
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%notification%';

-- ========================================
-- 10. تعليقات ومعلومات إضافية
-- ========================================

COMMENT ON TABLE user_notification_settings IS 'إعدادات الإشعارات لكل مستخدم';
COMMENT ON TABLE notification_logs IS 'سجل الإشعارات المرسلة للمستخدمين';

COMMENT ON COLUMN user_notification_settings.notification_type IS 'نوع الإشعار: transaction_updates, service_updates, promotional, maintenance';
COMMENT ON COLUMN notification_logs.status IS 'حالة الإشعار: sent, delivered, failed, read';
COMMENT ON COLUMN notification_logs.payload IS 'بيانات إضافية للإشعار في صيغة JSON';

-- انتهاء الملف
SELECT '✅ تم إنشاء نظام الإشعارات بنجاح!' as "النتيجة النهائية";
