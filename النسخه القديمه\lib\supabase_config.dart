import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:wardlytec_app/config/app_config.dart';

class SupabaseConfig {
  // استخدام الإعدادات الآمنة من AppConfig
  static String get supabaseUrl => AppConfig.supabaseUrl;
  static String get supabaseAnonKey => AppConfig.supabaseAnonKey;
  
  static Future<void> initialize() async {
    // طباعة تحذير أمني في وضع التطوير
    AppConfig.printSecurityWarning();

    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
    );
  }
  
  // للوصول السريع لـ Supabase client
  static SupabaseClient get client => Supabase.instance.client;
}

// مثال على استخدام Supabase
class SupabaseService {
  static final _client = SupabaseConfig.client;
  
  // إضافة مستخدم جديد
  static Future<bool> addUser({
    required String name,
    required String phoneNumber,
    required String secretCode,
  }) async {
    try {
      await _client.from('users').insert({
        'name': name,
        'phone_number': phoneNumber,
        'secret_code': secretCode,
        'created_at': DateTime.now().toIso8601String(),
        'favorite_accounts': [],
      });
      return true;
    } catch (e) {
      print('خطأ في إضافة المستخدم: $e');
      return false;
    }
  }
  
  // البحث عن مستخدم بالهاتف والرقم السري
  static Future<Map<String, dynamic>?> loginUser({
    required String phoneNumber,
    required String secretCode,
  }) async {
    try {
      final response = await _client
          .from('users')
          .select('''
            id,
            phone_number,
            name,
            secret_code,
            created_at,
            last_login,
            favorite_accounts,
            ad_views_remaining,
            recharge_ad_views_remaining,
            last_rewards_renewal,
            next_rewards_renewal,
            renewal_count,
            monthly_supply_rewards,
            monthly_recharge_rewards
          ''')
          .eq('phone_number', phoneNumber)
          .eq('secret_code', secretCode)
          .maybeSingle();

      return response;
    } catch (e) {
      print('خطأ في تسجيل الدخول: $e');
      return null;
    }
  }
  
  // الحصول على جميع المستخدمين (للتشخيص)
  static Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final response = await _client
          .from('users')
          .select('''
            id,
            phone_number,
            name,
            secret_code,
            created_at,
            last_login,
            favorite_accounts,
            ad_views_remaining,
            recharge_ad_views_remaining,
            last_rewards_renewal,
            next_rewards_renewal,
            renewal_count,
            monthly_supply_rewards,
            monthly_recharge_rewards
          ''');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('خطأ في جلب المستخدمين: $e');
      return [];
    }
  }
  
  // تحديث آخر تسجيل دخول
  static Future<bool> updateLastLogin(String phoneNumber) async {
    try {
      await _client
          .from('users')
          .update({'last_login': DateTime.now().toIso8601String()})
          .eq('phone_number', phoneNumber);
      return true;
    } catch (e) {
      print('خطأ في تحديث آخر تسجيل دخول: $e');
      return false;
    }
  }
}
