import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:wardlytec_app/models/user.dart';
import 'package:wardlytec_app/utils/app_theme.dart';

/// بطاقة عرض معلومات تجديد المكافآت
class RewardsRenewalCard extends StatelessWidget {
  final UserModel user;
  final VoidCallback? onRefresh;

  const RewardsRenewalCard({
    Key? key,
    required this.user,
    this.onRefresh,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              isDark ? Colors.grey[800]! : Colors.white,
              isDark ? Colors.grey[850]! : Colors.grey[50]!,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.card_giftcard,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'نظام المكافآت الشهرية',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'تجديد تلقائي كل 30 يوم',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (onRefresh != null)
                    IconButton(
                      onPressed: onRefresh,
                      icon: const Icon(Icons.refresh),
                      tooltip: 'تحديث',
                    ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // المكافآت الحالية
              Row(
                children: [
                  Expanded(
                    child: _buildRewardCounter(
                      context,
                      'التوريد',
                      user.adViewsRemaining,
                      user.monthlySupplyRewards,
                      Icons.upload,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildRewardCounter(
                      context,
                      'الشحن',
                      user.rechargeAdViewsRemaining,
                      user.monthlyRechargeRewards,
                      Icons.battery_charging_full,
                      Colors.green,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // معلومات التجديد
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _getRenewalStatusColor(context).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getRenewalStatusColor(context).withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getRenewalStatusIcon(),
                          color: _getRenewalStatusColor(context),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            user.renewalStatusText,
                            style: TextStyle(
                              color: _getRenewalStatusColor(context),
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    if (user.nextRewardsRenewal != null) ...[
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'التجديد القادم:',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          Text(
                            DateFormat('dd/MM/yyyy').format(user.nextRewardsRenewal!),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                    
                    if (user.lastRewardsRenewal != null) ...[
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'آخر تجديد:',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          Text(
                            DateFormat('dd/MM/yyyy').format(user.lastRewardsRenewal!),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // إحصائيات إضافية
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem(
                    context,
                    'مرات التجديد',
                    user.renewalCount.toString(),
                    Icons.refresh,
                  ),
                  _buildStatItem(
                    context,
                    'عضو منذ',
                    _getTimeSinceRegistration(),
                    Icons.person_add,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRewardCounter(
    BuildContext context,
    String title,
    int current,
    int total,
    IconData icon,
    Color color,
  ) {
    final percentage = total > 0 ? (current / total) : 0.0;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '$current/$total',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: percentage,
            backgroundColor: color.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.grey[600],
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Color _getRenewalStatusColor(BuildContext context) {
    if (user.isDueForRenewal) return Colors.orange;
    
    final days = user.daysUntilRenewal;
    if (days <= 3) return Colors.orange;
    if (days <= 7) return Colors.blue;
    return Colors.green;
  }

  IconData _getRenewalStatusIcon() {
    if (user.isDueForRenewal) return Icons.notification_important;
    
    final days = user.daysUntilRenewal;
    if (days <= 3) return Icons.schedule;
    if (days <= 7) return Icons.access_time;
    return Icons.check_circle;
  }

  String _getTimeSinceRegistration() {
    final difference = DateTime.now().difference(user.createdAt);
    final days = difference.inDays;
    
    if (days < 30) return '$days يوم';
    if (days < 365) return '${(days / 30).floor()} شهر';
    return '${(days / 365).floor()} سنة';
  }
}
