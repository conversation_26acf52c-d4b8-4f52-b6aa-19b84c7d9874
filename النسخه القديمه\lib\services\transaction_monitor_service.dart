import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:wardlytec_app/supabase_config.dart';
import 'package:wardlytec_app/services/local_notification_service.dart';

/// خدمة مراقبة تغييرات المعاملات
class TransactionMonitorService {
  static final SupabaseClient _client = SupabaseConfig.client;
  static RealtimeChannel? _transactionChannel;
  static RealtimeChannel? _serviceStatusChannel;
  static bool _isMonitoring = false;
  static String? _currentUserId;

  /// بدء مراقبة المعاملات للمستخدم الحالي
  static Future<void> startMonitoring(String userId) async {
    if (_isMonitoring && _currentUserId == userId) {
      return; // المراقبة مفعلة بالفعل لنفس المستخدم
    }

    try {
      // إيقاف المراقبة السابقة إن وجدت
      await stopMonitoring();

      _currentUserId = userId;

      // تهيئة خدمة الإشعارات
      await LocalNotificationService.initialize();

      // بدء مراقبة المعاملات
      await _startTransactionMonitoring(userId);

      // بدء مراقبة حالة الخدمات
      await _startServiceStatusMonitoring();

      _isMonitoring = true;

      if (kDebugMode) {
        print('🔍 تم بدء مراقبة المعاملات للمستخدم: $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في بدء مراقبة المعاملات: $e');
      }
    }
  }

  /// إيقاف مراقبة المعاملات
  static Future<void> stopMonitoring() async {
    try {
      if (_transactionChannel != null) {
        await _client.removeChannel(_transactionChannel!);
        _transactionChannel = null;
      }

      if (_serviceStatusChannel != null) {
        await _client.removeChannel(_serviceStatusChannel!);
        _serviceStatusChannel = null;
      }

      _isMonitoring = false;
      _currentUserId = null;

      if (kDebugMode) {
        print('🛑 تم إيقاف مراقبة المعاملات');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إيقاف مراقبة المعاملات: $e');
      }
    }
  }

  /// بدء مراقبة تغييرات المعاملات
  static Future<void> _startTransactionMonitoring(String userId) async {
    try {
      _transactionChannel = _client
          .channel('transaction_updates_$userId')
          .onPostgresChanges(
            event: PostgresChangeEvent.update,
            schema: 'public',
            table: 'transactions',
            filter: PostgresChangeFilter(
              type: PostgresChangeFilterType.eq,
              column: 'user_id',
              value: userId,
            ),
            callback: _handleTransactionUpdate,
          )
          .subscribe();

      if (kDebugMode) {
        print('📡 تم بدء مراقبة تحديثات المعاملات');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في بدء مراقبة المعاملات: $e');
      }
    }
  }

  /// بدء مراقبة تغييرات حالة الخدمات
  static Future<void> _startServiceStatusMonitoring() async {
    try {
      _serviceStatusChannel = _client
          .channel('service_status_updates')
          .onPostgresChanges(
            event: PostgresChangeEvent.update,
            schema: 'public',
            table: 'service_status',
            callback: _handleServiceStatusUpdate,
          )
          .subscribe();

      if (kDebugMode) {
        print('📡 تم بدء مراقبة تحديثات حالة الخدمات');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في بدء مراقبة حالة الخدمات: $e');
      }
    }
  }

  /// معالجة تحديث المعاملة
  static void _handleTransactionUpdate(PostgresChangePayload payload) {
    try {
      if (kDebugMode) {
        print('🔄 تحديث معاملة: ${payload.newRecord}');
      }

      final Map<String, dynamic> oldRecord = payload.oldRecord;
      final Map<String, dynamic> newRecord = payload.newRecord;

      // التحقق من تغيير الحالة
      final String? oldStatus = oldRecord['status'];
      final String? newStatus = newRecord['status'];

      if (oldStatus != null && newStatus != null && oldStatus != newStatus) {
        // إرسال إشعار تحديث المعاملة
        _sendTransactionUpdateNotification(oldRecord, newRecord);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في معالجة تحديث المعاملة: $e');
      }
    }
  }

  /// معالجة تحديث حالة الخدمة
  static void _handleServiceStatusUpdate(PostgresChangePayload payload) {
    try {
      if (kDebugMode) {
        print('🔄 تحديث حالة الخدمة: ${payload.newRecord}');
      }

      final Map<String, dynamic> oldRecord = payload.oldRecord;
      final Map<String, dynamic> newRecord = payload.newRecord;

      // التحقق من تغيير حالة التفعيل
      final bool? oldEnabled = oldRecord['is_enabled'];
      final bool? newEnabled = newRecord['is_enabled'];

      if (oldEnabled != null && newEnabled != null && oldEnabled != newEnabled) {
        // إرسال إشعار تحديث حالة الخدمة
        _sendServiceStatusUpdateNotification(newRecord);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في معالجة تحديث حالة الخدمة: $e');
      }
    }
  }

  /// إرسال إشعار تحديث المعاملة
  static Future<void> _sendTransactionUpdateNotification(
    Map<String, dynamic> oldRecord,
    Map<String, dynamic> newRecord,
  ) async {
    try {
      final String transactionId = newRecord['id']?.toString() ?? '';
      final String oldStatus = oldRecord['status'] ?? '';
      final String newStatus = newRecord['status'] ?? '';
      final String transactionType = newRecord['transaction_type'] ?? '';
      final double? amount = newRecord['amount']?.toDouble();

      await LocalNotificationService.showTransactionUpdateNotification(
        transactionId: transactionId,
        oldStatus: _getStatusDisplayName(oldStatus),
        newStatus: _getStatusDisplayName(newStatus),
        transactionType: transactionType,
        amount: amount,
      );

      if (kDebugMode) {
        print('📱 تم إرسال إشعار تحديث المعاملة: $transactionId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال إشعار تحديث المعاملة: $e');
      }
    }
  }

  /// إرسال إشعار تحديث حالة الخدمة
  static Future<void> _sendServiceStatusUpdateNotification(
    Map<String, dynamic> record,
  ) async {
    try {
      final String serviceName = record['service_name'] ?? '';
      final bool isEnabled = record['is_enabled'] ?? true;
      final String message = record['disabled_message'] ?? '';

      // إرسال إشعار فقط عند تعطيل الخدمة
      if (!isEnabled) {
        await LocalNotificationService.showServiceDisabledNotification(
          serviceName: serviceName,
          message: message,
        );

        if (kDebugMode) {
          print('📱 تم إرسال إشعار تعطيل الخدمة: $serviceName');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال إشعار تحديث حالة الخدمة: $e');
      }
    }
  }

  /// الحصول على اسم الحالة للعرض
  static String _getStatusDisplayName(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'قيد المراجعة';
      case 'approved':
        return 'مقبولة';
      case 'rejected':
        return 'مرفوضة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  /// التحقق من حالة المراقبة
  static bool get isMonitoring => _isMonitoring;

  /// الحصول على معرف المستخدم الحالي المراقب
  static String? get currentUserId => _currentUserId;

  /// إعادة تشغيل المراقبة (مفيد عند انقطاع الاتصال)
  static Future<void> restartMonitoring() async {
    if (_currentUserId != null) {
      await startMonitoring(_currentUserId!);
    }
  }

  /// إرسال إشعار اختبار
  static Future<void> sendTestNotification() async {
    try {
      await LocalNotificationService.showNotification(
        id: 999999,
        title: '🧪 إشعار تجريبي',
        body: 'هذا إشعار تجريبي للتأكد من عمل النظام',
        payload: 'test_notification',
      );

      if (kDebugMode) {
        print('📱 تم إرسال إشعار تجريبي');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال الإشعار التجريبي: $e');
      }
    }
  }
}
