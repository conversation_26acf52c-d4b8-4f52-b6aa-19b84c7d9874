-- إضافة نظام التجديد الشهري للجدول الموجود
-- Add monthly renewal system to existing users table
-- 🚀 شغل هذا الكود لإضافة نظام التجديد للجدول الموجود

-- ========================================
-- 1. فحص الجدول الحالي
-- ========================================

SELECT '📋 فحص جدول المستخدمين الحالي' as "الخطوة 1";

-- عرض الحقول الموجودة
SELECT 
    column_name as "الحقل الموجود",
    data_type as "النوع"
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- عدد المستخدمين
SELECT COUNT(*) as "عدد المستخدمين الحاليين" FROM users;

-- ========================================
-- 2. إضافة الحقول المطلوبة للتجديد الشهري
-- ========================================

SELECT '➕ إضافة حقول التجديد الشهري' as "الخطوة 2";

-- إضافة حقل تاريخ آخر تجديد
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_rewards_renewal TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- إضافة حقل تاريخ التجديد القادم
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS next_rewards_renewal TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days');

-- إضافة حقل عدد مرات التجديد
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS renewal_count INTEGER DEFAULT 0 CHECK (renewal_count >= 0);

-- إضافة حقول إعدادات المكافآت الشهرية
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS monthly_supply_rewards INTEGER DEFAULT 10 CHECK (monthly_supply_rewards > 0);

ALTER TABLE users 
ADD COLUMN IF NOT EXISTS monthly_recharge_rewards INTEGER DEFAULT 10 CHECK (monthly_recharge_rewards > 0);

SELECT '✅ تم إضافة حقول التجديد الشهري' as "النتيجة";

-- ========================================
-- 3. تحديث المستخدمين الموجودين
-- ========================================

SELECT '🔄 تحديث المستخدمين الموجودين' as "الخطوة 3";

-- تحديث المستخدمين الذين ليس لديهم تواريخ تجديد
UPDATE users 
SET 
    last_rewards_renewal = COALESCE(last_rewards_renewal, created_at, NOW()),
    next_rewards_renewal = COALESCE(next_rewards_renewal, created_at + INTERVAL '30 days', NOW() + INTERVAL '30 days'),
    renewal_count = COALESCE(renewal_count, 0),
    monthly_supply_rewards = COALESCE(monthly_supply_rewards, 10),
    monthly_recharge_rewards = COALESCE(monthly_recharge_rewards, 10)
WHERE last_rewards_renewal IS NULL 
   OR next_rewards_renewal IS NULL 
   OR renewal_count IS NULL
   OR monthly_supply_rewards IS NULL
   OR monthly_recharge_rewards IS NULL;

-- عرض نتائج التحديث
SELECT 
    COUNT(*) as "إجمالي المستخدمين",
    COUNT(CASE WHEN last_rewards_renewal IS NOT NULL THEN 1 END) as "لديهم تاريخ تجديد",
    COUNT(CASE WHEN next_rewards_renewal <= NOW() THEN 1 END) as "مستحقين للتجديد"
FROM users;

-- ========================================
-- 4. إنشاء الفهارس للأداء
-- ========================================

SELECT '📊 إنشاء الفهارس للأداء' as "الخطوة 4";

-- فهرس تاريخ التجديد القادم (مهم للأداء)
CREATE INDEX IF NOT EXISTS idx_users_next_renewal ON users(next_rewards_renewal);

-- فهرس تاريخ آخر تجديد
CREATE INDEX IF NOT EXISTS idx_users_last_renewal ON users(last_rewards_renewal);

-- فهرس عدد التجديدات
CREATE INDEX IF NOT EXISTS idx_users_renewal_count ON users(renewal_count);

SELECT '✅ تم إنشاء الفهارس' as "النتيجة";

-- ========================================
-- 5. حذف الدوال القديمة إن وجدت
-- ========================================

SELECT '🧹 تنظيف الدوال القديمة' as "الخطوة 5";

DROP FUNCTION IF EXISTS renew_user_rewards() CASCADE;
DROP FUNCTION IF EXISTS get_users_due_for_renewal() CASCADE;
DROP FUNCTION IF EXISTS set_initial_renewal_dates() CASCADE;
DROP TRIGGER IF EXISTS users_renewal_trigger ON users CASCADE;

SELECT '✅ تم تنظيف الدوال القديمة' as "النتيجة";

-- ========================================
-- 6. إنشاء دالة التجديد
-- ========================================

SELECT '⚙️ إنشاء دالة التجديد' as "الخطوة 6";

CREATE FUNCTION renew_user_rewards()
RETURNS TABLE(
    user_id BIGINT,
    phone_number VARCHAR(20),
    name VARCHAR(100),
    old_supply_rewards INTEGER,
    old_recharge_rewards INTEGER,
    new_supply_rewards INTEGER,
    new_recharge_rewards INTEGER,
    renewal_date TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    UPDATE users 
    SET 
        ad_views_remaining = monthly_supply_rewards,
        recharge_ad_views_remaining = monthly_recharge_rewards,
        last_rewards_renewal = NOW(),
        next_rewards_renewal = NOW() + INTERVAL '30 days',
        renewal_count = renewal_count + 1
    WHERE next_rewards_renewal <= NOW()
    RETURNING 
        id as user_id,
        users.phone_number,
        users.name,
        (ad_views_remaining - monthly_supply_rewards) as old_supply_rewards,
        (recharge_ad_views_remaining - monthly_recharge_rewards) as old_recharge_rewards,
        monthly_supply_rewards as new_supply_rewards,
        monthly_recharge_rewards as new_recharge_rewards,
        NOW() as renewal_date;
END;
$$;

SELECT '✅ تم إنشاء دالة التجديد' as "النتيجة";

-- ========================================
-- 7. إنشاء دالة فحص المستحقين للتجديد
-- ========================================

SELECT '🔍 إنشاء دالة فحص المستحقين للتجديد' as "الخطوة 7";

CREATE FUNCTION get_users_due_for_renewal()
RETURNS TABLE(
    user_id BIGINT,
    phone_number VARCHAR(20),
    name VARCHAR(100),
    current_supply_rewards INTEGER,
    current_recharge_rewards INTEGER,
    days_overdue INTEGER,
    last_renewal TIMESTAMP WITH TIME ZONE,
    next_renewal TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id as user_id,
        u.phone_number,
        u.name,
        u.ad_views_remaining as current_supply_rewards,
        u.recharge_ad_views_remaining as current_recharge_rewards,
        EXTRACT(DAYS FROM (NOW() - u.next_rewards_renewal))::INTEGER as days_overdue,
        u.last_rewards_renewal as last_renewal,
        u.next_rewards_renewal as next_renewal
    FROM users u
    WHERE u.next_rewards_renewal <= NOW()
    ORDER BY u.next_rewards_renewal ASC;
END;
$$;

SELECT '✅ تم إنشاء دالة فحص المستحقين للتجديد' as "النتيجة";

-- ========================================
-- 8. إنشاء دالة للمستخدمين الجدد
-- ========================================

SELECT '👤 إنشاء دالة للمستخدمين الجدد' as "الخطوة 8";

CREATE FUNCTION set_initial_renewal_dates()
RETURNS TRIGGER 
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.last_rewards_renewal = NOW();
    NEW.next_rewards_renewal = NOW() + INTERVAL '30 days';
    NEW.renewal_count = 0;
    NEW.monthly_supply_rewards = COALESCE(NEW.monthly_supply_rewards, 10);
    NEW.monthly_recharge_rewards = COALESCE(NEW.monthly_recharge_rewards, 10);
    NEW.ad_views_remaining = COALESCE(NEW.ad_views_remaining, NEW.monthly_supply_rewards);
    NEW.recharge_ad_views_remaining = COALESCE(NEW.recharge_ad_views_remaining, NEW.monthly_recharge_rewards);
    
    RETURN NEW;
END;
$$;

-- إنشاء trigger للمستخدمين الجدد
CREATE TRIGGER users_renewal_trigger
    BEFORE INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION set_initial_renewal_dates();

SELECT '✅ تم إنشاء دالة وtrigger للمستخدمين الجدد' as "النتيجة";

-- ========================================
-- 9. اختبار النظام
-- ========================================

SELECT '🧪 اختبار النظام' as "الخطوة 9";

-- فحص المستحقين للتجديد
SELECT 
    'المستحقين للتجديد' as "النوع",
    COUNT(*) as "العدد"
FROM get_users_due_for_renewal();

-- إحصائيات عامة
SELECT 
    COUNT(*) as "إجمالي المستخدمين",
    COUNT(CASE WHEN next_rewards_renewal <= NOW() THEN 1 END) as "مستحقين للتجديد",
    ROUND(AVG(renewal_count), 2) as "متوسط التجديدات",
    ROUND(AVG(COALESCE(ad_views_remaining, 0)), 2) as "متوسط مكافآت التوريد",
    ROUND(AVG(COALESCE(recharge_ad_views_remaining, 0)), 2) as "متوسط مكافآت الشحن"
FROM users;

-- فحص الدوال الجديدة
SELECT 
    routine_name as "الدالة المنشأة",
    'موجودة' as "الحالة"
FROM information_schema.routines 
WHERE routine_name IN ('renew_user_rewards', 'get_users_due_for_renewal', 'set_initial_renewal_dates')
ORDER BY routine_name;

-- فحص الـ trigger الجديد
SELECT 
    trigger_name as "الـ Trigger المنشأ",
    'يعمل' as "الحالة"
FROM information_schema.triggers 
WHERE trigger_name = 'users_renewal_trigger';

SELECT '✅ اكتملت الاختبارات بنجاح' as "النتيجة";

-- ========================================
-- 10. رسائل النجاح النهائية
-- ========================================

SELECT '🎉 تم إضافة نظام التجديد الشهري بنجاح!' as "النتيجة النهائية";
SELECT 'تم الاحتفاظ بجميع البيانات الموجودة' as "الأمان";
SELECT 'جميع الدوال والـ triggers والفهارس جاهزة' as "الحالة";
SELECT 'يمكنك الآن استخدام النظام في التطبيق' as "الخطوة التالية";

-- ملخص النظام
SELECT 
    '📋 ملخص النظام' as "الملخص",
    (SELECT COUNT(*) FROM users) as "إجمالي المستخدمين",
    (SELECT COUNT(*) FROM get_users_due_for_renewal()) as "مستحقين للتجديد",
    '5 حقول جديدة' as "الحقول المضافة",
    '3 دوال جديدة' as "الدوال",
    '1 trigger جديد' as "الـ Triggers",
    '3 فهارس جديدة' as "الفهارس";
