import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wardlytec_app/services/connectivity_service.dart';
import 'package:wardlytec_app/utils/app_theme.dart';

/// Widget لتغليف التطبيق ومراقبة حالة الاتصال مع دعم الإنترنت الضعيف
class ConnectivityWrapper extends StatefulWidget {
  final Widget child;
  final bool showNoInternetCard; // للتحكم في إظهار البطاقة
  final bool showQualityIndicator; // لإظهار مؤشر جودة الاتصال
  final ActionType? requiredQuality; // الجودة المطلوبة للعملية
  final VoidCallback? onConnectionRestored; // callback عند استعادة الاتصال

  const ConnectivityWrapper({
    Key? key,
    required this.child,
    this.showNoInternetCard = true, // افتراضياً تظهر البطاقة
    this.showQualityIndicator = false, // افتراضياً لا تظهر مؤشر الجودة
    this.requiredQuality, // اختياري - للعمليات التي تحتاج جودة معينة
    this.onConnectionRestored, // callback عند استعادة الاتصال
  }) : super(key: key);

  @override
  State<ConnectivityWrapper> createState() => _ConnectivityWrapperState();
}

class _ConnectivityWrapperState extends State<ConnectivityWrapper>
    with TickerProviderStateMixin {
  final ConnectivityService _connectivityService = ConnectivityService();
  bool _isConnected = true;
  ConnectionQuality _currentQuality = ConnectionQuality.good;
  ConnectionInfo? _lastConnectionInfo;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  Timer? _connectionCheckTimer;
  StreamSubscription<bool>? _connectionStatusSubscription;
  StreamSubscription<ConnectionQuality>? _connectionQualitySubscription;

  @override
  void initState() {
    super.initState();

    // تهيئة الرسوم المتحركة
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // الاستماع لتغييرات الاتصال (الخدمة مُهيأة مسبقاً في main.dart)
    _connectionStatusSubscription = _connectivityService.connectionStatus.listen((bool isConnected) {
      if (!mounted) return;

      final bool wasConnected = _isConnected;
      setState(() {
        _isConnected = isConnected;
      });

      // إذا انتقلنا من عدم الاتصال إلى الاتصال، نفّذ callback فوراً حتى بدون إظهار بطاقة عدم الاتصال
      if (!wasConnected && isConnected && widget.onConnectionRestored != null) {
        widget.onConnectionRestored!();
      }
    });

    // الاستماع لتغييرات جودة الاتصال
    _connectionQualitySubscription = _connectivityService.connectionQuality.listen((ConnectionQuality quality) {
      if (mounted) {
        setState(() {
          _currentQuality = quality;
        });
      }
    });

    // فحص الحالة الأولية للاتصال
    _checkInitialConnection();
  }

  // بدء مراقبة الاتصال تلقائياً (مثل NoInternetDialog)
  void _startConnectionMonitoring() {
    // إلغاء أي مراقبة سابقة
    _connectionCheckTimer?.cancel();

    _connectionCheckTimer = Timer.periodic(const Duration(seconds: 5), (timer) async {
      if (!mounted) {
        timer.cancel();
        return;
      }

      // فقط إذا كانت البطاقة تظهر حالياً (لا يوجد اتصال)
      if (!_isConnected && widget.showNoInternetCard) {
        try {
          final bool isConnected = await _connectivityService.checkConnection();

          if (isConnected && mounted) {
            // تم استعادة الاتصال - إخفاء البطاقة تلقائياً
            timer.cancel();
            setState(() {
              _isConnected = true;
            });

            // استدعاء callback إذا كان موجود
            if (widget.onConnectionRestored != null) {
              widget.onConnectionRestored!();
            }

            // إظهار رسالة نجاح
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 8),
                    Text('تم استعادة الاتصال بالإنترنت تلقائياً'),
                  ],
                ),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }
        } catch (e) {
          // في حالة الخطأ، استمر في المراقبة
          print('خطأ في مراقبة الاتصال (ConnectivityWrapper): $e');
        }
      } else {
        // إذا عاد الاتصال، أوقف المراقبة
        timer.cancel();
      }
    });
  }

  // فحص الحالة الأولية للاتصال مع تقييم الجودة
  Future<void> _checkInitialConnection() async {
    print('🔍 ConnectivityWrapper: فحص الحالة الأولية للاتصال...');

    // فحص مفصل لجودة الاتصال
    final connectionInfo = await _connectivityService.getDetailedConnectionInfo();
    final bool isConnected = connectionInfo.quality != ConnectionQuality.none;

    print('📶 ConnectivityWrapper: حالة الاتصال = $isConnected');
    print('📊 ConnectivityWrapper: جودة الاتصال = ${connectionInfo.qualityText}');
    print('🎛️ ConnectivityWrapper: showNoInternetCard = ${widget.showNoInternetCard}');

    if (mounted) {
      setState(() {
        _isConnected = isConnected;
        _currentQuality = connectionInfo.quality;
        _lastConnectionInfo = connectionInfo;
      });

      // التحقق من الجودة المطلوبة للعملية (تم تعطيل التحذيرات)
      if (widget.requiredQuality != null) {
        final canPerform = await _connectivityService.canPerformAction(
          actionType: widget.requiredQuality!,
        );

        if (!canPerform) {
          print('⚠️ ConnectivityWrapper: جودة الاتصال غير كافية للعملية المطلوبة (تم إخفاء التحذير)');
          // _showQualityWarning(connectionInfo); // تم تعطيل إشعار تحذير الجودة
          // return; // السماح بالمتابعة حتى مع ضعف الاتصال
        }
      }

      if (!isConnected && widget.showNoInternetCard) {
        print('🚫 ConnectivityWrapper: سيتم عرض بطاقة عدم الاتصال');
        // بدء المراقبة التلقائية
        _startConnectionMonitoring();
      } else {
        print('✅ ConnectivityWrapper: سيتم عرض المحتوى العادي');
      }
    }
  }

  // إظهار تحذير جودة الاتصال
  void _showQualityWarning(ConnectionInfo connectionInfo) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.signal_wifi_bad,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'جودة الاتصال: ${connectionInfo.qualityText}\n'
                '${_connectivityService.getQualityMessage(connectionInfo.quality)}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Color(_connectivityService.getQualityColor(connectionInfo.quality)),
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  @override
  void dispose() {
    _connectionCheckTimer?.cancel();
    _connectionStatusSubscription?.cancel();
    _connectionQualitySubscription?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  /// إعادة محاولة الاتصال
  Future<void> _retryConnection() async {
    // إظهار مؤشر التحميل
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text('جاري فحص الاتصال...'),
          ],
        ),
        duration: Duration(seconds: 2),
      ),
    );

    // فحص الاتصال بقوة (بدون cache)
    final bool isConnected = await _connectivityService.checkConnection(forceCheck: true);

    if (mounted) {
      if (isConnected) {
        setState(() {
          _isConnected = true;
        });

        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('تم استعادة الاتصال بالإنترنت'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      } else {
        // إظهار رسالة فشل
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 8),
                Text('لا يزال الاتصال غير متوفر'),
              ],
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );

        // إعادة بدء المراقبة التلقائية
        _startConnectionMonitoring();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    print('🏗️ ConnectivityWrapper.build: _isConnected = $_isConnected, quality = ${_currentQuality.name}');

    // إذا كان الاتصال متوفر أو لا نريد إظهار البطاقة، نعرض الطفل مع مؤشر الجودة
    if (_isConnected || !widget.showNoInternetCard) {
      print('✅ ConnectivityWrapper: عرض المحتوى العادي');

      // إذا كان مؤشر الجودة مطلوب، نعرضه
      if (widget.showQualityIndicator && _lastConnectionInfo != null) {
        return Stack(
          children: [
            widget.child,
            _buildQualityIndicator(),
          ],
        );
      }

      return widget.child;
    }

    // إذا لم يكن هناك اتصال، نعرض بطاقة عدم الاتصال
    print('🚫 ConnectivityWrapper: عرض بطاقة عدم الاتصال');
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Card(
            elevation: 8,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(32.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة عدم الاتصال
                  TweenAnimationBuilder<double>(
                    duration: const Duration(seconds: 2),
                    tween: Tween(begin: 0.0, end: 1.0),
                    builder: (context, value, child) {
                      return Transform.scale(
                        scale: 0.8 + (0.2 * value),
                        child: Icon(
                          Icons.wifi_off,
                          size: 80,
                          color: Colors.red.shade400,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 24),

                  // العنوان
                  Text(
                    'لا يوجد اتصال بالإنترنت',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.titleLarge?.color,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),

                  // الوصف
                  Text(
                    'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى',
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'تأكد من تفعيل الواي فاي أو بيانات الجوال',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),

                  // زر إعادة المحاولة في المنتصف
                  Center(
                    child: ElevatedButton.icon(
                      onPressed: _retryConnection,
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة المحاولة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // بناء مؤشر جودة الاتصال
  Widget _buildQualityIndicator() {
    if (_lastConnectionInfo == null) return const SizedBox.shrink();

    final connectionInfo = _lastConnectionInfo!;
    final quality = connectionInfo.quality;

    // لا نعرض المؤشر إذا كان الاتصال جيد
    if (quality == ConnectionQuality.good) {
      return const SizedBox.shrink();
    }

    return Positioned(
      top: MediaQuery.of(context).padding.top + 8,
      left: 16,
      right: 16,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Color(_connectivityService.getQualityColor(quality)).withOpacity(0.9),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getQualityIcon(quality),
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '${connectionInfo.qualityText} (${connectionInfo.responseTime}ms)',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Text(
              connectionInfo.connectionType,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // الحصول على أيقونة مناسبة لجودة الاتصال
  IconData _getQualityIcon(ConnectionQuality quality) {
    switch (quality) {
      case ConnectionQuality.none:
        return Icons.wifi_off;
      case ConnectionQuality.poor:
        return Icons.wifi_1_bar;
      case ConnectionQuality.slow:
        return Icons.wifi_2_bar;
      case ConnectionQuality.moderate:
        return Icons.network_wifi;
      case ConnectionQuality.good:
        return Icons.wifi;
    }
  }
}
