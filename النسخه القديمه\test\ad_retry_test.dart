import 'package:flutter_test/flutter_test.dart';
import 'package:wardlytec_app/services/ad_manager.dart';
import 'package:wardlytec_app/utils/ad_connection_monitor.dart';

void main() {
  group('🔍 Ad Retry Mechanism - Deep Tests', () {
    test('AdConnectionMonitor should handle connection loss', () async {
      bool connectionLostCalled = false;
      bool buttonReactivatedCalled = false;

      // محاكاة مراقبة الاتصال
      AdConnectionMonitor.startAdMonitoring(
        onConnectionLost: () {
          connectionLostCalled = true;
        },
        onAdButtonReactivated: () {
          buttonReactivatedCalled = true;
        },
      );

      // انتظار قصير للسماح للمراقبة بالعمل
      await Future.delayed(const Duration(milliseconds: 100));

      // إيقاف المراقبة
      AdConnectionMonitor.stopMonitoring();

      expect(AdConnectionMonitor.isMonitoring, false);
    });

    test('AdManager should handle ad failure correctly', () {
      // اختبار أن AdManager يتعامل مع فشل الإعلان بشكل صحيح
      expect(AdManager.rewardedAdUnitId, isNotEmpty);
    });

    test('Connection monitor should stop monitoring when disposed', () {
      AdConnectionMonitor.dispose();
      expect(AdConnectionMonitor.isMonitoring, false);
    });

    test('AdManager callback protection should work', () {
      // اختبار أن callbacks لا يتم استدعاؤها أكثر من مرة
      int onAdWatchedCount = 0;
      int onAdFailedCount = 0;

      // محاكاة callback protection
      bool callbackExecuted = false;

      void mockOnAdWatched() {
        if (!callbackExecuted) {
          callbackExecuted = true;
          onAdWatchedCount++;
        }
      }

      void mockOnAdFailed() {
        if (!callbackExecuted) {
          callbackExecuted = true;
          onAdFailedCount++;
        }
      }

      // محاكاة استدعاءات متعددة
      mockOnAdWatched();
      mockOnAdWatched(); // يجب أن يتم تجاهله
      mockOnAdFailed(); // يجب أن يتم تجاهله

      expect(onAdWatchedCount, 1);
      expect(onAdFailedCount, 0);
    });

    test('AdManager should handle hasEarnedReward correctly', () {
      // محاكاة منطق hasEarnedReward
      bool hasEarnedReward = false;
      bool callbackExecuted = false;
      int onAdWatchedCount = 0;
      int onAdFailedCount = 0;

      // محاكاة onUserEarnedReward
      void mockOnUserEarnedReward() {
        hasEarnedReward = true;
        if (!callbackExecuted) {
          callbackExecuted = true;
          onAdWatchedCount++;
        }
      }

      // محاكاة onAdDismissedFullScreenContent
      void mockOnAdDismissed() {
        if (hasEarnedReward) {
          // تم الحصول على المكافأة - لا نحتاج لاستدعاء onAdWatched مرة أخرى
        } else {
          if (!callbackExecuted) {
            callbackExecuted = true;
            onAdFailedCount++;
          }
        }
      }

      // سيناريو 1: مشاهدة ناجحة
      mockOnUserEarnedReward();
      mockOnAdDismissed();

      expect(onAdWatchedCount, 1);
      expect(onAdFailedCount, 0);

      // إعادة تعيين للسيناريو التالي
      hasEarnedReward = false;
      callbackExecuted = false;
      onAdWatchedCount = 0;
      onAdFailedCount = 0;

      // سيناريو 2: إغلاق بدون مكافأة
      mockOnAdDismissed(); // بدون استدعاء mockOnUserEarnedReward

      expect(onAdWatchedCount, 0);
      expect(onAdFailedCount, 1);
    });

    test('Session ID format should be correct', () {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final sessionId = 'session_$timestamp';
      final sessionKey = 'ad_watched_$sessionId';

      expect(sessionId, startsWith('session_'));
      expect(sessionKey, startsWith('ad_watched_session_'));

      // اختبار تحليل timestamp
      final parts = sessionKey.split('_');
      expect(parts.length, 4); // ['ad', 'watched', 'session', 'timestamp']
      expect(int.tryParse(parts.last), isNotNull);
    });
  });

  group('🎯 Ad Button State Tests', () {
    test('Button state logic should work correctly', () {
      // محاكاة حالات الزر
      bool hasWatchedAd = false;
      bool hasWatchedAdInSession = false;
      double commission = 1.0;
      int remainingViews = 5;

      // اختبار شرط تفعيل الزر
      bool shouldEnable = (commission > 0 && remainingViews > 0 && !hasWatchedAd && !hasWatchedAdInSession);
      expect(shouldEnable, true);

      // محاكاة مشاهدة الإعلان
      hasWatchedAdInSession = true;
      shouldEnable = (commission > 0 && remainingViews > 0 && !hasWatchedAd && !hasWatchedAdInSession);
      expect(shouldEnable, false);

      // محاكاة إعادة تعيين للمحاولة مرة أخرى
      hasWatchedAdInSession = false;
      shouldEnable = (commission > 0 && remainingViews > 0 && !hasWatchedAd && !hasWatchedAdInSession);
      expect(shouldEnable, true);
    });

    test('Session cleanup logic should work correctly', () {
      final now = DateTime.now().millisecondsSinceEpoch;
      final oldTimestamp = now - (25 * 60 * 60 * 1000); // 25 ساعة قديمة
      final newTimestamp = now - (1 * 60 * 60 * 1000); // ساعة واحدة قديمة

      final oldKey = 'ad_watched_session_$oldTimestamp';
      final newKey = 'ad_watched_session_$newTimestamp';

      // محاكاة منطق التنظيف
      bool shouldDeleteOld = (now - oldTimestamp) > (24 * 60 * 60 * 1000);
      bool shouldDeleteNew = (now - newTimestamp) > (24 * 60 * 60 * 1000);

      expect(shouldDeleteOld, true);
      expect(shouldDeleteNew, false);
    });
  });

  group('🔄 Integration Flow Tests', () {
    test('Complete ad failure and retry flow', () {
      // محاكاة التدفق الكامل
      bool adButtonEnabled = true;
      bool hasWatchedAdInSession = false;
      bool isLoading = false;

      // 1. المستخدم يضغط على الزر
      expect(adButtonEnabled && !hasWatchedAdInSession && !isLoading, true);

      // 2. بدء التحميل
      isLoading = true;
      expect(adButtonEnabled && !isLoading, false);

      // 3. فشل الإعلان - إعادة تعيين الحالة
      isLoading = false;
      hasWatchedAdInSession = false; // إعادة تعيين للمحاولة مرة أخرى

      // 4. الزر يصبح متاحاً مرة أخرى
      expect(adButtonEnabled && !hasWatchedAdInSession && !isLoading, true);
    });

    test('_resetAdStateForRetry should work correctly', () {
      // محاكاة دالة _resetAdStateForRetry
      bool hasWatchedAdInSession = true;
      bool isLoading = true;
      bool buttonReactivated = false;

      // محاكاة _resetAdStateForRetry
      void mockResetAdStateForRetry() {
        hasWatchedAdInSession = false;
        isLoading = false; // محاكاة reactivateButton
        buttonReactivated = true;
      }

      // قبل إعادة التعيين
      expect(hasWatchedAdInSession, true);
      expect(isLoading, true);

      // تنفيذ إعادة التعيين
      mockResetAdStateForRetry();

      // بعد إعادة التعيين
      expect(hasWatchedAdInSession, false);
      expect(isLoading, false);
      expect(buttonReactivated, true);
    });

    test('Ad retry scenarios should work correctly', () {
      // اختبار سيناريوهات مختلفة لإعادة المحاولة

      // سيناريو 1: انقطاع الاتصال
      bool scenario1_hasWatchedAdInSession = true;
      scenario1_hasWatchedAdInSession = false; // إعادة تعيين
      expect(scenario1_hasWatchedAdInSession, false);

      // سيناريو 2: فشل تحميل الإعلان
      bool scenario2_hasWatchedAdInSession = true;
      scenario2_hasWatchedAdInSession = false; // إعادة تعيين
      expect(scenario2_hasWatchedAdInSession, false);

      // سيناريو 3: إغلاق الإعلان بدون مكافأة
      bool scenario3_hasWatchedAdInSession = true;
      scenario3_hasWatchedAdInSession = false; // إعادة تعيين
      expect(scenario3_hasWatchedAdInSession, false);
    });
  });
}
