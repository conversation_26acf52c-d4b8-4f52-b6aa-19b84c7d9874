import 'package:flutter/material.dart';
import 'package:wardlytec_app/models/service_status.dart';
import 'package:wardlytec_app/services/service_status_service.dart';
import 'package:wardlytec_app/utils/app_theme.dart';

/// صفحة تحكم المسؤول في حالة الخدمات
class AdminControlScreen extends StatefulWidget {
  const AdminControlScreen({Key? key}) : super(key: key);

  @override
  State<AdminControlScreen> createState() => _AdminControlScreenState();
}

class _AdminControlScreenState extends State<AdminControlScreen> {
  bool _isLoading = true;
  List<ServiceStatus> _services = [];
  String? _error;

  // Controllers للرسائل المخصصة
  final _supplyMessageController = TextEditingController();
  final _supplyTitleController = TextEditingController();
  final _rechargeMessageController = TextEditingController();
  final _rechargeTitleController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadServices();
  }

  @override
  void dispose() {
    _supplyMessageController.dispose();
    _supplyTitleController.dispose();
    _rechargeMessageController.dispose();
    _rechargeTitleController.dispose();
    super.dispose();
  }

  /// تحميل حالة الخدمات
  Future<void> _loadServices() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final services = await ServiceStatusService.getAllServiceStatuses();
      
      setState(() {
        _services = services;
        _isLoading = false;
      });

      // تحديث النصوص في الحقول
      _updateTextControllers();
    } catch (e) {
      setState(() {
        _error = 'فشل في تحميل حالة الخدمات: $e';
        _isLoading = false;
      });
    }
  }

  /// تحديث النصوص في حقول الإدخال
  void _updateTextControllers() {
    for (final service in _services) {
      if (service.serviceName == 'supply_transactions') {
        _supplyMessageController.text = service.disabledMessage;
        _supplyTitleController.text = service.customTitle;
      } else if (service.serviceName == 'recharge_transactions') {
        _rechargeMessageController.text = service.disabledMessage;
        _rechargeTitleController.text = service.customTitle;
      }
    }
  }

  /// تحديث حالة خدمة
  Future<void> _updateServiceStatus(String serviceName, bool isEnabled) async {
    try {
      // تحديد الرسائل المخصصة حسب نوع الخدمة
      String? customMessage;
      String? customTitle;

      if (serviceName == 'supply_transactions') {
        customMessage = _supplyMessageController.text.trim();
        customTitle = _supplyTitleController.text.trim();
      } else if (serviceName == 'recharge_transactions') {
        customMessage = _rechargeMessageController.text.trim();
        customTitle = _rechargeTitleController.text.trim();
      }

      final update = ServiceStatusUpdate(
        serviceName: serviceName,
        isEnabled: isEnabled,
        disabledMessage: customMessage?.isNotEmpty == true ? customMessage : null,
        customTitle: customTitle?.isNotEmpty == true ? customTitle : null,
        updatedBy: 'admin',
      );

      final response = await ServiceStatusService.updateServiceStatus(update);

      if (response.success) {
        // إظهار رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    isEnabled ? Icons.check_circle : Icons.cancel,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      isEnabled 
                          ? 'تم تفعيل ${_getServiceDisplayName(serviceName)} بنجاح'
                          : 'تم تعطيل ${_getServiceDisplayName(serviceName)} بنجاح',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: isEnabled ? Colors.green[600] : Colors.orange[600],
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );
        }

        // إعادة تحميل البيانات
        await _loadServices();
      } else {
        // إظهار رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ: ${response.message}'),
              backgroundColor: Colors.red[600],
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  /// الحصول على اسم الخدمة للعرض
  String _getServiceDisplayName(String serviceName) {
    switch (serviceName) {
      case 'supply_transactions':
        return 'خدمة التوريد';
      case 'recharge_transactions':
        return 'خدمة الشحن';
      default:
        return serviceName;
    }
  }

  /// الحصول على أيقونة الخدمة
  IconData _getServiceIcon(String serviceName) {
    switch (serviceName) {
      case 'supply_transactions':
        return Icons.account_balance_wallet;
      case 'recharge_transactions':
        return Icons.phone_android;
      default:
        return Icons.settings;
    }
  }

  /// الحصول على خدمة معينة
  ServiceStatus? _getService(String serviceName) {
    try {
      return _services.firstWhere((s) => s.serviceName == serviceName);
    } catch (e) {
      return null;
    }
  }

  /// بناء كارت الخدمة
  Widget _buildServiceCard(String serviceName) {
    final service = _getService(serviceName);
    final isEnabled = service?.isEnabled ?? true;
    final displayName = _getServiceDisplayName(serviceName);
    final icon = _getServiceIcon(serviceName);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isEnabled ? Colors.green.withOpacity(0.3) : Colors.red.withOpacity(0.3),
            width: 2,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الكارت
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isEnabled
                          ? Colors.green.withOpacity(0.1)
                          : Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      icon,
                      size: 32,
                      color: isEnabled ? Colors.green : Colors.red,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          displayName,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: isEnabled ? Colors.green : Colors.red,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                isEnabled ? 'مفعلة' : 'معطلة',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              isEnabled ? '✅' : '❌',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // مفتاح التفعيل/التعطيل
                  Switch(
                    value: isEnabled,
                    onChanged: (value) => _updateServiceStatus(serviceName, value),
                    activeColor: Colors.green,
                    inactiveThumbColor: Colors.red,
                  ),
                ],
              ),

              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),

              // إعدادات الرسائل المخصصة
              Text(
                'رسائل التعطيل المخصصة:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 12),

              // حقل العنوان المخصص
              TextField(
                controller: serviceName == 'supply_transactions'
                    ? _supplyTitleController
                    : _rechargeTitleController,
                decoration: InputDecoration(
                  labelText: 'عنوان رسالة التعطيل',
                  hintText: 'مثال: الخدمة غير متاحة مؤقتاً',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.title),
                ),
                maxLines: 1,
              ),

              const SizedBox(height: 12),

              // حقل الرسالة المخصصة
              TextField(
                controller: serviceName == 'supply_transactions'
                    ? _supplyMessageController
                    : _rechargeMessageController,
                decoration: InputDecoration(
                  labelText: 'نص رسالة التعطيل',
                  hintText: 'اكتب رسالة مخصصة للعملاء عند تعطيل الخدمة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.message),
                ),
                maxLines: 3,
              ),

              const SizedBox(height: 16),

              // أزرار سريعة
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: isEnabled
                          ? null
                          : () => _updateServiceStatus(serviceName, true),
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('تفعيل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: !isEnabled
                          ? null
                          : () => _updateServiceStatus(serviceName, false),
                      icon: const Icon(Icons.pause),
                      label: const Text('تعطيل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'لوحة تحكم المسؤول',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadServices,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل حالة الخدمات...'),
                ],
              ),
            )
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _error!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadServices,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [


                      // خدمة التوريد
                      _buildServiceCard('supply_transactions'),

                      const SizedBox(height: 16),

                      // خدمة الشحن
                      _buildServiceCard('recharge_transactions'),


                    ],
                  ),
                ),
    );
  }
}
