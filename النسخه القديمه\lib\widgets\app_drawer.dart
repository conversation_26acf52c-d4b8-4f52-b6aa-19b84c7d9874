import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/screens/company_selection_screen.dart';
import 'package:wardlytec_app/screens/network_selection_screen.dart';
import 'package:wardlytec_app/screens/transaction_history_screen.dart';
import 'package:wardlytec_app/screens/auth/login_screen.dart';
import 'package:wardlytec_app/screens/faq_screen.dart';
import 'package:wardlytec_app/screens/admin/admin_control_screen.dart';

import 'package:wardlytec_app/providers/theme_provider.dart';
import 'package:wardlytec_app/utils/app_theme.dart';
import 'package:wardlytec_app/utils/network_helper.dart';
import 'package:wardlytec_app/config/production_config.dart';
import 'package:url_launcher/url_launcher.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<SupabaseAuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        final isLoggedIn = user != null;

        return Drawer(
          child: Column(
            children: [
              // Header - معلومات المستخدم
              _buildDrawerHeader(context, user, isLoggedIn),

              // Body - القائمة الرئيسية
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    // قسم التنقل
                    _buildNavigationSection(context, isLoggedIn),

                    const Divider(),

                    // قسم الإعدادات
                    _buildSettingsSection(context),

                    const Divider(),

                    // قسم الدعم
                    _buildSupportSection(context),

                    const Divider(),

                    // قسم المعلومات
                    _buildInfoSection(context, isLoggedIn),
                  ],
                ),
              ),
              
              // Footer - معلومات الإصدار
              _buildDrawerFooter(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDrawerHeader(BuildContext context, dynamic user, bool isLoggedIn) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(16, 60, 16, 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // صورة المستخدم
          CircleAvatar(
            radius: 35,
            backgroundColor: Colors.white,
            child: Icon(
              Icons.person,
              size: 40,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          // اسم العميل
          Text(
            isLoggedIn ? (user?.name ?? 'عميل وردلي تك') : 'ضيف',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          // رقم الهاتف
          Text(
            isLoggedIn ? (user?.phoneNumber ?? '') : 'مرحباً بك',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }



  Widget _buildNavigationSection(BuildContext context, bool isLoggedIn) {
    return Column(
      children: [
        if (isLoggedIn) ...[
          ListTile(
            leading: const Icon(Icons.monetization_on),
            title: const Text('توريد جديد'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CompanySelectionScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.phone_android),
            title: const Text('شحن رصيد'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NetworkSelectionScreen(),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.history),
            title: const Text('سجل المعاملات'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TransactionHistoryScreen(),
                ),
              );
            },
          ),
        ],
      ],
    );
  }

  Widget _buildSettingsSection(BuildContext context) {
    return Column(
      children: [
        Consumer<ThemeProvider>(
          builder: (context, themeProvider, child) {
            return ListTile(
              leading: Icon(
                themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              ),
              title: Text(
                themeProvider.isDarkMode ? 'الوضع النهاري' : 'الوضع الليلي',
              ),
              trailing: Switch(
                value: themeProvider.isDarkMode,
                onChanged: (value) {
                  themeProvider.toggleTheme();
                  Navigator.pop(context); // إغلاق القائمة
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: value ? Colors.black : Colors.white, // لون الأيقونة حسب الوضع
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              value ? 'تم تفعيل الوضع الليلي' : 'تم تفعيل الوضع النهاري',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: value ? Colors.black : Colors.white, // لون النص حسب الوضع
                              ),
                            ),
                          ),
                        ],
                      ),
                      backgroundColor: value ? Colors.white : Colors.black, // خلفية بيضاء للوضع الليلي، سوداء للنهاري
                      duration: const Duration(seconds: 3),
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      margin: const EdgeInsets.all(16),
                    ),
                  );
                },
              ),
            );
          },
        ),



        // زر تحكم المسؤول مخفي تماماً
      ],
    );
  }

  Widget _buildSupportSection(BuildContext context) {
    return Column(
      children: [
        // الأسئلة الشائعة - أولاً في قسم الدعم
        ListTile(
          leading: const Icon(Icons.help_outline),
          title: const Text('الأسئلة الشائعة'),
          onTap: () {
            Navigator.pop(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const FAQScreen(isForLoggedInUsers: true),
              ),
            );
          },
        ),
        ListTile(
          leading: const Icon(Icons.support_agent),
          title: const Text('الدعم الفني'),
          onTap: () async {
            Navigator.pop(context);

            // فحص الاتصال قبل فتح واتساب
            final hasConnection = await NetworkHelper.checkConnectionWithDialog(
              context,
              title: 'يتطلب اتصال بالإنترنت',
              message: 'فتح واتساب للدعم الفني يتطلب اتصال بالإنترنت.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
            );

            if (hasConnection) {
              _launchWhatsApp();
            }
          },
        ),

      ],
    );
  }

  Widget _buildInfoSection(BuildContext context, bool isLoggedIn) {
    return Column(
      children: [
        ListTile(
          leading: const Icon(Icons.info_outline),
          title: const Text('حول التطبيق'),
          onTap: () {
            Navigator.pop(context);
            _showAboutDialog(context);
          },
        ),
        if (isLoggedIn) ...[
          ListTile(
            leading: const Icon(Icons.privacy_tip),
            title: const Text('سياسة الخصوصية'),
            onTap: () {
              Navigator.pop(context);
              _launchPrivacyPolicy(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              _showLogoutDialog(context);
            },
          ),
        ],
      ],
    );
  }

  Widget _buildDrawerFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Text(
        'الإصدار ${ProductionConfig.appVersion}',
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 12,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  void _launchWhatsApp() async {
    const message = 'مرحباً، أحتاج مساعدة في تطبيق وردلي تك';
    final whatsappUrl = 'https://wa.me/+201201937252?text=${Uri.encodeComponent(message)}';
    
    if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
      await launchUrl(Uri.parse(whatsappUrl));
    }
  }

  void _launchPrivacyPolicy(BuildContext context) async {
    // رابط سياسة الخصوصية على GitHub Pages
    const url = 'https://wardlytec.github.io/wardly-privacy-policy/';

    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);

        // عرض رسالة تأكيد
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'تم فتح سياسة الخصوصية بنجاح',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green[600],
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      } else {
        throw 'لا يمكن فتح الرابط';
      }
    } catch (e) {
      // في حالة الخطأ، عرض رسالة
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تعذر فتح سياسة الخصوصية',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    }
  }

  void _showComingSoonDialog(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(feature),
        content: const Text('هذه الميزة ستكون متاحة في التحديث القادم'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header مع زر الإغلاق
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'حول التطبيق',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    radius: 18,
                    child: IconButton(
                      icon: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 18,
                      ),
                      onPressed: () => Navigator.pop(context),
                      tooltip: 'إغلاق',
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // أيقونة التطبيق
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Image.asset(
                    'assets/images/app_logo.jpg',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.account_balance_wallet,
                        size: 64,
                        color: AppTheme.primaryColor,
                      );
                    },
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // اسم التطبيق
              Text(
                ProductionConfig.appName,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 8),

              // رقم الإصدار
              Text(
                'الإصدار: ${ProductionConfig.appVersion}',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),

              const SizedBox(height: 16),

              // وصف التطبيق
              Text(
                'تطبيق يهدف لتسهيل عمليات الدفع الالكتروني في مصر',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 14),
              ),

              const SizedBox(height: 16),

              // حقوق النشر
              Text(
                '© 2025 تطبيق وردلي تك – جميع الحقوق محفوظة',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),


            ],
          ),
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('تسجيل الخروج'),
            CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              radius: 18,
              child: IconButton(
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 18,
                ),
                onPressed: () => Navigator.pop(context),
                tooltip: 'إغلاق',
              ),
            ),
          ],
        ),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          Center(
            child: ElevatedButton.icon(
              onPressed: () async {
                Navigator.pop(context); // إغلاق الحوار
                await _performLogout(context);
              },
              icon: const Icon(Icons.logout, color: Colors.white),
              label: const Text(
                'تسجيل الخروج',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _performLogout(BuildContext context) async {
    try {
      // تسجيل الخروج من AuthProvider
      final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
      await authProvider.signOut();

      if (!context.mounted) return;

      // عرض رسالة تأكيد محسنة
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'تم تسجيل الخروج بنجاح',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green[600],
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );

      // الانتقال إلى شاشة تسجيل الدخول وحذف جميع الشاشات السابقة
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (_) => const LoginScreen()),
        (route) => false,
      );
    } catch (e) {
      if (!context.mounted) return;

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تسجيل الخروج: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }




}
