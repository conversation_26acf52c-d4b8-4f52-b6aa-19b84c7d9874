/// دالة مساعدة لتنسيق المبالغ المالية
/// تزيل .00 من الأرقام الصحيحة وتعرض الأرقام العشرية عند الحاجة
class CurrencyFormatter {
  /// تنسيق المبلغ لإزالة .00 من الأرقام الصحيحة
  static String formatAmount(double amount) {
    // إذا كان الرقم صحيح (بدون كسور عشرية)
    if (amount == amount.roundToDouble()) {
      return amount.toInt().toString();
    } else {
      // إذا كان هناك كسور عشرية، اعرضها
      return amount.toStringAsFixed(2);
    }
  }

  /// تنسيق المبلغ مع كلمة "جنيه"
  static String formatAmountWithCurrency(double amount) {
    return '${formatAmount(amount)} جنيه';
  }
}
