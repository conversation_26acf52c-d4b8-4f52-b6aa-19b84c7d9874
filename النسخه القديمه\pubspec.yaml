name: wardlytec_app
description: تطبيق يهدف لتسهيل عمليات الدفع الالكتروني في مصر

publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=2.19.0 <3.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2
  provider: ^6.0.5
  shared_preferences: ^2.1.0
  intl: ^0.20.2
  url_launcher: ^6.1.10
  supabase_flutter: ^2.5.6
  http: ^1.1.0

  google_mobile_ads: ^5.1.0
  image_picker: ^1.0.4
  permission_handler: ^11.0.1
  connectivity_plus: ^6.0.5
  photo_view: ^0.14.0
  flutter_local_notifications: ^17.2.2
  timezone: ^0.9.4


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true
  assets:
    - assets/images/
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700

# إعدادات أيقونة التطبيق (Android فقط)
flutter_launcher_icons:
  android: "launcher_icon"
  ios: false # لا نحتاج iOS حالياً
  image_path: "assets/images/app_logo.jpg"
  min_sdk_android: 21
  windows:
    generate: false
  macos:
    generate: false