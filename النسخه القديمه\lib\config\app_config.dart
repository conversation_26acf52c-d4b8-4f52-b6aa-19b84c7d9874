import 'package:flutter/foundation.dart';
import 'package:wardlytec_app/config/security_config.dart';

/// إعدادات التطبيق الآمنة
/// ⚠️ تحذير: يجب نقل القيم الحساسة إلى متغيرات البيئة في الإنتاج
/// 🔒 للأمان: أنشئ ملف .env واستخدم القيم الحقيقية
class AppConfig {
  // إعدادات Supabase
  static const String supabaseUrl = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: 'https://rtzkgoxsthhocibehzay.supabase.co',
  );

  static const String supabaseAnonKey = String.fromEnvironment(
    'SUPABASE_ANON_KEY',
    defaultValue: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ0emtnb3hzdGhob2NpYmVoemF5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5MTk5NzksImV4cCI6MjA2OTQ5NTk3OX0.bj5fSRTLXze1h0kkSveSZsEyfrkRAtoXvqeWtUikD7o',
  );

  // أرقام الاتصال
  static const String adminWhatsApp = String.fromEnvironment(
    'ADMIN_WHATSAPP',
    defaultValue: '+201201937252', // للتطوير فقط - يجب تغييره
  );

  // إعدادات الإعلانات - تجريبي حتى موافقة AdMob
  static const bool useTestAds = bool.fromEnvironment('USE_TEST_ADS', defaultValue: true); // تجريبي
  
  // معرفات الإعلانات للاختبار
  static const String testRewardedAdUnitIdAndroid = 'ca-app-pub-3940256099942544/5224354917';
  static const String testRewardedAdUnitIdIOS = 'ca-app-pub-3940256099942544/1712485313';
  
  // معرفات الإعلانات الحقيقية (يجب تحديثها)
  static const String prodRewardedAdUnitIdAndroid = String.fromEnvironment(
    'ADMOB_REWARDED_AD_UNIT_ID_ANDROID',
    defaultValue: 'ca-app-pub-9880594970188723/1686981520',
  );
  
  static const String prodRewardedAdUnitIdIOS = String.fromEnvironment(
    'ADMOB_REWARDED_AD_UNIT_ID_IOS',
    defaultValue: 'ca-app-pub-9880594970188723/1686981520',
  );

  // دوال مساعدة
  static String getRewardedAdUnitId(bool isAndroid) {
    if (useTestAds) {
      return isAndroid ? testRewardedAdUnitIdAndroid : testRewardedAdUnitIdIOS;
    } else {
      return isAndroid ? prodRewardedAdUnitIdAndroid : prodRewardedAdUnitIdIOS;
    }
  }

  // ملاحظة مهمة للمطور
  static void printAdMobStatus() {
    if (kDebugMode) {
      print('📢 حالة إعلانات AdMob:');
      print('🧪 استخدام إعلانات اختبار: $useTestAds');
      if (useTestAds) {
        print('💡 سبب استخدام إعلانات الاختبار:');
        print('   - AdMob لم يوافق على ظهور الإعلانات بعد');
        print('   - التطبيق لم يُضاف على متجر التطبيقات');
        print('   - تجنب مخالفة سياسات AdMob');
        print('🔄 للتبديل للإعلانات الحقيقية:');
        print('   1. أضف التطبيق على Google Play Store');
        print('   2. انتظر موافقة AdMob على التطبيق');
        print('   3. غير useTestAds إلى false في AppConfig');
        print('⚠️ الإعلانات التجريبية تعمل بشكل طبيعي لكن بدون أرباح حقيقية');
      }
    }
  }

  // تحذير أمني محسن
  static void printSecurityWarning() {
    if (kDebugMode) {
      print('⚠️ تحذير أمني: يتم استخدام قيم افتراضية للتطوير');
      print('🔒 في الإنتاج، يجب استخدام متغيرات البيئة لحماية المفاتيح الحساسة');

      // استخدام إعدادات الأمان المحسنة
      SecurityConfig.printSecurityWarnings();
      SecurityConfig.validateSecuritySettings();
    }
  }
}
