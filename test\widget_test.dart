import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:wardlytec_app/main.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/providers/transaction_provider.dart';
import 'package:wardlytec_app/widgets/single_tap_button.dart';

void main() {
  group('Wardly App Widget Tests', () {
    testWidgets('App should start with splash screen', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => SupabaseAuthProvider()),
            ChangeNotifierProvider(create: (_) => TransactionProvider()),
          ],
          child: const MyApp(),
        ),
      );

      // Verify that splash screen is displayed
      expect(find.text('وردلي'), findsOneWidget);
      expect(find.text('تك'), findsOneWidget);
      expect(find.text('لتسهيل التوريد وشحن الرصيد'), findsOneWidget);
    });

    testWidgets('Splash screen should show loading indicator', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => SupabaseAuthProvider()),
            ChangeNotifierProvider(create: (_) => TransactionProvider()),
          ],
          child: const MyApp(),
        ),
      );

      // Verify loading indicator is present
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('App should handle SingleTapButton correctly', (WidgetTester tester) async {
      // اختبار أن التطبيق يتعامل مع SingleTapButton بشكل صحيح
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleTapButton(
              onPressed: () {},
              child: const Text('اختبار'),
            ),
          ),
        ),
      );

      expect(find.text('اختبار'), findsOneWidget);
      expect(find.byType(SingleTapButton), findsOneWidget);
    });

    testWidgets('Test app theme and Arabic support', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => SupabaseAuthProvider()),
            ChangeNotifierProvider(create: (_) => TransactionProvider()),
          ],
          child: const MyApp(),
        ),
      );

      // Get the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      
      // Verify Arabic locale
      expect(materialApp.locale, const Locale('ar', 'EG'));
      
      // Verify app title
      expect(materialApp.title, 'وردلي تك');
    });
  });

  // تم إزالة اختبارات SimpleTestApp لأنها غير موجودة في الكود الحالي
}
