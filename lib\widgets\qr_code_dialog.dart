import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class QrCodeDialog extends StatelessWidget {
  final String data;
  final String title;
  final String? walletType;

  const QrCodeDialog({
    Key? key,
    required this.data,
    required this.title,
    this.walletType,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  Icons.qr_code,
                  color: _getWalletColor(),
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _getWalletColor(),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  tooltip: 'إغلاق',
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // صورة QR Code
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _getWalletColor().withOpacity(0.3),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: _getWalletColor().withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _buildQrCodeImage(),
            ),
            
            const SizedBox(height: 20),
            
            // النص المعروض
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade800
                    : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _getWalletColor().withOpacity(0.3),
                ),
              ),
              child: Text(
                data,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
                textAlign: TextAlign.center,
                textDirection: walletType == 'Instapay' ? TextDirection.rtl : TextDirection.ltr,
              ),
            ),
            
            const SizedBox(height: 20),
            
            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _copyToClipboard(context),
                    icon: const Icon(Icons.copy),
                    label: const Text('نسخ'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _getWalletColor(),
                      side: BorderSide(color: _getWalletColor()),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _shareQrCode(context),
                    icon: const Icon(Icons.share),
                    label: const Text('مشاركة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getWalletColor(),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // نص توضيحي
            Text(
              'امسح الكود أو انسخ النص للاستخدام',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQrCodeImage() {
    // إذا كان هناك صورة QR محفوظة، استخدمها
    final imagePath = _getQrImagePath();
    if (imagePath != null) {
      return Image.asset(
        imagePath,
        width: 200,
        height: 200,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return _buildFallbackQrCode();
        },
      );
    }
    
    return _buildFallbackQrCode();
  }

  Widget _buildFallbackQrCode() {
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.qr_code,
            size: 80,
            color: _getWalletColor(),
          ),
          const SizedBox(height: 8),
          Text(
            'QR Code',
            style: TextStyle(
              color: _getWalletColor(),
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String? _getQrImagePath() {
    if (walletType == null) return null;
    
    switch (walletType!.toLowerCase()) {
      case 'vodafone cash':
        return 'assets/images/فودافون كاش.jpg';
      case 'etisalat cash':
        return 'assets/images/اتصالات كاش.jpg';
      case 'orange cash':
        return 'assets/images/اورنج كاش.jpg';
      case 'instapay':
        return 'assets/images/انستاباي.jpg';
      default:
        return null;
    }
  }

  Color _getWalletColor() {
    if (walletType == null) return Colors.blue;
    
    switch (walletType!.toLowerCase()) {
      case 'vodafone cash':
        return Colors.red;
      case 'etisalat cash':
        return Colors.orange;
      case 'orange cash':
        return const Color(0xFFFF6600);
      case 'instapay':
        return Colors.blue;
      default:
        return Colors.green;
    }
  }

  Future<void> _copyToClipboard(BuildContext context) async {
    await Clipboard.setData(ClipboardData(text: data));
    
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text(
                walletType == 'Instapay' 
                    ? 'تم نسخ عنوان إنستاباي بنجاح'
                    : 'تم نسخ رقم المحفظة بنجاح',
              ),
            ],
          ),
          backgroundColor: _getWalletColor(),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  Future<void> _shareQrCode(BuildContext context) async {
    // يمكن إضافة مشاركة الصورة لاحقاً
    await _copyToClipboard(context);
  }

  static void show(
    BuildContext context, {
    required String data,
    required String title,
    String? walletType,
  }) {
    showDialog(
      context: context,
      builder: (context) => QrCodeDialog(
        data: data,
        title: title,
        walletType: walletType,
      ),
    );
  }
}
