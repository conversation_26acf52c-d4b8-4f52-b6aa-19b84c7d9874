# 🔐 دليل الدخول المخفي لصفحة تحكم المسؤول

## 📋 نظرة عامة

تم تصميم نظام دخول مخفي لصفحة تحكم المسؤول لضمان عدم اكتشاف المستخدمين العاديين لوجود هذه الصفحة. يوجد **طريقة واحدة فقط** للدخول كمسؤول.

---

## 🎯 طريقة الدخول الوحيدة

### **الضغط على عنوان التطبيق** ⭐

1. **المكان**: الصفحة الرئيسية للتطبيق
2. **الطريقة**: اضغط على عنوان "وردلي تك" في شريط التطبيق العلوي **7 مرات متتالية** خلال 3 ثوان
3. **النتيجة**: سيظهر حوار تسجيل دخول المسؤول مباشرة

```
الصفحة الرئيسية → اضغط على "وردلي تك" 7 مرات → أدخل PIN (123456)
```

---

## 🔑 معلومات تسجيل الدخول

### **رمز PIN الافتراضي**
```
123456
```

> ⚠️ **تحذير مهم**: يجب تغيير رمز PIN الافتراضي قبل النشر في الإنتاج!

### **تغيير رمز PIN**
لتغيير رمز PIN، عدّل القيمة في الملف التالي:

1. `lib/screens/home_screen.dart` - السطر 158

```dart
const String adminPin = '123456'; // غيّر هذا الرقم
```

---

## 🛡️ الأمان والحماية

### **مستويات الحماية**
1. **إخفاء تام**: لا توجد أي إشارة مرئية لوجود صفحة المسؤول
2. **طريقة واحدة فقط**: طريقة واحدة مخفية للدخول
3. **حماية برمز PIN**: حتى لو اكتُشفت الطريقة، يحتاج رمز PIN
4. **انتهاء صلاحية**: عداد الضغطات ينتهي خلال 3 ثوان

### **إعادة تعيين العداد**
- عداد الضغطات على العنوان يُعاد تعيينه تلقائياً بعد 3 ثوان
- إذا لم تكمل 7 ضغطات خلال 3 ثوان، ستحتاج للبدء من جديد

---

## 🎛️ وظائف صفحة تحكم المسؤول

### **الميزات المتاحة**
- ✅ تفعيل/تعطيل خدمة التوريد
- ✅ تفعيل/تعطيل خدمة الشحن
- ✅ تخصيص رسائل التعطيل
- ✅ تخصيص عناوين رسائل التعطيل
- ✅ عرض حالة الخدمات الحالية
- ✅ تحديث فوري للحالة

### **كيفية الاستخدام**
1. **تعطيل خدمة**: اضغط على مفتاح التبديل أو زر "تعطيل"
2. **تخصيص الرسالة**: اكتب رسالة مخصصة في الحقل المخصص
3. **حفظ التغييرات**: التغييرات تُحفظ تلقائياً عند الضغط على أي زر

---

## 🔄 آلية عمل النظام

### **عند تعطيل خدمة**
1. المستخدم يحاول الدخول للخدمة (توريد أو شحن)
2. التطبيق يفحص حالة الخدمة من قاعدة البيانات
3. إذا كانت معطلة، يُعرض صفحة "الخدمة غير متاحة"
4. المستخدم يرى الرسالة المخصصة التي حددتها

### **عند تفعيل خدمة**
1. المستخدم يدخل للخدمة بشكل طبيعي
2. لا توجد أي رسائل أو عوائق

---

## 📱 نصائح للاستخدام

### **للمسؤول**
- احفظ الطرق الثلاث للدخول في مكان آمن
- غيّر رمز PIN قبل النشر
- اختبر النظام بانتظام للتأكد من عمله

### **للمطورين**
- تأكد من تشغيل ملف SQL في قاعدة البيانات أولاً
- اختبر جميع الطرق قبل النشر
- راجع logs قاعدة البيانات لمراقبة التغييرات

---

## 🚨 استكشاف الأخطاء

### **المشاكل الشائعة**

**1. لا يظهر حوار تسجيل الدخول**
- تأكد من الضغط 7 مرات بسرعة (خلال 3 ثوان)
- تأكد من الضغط على عنوان "وردلي تك" في الصفحة الرئيسية

**2. رمز PIN لا يعمل**
- تأكد من أن الرمز هو `123456`
- تحقق من عدم وجود مسافات إضافية

**3. صفحة المسؤول لا تظهر**
- تأكد من وجود ملف `admin_control_screen.dart`
- تحقق من الـ imports في الملفات

**4. تغييرات الخدمات لا تُحفظ**
- تأكد من تشغيل ملف SQL في قاعدة البيانات
- تحقق من اتصال الإنترنت

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من هذا الدليل أولاً
2. اختبر الطرق الثلاث
3. راجع logs التطبيق
4. تواصل مع فريق التطوير

---

**آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0.0
