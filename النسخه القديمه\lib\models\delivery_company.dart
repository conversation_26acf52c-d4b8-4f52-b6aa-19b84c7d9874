/// نموذج شركات التوصيل
enum DeliveryCompany {
  talabat('طلبات', 'talabat', '🍔'),
  mrsool('مرسول', 'mrsool', '🚚');

  const DeliveryCompany(this.nameAr, this.nameEn, this.icon);

  final String nameAr;
  final String nameEn;
  final String icon;

  /// الحصول على الشركة من النص العربي
  static DeliveryCompany? fromArabicName(String name) {
    for (DeliveryCompany company in DeliveryCompany.values) {
      if (company.nameAr == name) {
        return company;
      }
    }
    return null;
  }

  /// الحصول على الشركة من النص الإنجليزي
  static DeliveryCompany? fromEnglishName(String name) {
    for (DeliveryCompany company in DeliveryCompany.values) {
      if (company.nameEn == name) {
        return company;
      }
    }
    return null;
  }

  /// الحصول على قائمة بأسماء الشركات بالعربية
  static List<String> getArabicNames() {
    return DeliveryCompany.values.map((company) => company.nameAr).toList();
  }

  /// التحقق من توفر الخدمة
  bool get isServiceAvailable {
    switch (this) {
      case DeliveryCompany.talabat:
        return true;
      case DeliveryCompany.mrsool:
        return false; // ستكون متاحة قريباً
    }
  }

  /// رسالة حالة الخدمة
  String get serviceStatusMessage {
    switch (this) {
      case DeliveryCompany.talabat:
        return '';
      case DeliveryCompany.mrsool:
        return '';
    }
  }
}
