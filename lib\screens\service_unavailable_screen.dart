import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wardlytec_app/models/service_status.dart';
import 'package:wardlytec_app/services/service_status_service.dart';
import 'package:wardlytec_app/utils/app_theme.dart';
import 'package:wardlytec_app/config/app_config.dart';

/// صفحة عرض رسالة عدم توفر الخدمة
class ServiceUnavailableScreen extends StatefulWidget {
  final ServiceStatus serviceStatus;
  final VoidCallback? onRetry;

  const ServiceUnavailableScreen({
    Key? key,
    required this.serviceStatus,
    this.onRetry,
  }) : super(key: key);

  @override
  State<ServiceUnavailableScreen> createState() => _ServiceUnavailableScreenState();
}

class _ServiceUnavailableScreenState extends State<ServiceUnavailableScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isCheckingStatus = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  /// فحص حالة الخدمة مرة أخرى
  Future<void> _checkServiceStatus() async {
    setState(() {
      _isCheckingStatus = true;
    });

    try {
      final updatedStatus = await ServiceStatusService.getServiceStatus(
        widget.serviceStatus.serviceName,
      );

      if (updatedStatus != null && updatedStatus.isEnabled) {
        // الخدمة أصبحت متاحة
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تم تفعيل ${updatedStatus.getDisplayTitle()} مرة أخرى!',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green[600],
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );

          // العودة للصفحة السابقة أو تنفيذ callback
          if (widget.onRetry != null) {
            widget.onRetry!();
          } else {
            Navigator.of(context).pop(true);
          }
        }
      } else {
        // الخدمة ما زالت معطلة
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.white,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'الخدمة ما زالت غير متاحة، يرجى المحاولة لاحقاً',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.orange[600],
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فحص حالة الخدمة: $e'),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCheckingStatus = false;
        });
      }
    }
  }

  /// فتح واتساب للتواصل مع الدعم
  Future<void> _contactSupport() async {
    try {
      final whatsappUrl = 'https://wa.me/${AppConfig.adminWhatsApp.replaceAll('+', '')}?text=${Uri.encodeComponent('مرحباً، أريد الاستفسار عن ${widget.serviceStatus.getDisplayTitle()}')}';
      
      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(Uri.parse(whatsappUrl), mode: LaunchMode.externalApplication);
      } else {
        throw 'لا يمكن فتح واتساب';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح واتساب: $e'),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// الحصول على أيقونة الخدمة
  IconData _getServiceIcon() {
    switch (widget.serviceStatus.serviceName) {
      case 'supply_transactions':
        return Icons.account_balance_wallet;
      case 'recharge_transactions':
        return Icons.phone_android;
      default:
        return Icons.error_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.serviceStatus.getDisplayTitle()),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40),

                // أيقونة الخدمة المعطلة
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(60),
                    border: Border.all(
                      color: Colors.orange.withOpacity(0.3),
                      width: 3,
                    ),
                  ),
                  child: Icon(
                    _getServiceIcon(),
                    size: 60,
                    color: Colors.orange,
                  ),
                ),

                const SizedBox(height: 32),

                // عنوان الرسالة
                Text(
                  widget.serviceStatus.getDisplayTitle(),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                // رسالة التعطيل
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.orange.withOpacity(0.3),
                    ),
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.info_outline,
                        color: Colors.orange,
                        size: 32,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        widget.serviceStatus.getDisplayMessage(),
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // أزرار العمل
                Column(
                  children: [
                    // زر فحص الحالة مرة أخرى
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isCheckingStatus ? null : _checkServiceStatus,
                        icon: _isCheckingStatus
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Icon(Icons.refresh),
                        label: Text(_isCheckingStatus ? 'جاري الفحص...' : 'فحص الحالة مرة أخرى'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // زر التواصل مع الدعم
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _contactSupport,
                        icon: const Icon(Icons.support_agent),
                        label: const Text('تواصل مع الدعم'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.green,
                          side: const BorderSide(color: Colors.green),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // زر العودة
                    SizedBox(
                      width: double.infinity,
                      child: TextButton.icon(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.arrow_back),
                        label: const Text('العودة للصفحة الرئيسية'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[600],
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                      ),
                    ),
                  ],
                ),


              ],
            ),
          ),
        ),
      ),
    );
  }
}
