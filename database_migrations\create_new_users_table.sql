-- إنشاء جدول المستخدمين الجديد من الصفر
-- Create new users table from scratch
-- 🚀 شغل هذا الكود بعد حذف الجدول يدوياً من Dashboard

-- ========================================
-- 1. إنشاء جدول المستخدمين الجديد
-- ========================================

CREATE TABLE users (
    -- المعرف الأساسي
    id BIGSERIAL PRIMARY KEY,
    
    -- معلومات المستخدم الأساسية
    phone_number VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100),
    secret_code VARCHAR(10) NOT NULL,
    
    -- تواريخ النظام
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    
    -- الحسابات المفضلة
    favorite_accounts JSONB DEFAULT '[]'::jsonb,
    
    -- نظام المكافآت الحالي
    ad_views_remaining INTEGER DEFAULT 10 CHECK (ad_views_remaining >= 0),
    recharge_ad_views_remaining INTEGER DEFAULT 10 CHECK (recharge_ad_views_remaining >= 0),
    
    -- نظام التجديد الشهري
    last_rewards_renewal TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    next_rewards_renewal TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
    renewal_count INTEGER DEFAULT 0 CHECK (renewal_count >= 0),
    
    -- إعدادات المكافآت الشهرية
    monthly_supply_rewards INTEGER DEFAULT 10 CHECK (monthly_supply_rewards > 0),
    monthly_recharge_rewards INTEGER DEFAULT 10 CHECK (monthly_recharge_rewards > 0)
);

SELECT '✅ تم إنشاء جدول المستخدمين' as "الخطوة 1";

-- ========================================
-- 2. إنشاء الفهارس الأساسية
-- ========================================

-- فهرس رقم الهاتف (فريد)
CREATE UNIQUE INDEX idx_users_phone_number ON users(phone_number);

-- فهرس تاريخ التجديد القادم (مهم للأداء)
CREATE INDEX idx_users_next_renewal ON users(next_rewards_renewal);

-- فهرس تاريخ الإنشاء
CREATE INDEX idx_users_created_at ON users(created_at);

-- فهرس الحسابات المفضلة (JSON)
CREATE INDEX idx_users_favorite_accounts ON users USING GIN(favorite_accounts);

SELECT '✅ تم إنشاء الفهارس' as "الخطوة 2";

-- ========================================
-- 3. حذف الدوال القديمة إن وجدت
-- ========================================

DROP FUNCTION IF EXISTS renew_user_rewards() CASCADE;
DROP FUNCTION IF EXISTS get_users_due_for_renewal() CASCADE;
DROP FUNCTION IF EXISTS set_initial_renewal_dates() CASCADE;

SELECT '✅ تم حذف الدوال القديمة' as "تنظيف";

-- ========================================
-- 4. إنشاء دالة التجديد
-- ========================================

CREATE FUNCTION renew_user_rewards()
RETURNS TABLE(
    user_id BIGINT,
    phone_number VARCHAR(20),
    name VARCHAR(100),
    old_supply_rewards INTEGER,
    old_recharge_rewards INTEGER,
    new_supply_rewards INTEGER,
    new_recharge_rewards INTEGER,
    renewal_date TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    UPDATE users 
    SET 
        ad_views_remaining = monthly_supply_rewards,
        recharge_ad_views_remaining = monthly_recharge_rewards,
        last_rewards_renewal = NOW(),
        next_rewards_renewal = NOW() + INTERVAL '30 days',
        renewal_count = renewal_count + 1
    WHERE next_rewards_renewal <= NOW()
    RETURNING 
        id as user_id,
        users.phone_number,
        users.name,
        (ad_views_remaining - monthly_supply_rewards) as old_supply_rewards,
        (recharge_ad_views_remaining - monthly_recharge_rewards) as old_recharge_rewards,
        monthly_supply_rewards as new_supply_rewards,
        monthly_recharge_rewards as new_recharge_rewards,
        NOW() as renewal_date;
END;
$$;

SELECT '✅ تم إنشاء دالة التجديد' as "الخطوة 4";

-- ========================================
-- 5. إنشاء دالة فحص المستحقين للتجديد
-- ========================================

CREATE FUNCTION get_users_due_for_renewal()
RETURNS TABLE(
    user_id BIGINT,
    phone_number VARCHAR(20),
    name VARCHAR(100),
    current_supply_rewards INTEGER,
    current_recharge_rewards INTEGER,
    days_overdue INTEGER,
    last_renewal TIMESTAMP WITH TIME ZONE,
    next_renewal TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id as user_id,
        u.phone_number,
        u.name,
        u.ad_views_remaining as current_supply_rewards,
        u.recharge_ad_views_remaining as current_recharge_rewards,
        EXTRACT(DAYS FROM (NOW() - u.next_rewards_renewal))::INTEGER as days_overdue,
        u.last_rewards_renewal as last_renewal,
        u.next_rewards_renewal as next_renewal
    FROM users u
    WHERE u.next_rewards_renewal <= NOW()
    ORDER BY u.next_rewards_renewal ASC;
END;
$$;

SELECT '✅ تم إنشاء دالة فحص المستحقين للتجديد' as "الخطوة 5";

-- ========================================
-- 6. إنشاء دالة للمستخدمين الجدد
-- ========================================

CREATE FUNCTION set_initial_renewal_dates()
RETURNS TRIGGER 
LANGUAGE plpgsql
AS $$
BEGIN
    -- تحديد تواريخ التجديد والمكافآت للمستخدم الجديد
    NEW.last_rewards_renewal = NOW();
    NEW.next_rewards_renewal = NOW() + INTERVAL '30 days';
    NEW.renewal_count = 0;
    NEW.monthly_supply_rewards = COALESCE(NEW.monthly_supply_rewards, 10);
    NEW.monthly_recharge_rewards = COALESCE(NEW.monthly_recharge_rewards, 10);
    NEW.ad_views_remaining = NEW.monthly_supply_rewards;
    NEW.recharge_ad_views_remaining = NEW.monthly_recharge_rewards;
    NEW.created_at = COALESCE(NEW.created_at, NOW());
    
    RETURN NEW;
END;
$$;

-- إنشاء trigger للمستخدمين الجدد
CREATE TRIGGER users_renewal_trigger
    BEFORE INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION set_initial_renewal_dates();

SELECT '✅ تم إنشاء دالة وtrigger للمستخدمين الجدد' as "الخطوة 6";

-- ========================================
-- 6. اختبار سريع للنظام
-- ========================================

-- إنشاء مستخدم تجريبي
INSERT INTO users (phone_number, name, secret_code) 
VALUES ('01999999999', 'مستخدم تجريبي', '999999');

-- فحص القيم التلقائية
SELECT 
    'اختبار المستخدم التجريبي' as "الاختبار",
    phone_number,
    name,
    ad_views_remaining,
    recharge_ad_views_remaining,
    monthly_supply_rewards,
    monthly_recharge_rewards,
    renewal_count,
    (last_rewards_renewal IS NOT NULL) as "له تاريخ تجديد أخير",
    (next_rewards_renewal > NOW()) as "له تاريخ تجديد مستقبلي"
FROM users 
WHERE phone_number = '01999999999';

-- اختبار الدوال
SELECT 
    'اختبار دالة فحص المستحقين' as "الاختبار",
    COUNT(*) as "عدد المستحقين للتجديد"
FROM get_users_due_for_renewal();

-- حذف المستخدم التجريبي
DELETE FROM users WHERE phone_number = '01999999999';

SELECT '✅ اكتملت الاختبارات بنجاح' as "الخطوة 7";

-- ========================================
-- 7. فحص النظام النهائي
-- ========================================

-- فحص هيكل الجدول
SELECT 
    'هيكل الجدول' as "النوع",
    COUNT(*) as "عدد الحقول"
FROM information_schema.columns 
WHERE table_name = 'users';

-- فحص الفهارس
SELECT 
    'الفهارس' as "النوع",
    COUNT(*) as "عدد الفهارس"
FROM pg_indexes 
WHERE tablename = 'users';

-- فحص الدوال
SELECT 
    'الدوال' as "النوع",
    routine_name as "اسم الدالة"
FROM information_schema.routines 
WHERE routine_name IN ('renew_user_rewards', 'get_users_due_for_renewal', 'set_initial_renewal_dates')
ORDER BY routine_name;

-- فحص الـ trigger
SELECT 
    'الـ Trigger' as "النوع",
    trigger_name as "اسم الـ Trigger"
FROM information_schema.triggers 
WHERE trigger_name = 'users_renewal_trigger';

-- ========================================
-- 8. رسائل النجاح النهائية
-- ========================================

SELECT '🎉 تم إنشاء جدول المستخدمين الجديد بنجاح!' as "النتيجة النهائية";
SELECT 'جميع الدوال والـ triggers والفهارس جاهزة' as "الحالة";
SELECT 'يمكنك الآن استخدام النظام في التطبيق' as "الخطوة التالية";

-- ملخص النظام الجديد
SELECT 
    '📋 ملخص النظام الجديد' as "الملخص",
    '14 حقل' as "الحقول",
    '4 فهارس' as "الفهارس",
    '3 دوال' as "الدوال",
    '1 trigger' as "الـ Triggers",
    'جاهز للاستخدام' as "الحالة";
