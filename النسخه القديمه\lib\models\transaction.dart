class Transaction {
  final String id;
  final String userId;
  final String? talabatAccountNumber; // اختياري للشحن
  final String senderWalletNumber; // ✅ جديد
  final double amount;
  final double commission;
  final double totalAmount;
  final String walletType;
  final DateTime date;
  final String status;
  final String deliveryCompany; // ✅ جديد - شركة التوصيل

  // حقول جديدة للشحن
  final String? type; // 'transaction' أو 'recharge'
  final String? phoneNumber; // رقم الهاتف للشحن
  final String? rechargeType; // نوع الشحن
  final String? network; // الشبكة
  final String? paymentMethod; // طريقة الدفع
  final String? senderWallet; // محفظة المرسل
  final bool? hasWatchedAd; // هل شاهد إعلان
  final DateTime createdAt; // وقت الإنشاء


  Transaction({
    required this.id,
    required this.userId,
    this.talabatAccountNumber, // اختياري للشحن
    required this.senderWalletNumber, // ✅ جديد
    required this.amount,
    required this.commission,
    required this.totalAmount,
    required this.walletType,
    required this.date,
    required this.status,
    this.deliveryCompany = 'طلبات', // ✅ جديد - افتراضي طلبات

    // حقول جديدة للشحن
    this.type = 'transaction', // افتراضي معاملة عادية
    this.phoneNumber,
    this.rechargeType,
    this.network,
    this.paymentMethod,
    this.senderWallet,
    this.hasWatchedAd = false,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? date;

  factory Transaction.fromMap(Map<String, dynamic> map, String docId) {
    return Transaction(
      id: docId,
      userId: (map['user_id'] ?? map['userId'] ?? '').toString(),
      talabatAccountNumber: map['talabat_account_number'] ?? map['talabatAccountNumber'],
      senderWalletNumber: map['sender_wallet_number'] ?? map['senderWalletNumber'] ?? '',
      amount: (map['amount'] ?? 0).toDouble(),
      commission: (map['commission'] ?? 0).toDouble(),
      totalAmount: (map['total_amount'] ?? map['totalAmount'] ?? 0).toDouble(),
      walletType: map['wallet_type'] ?? map['walletType'] ?? map['payment_method'] ?? '',
      date: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : (map['date'] != null ? DateTime.parse(map['date'].toString()) : DateTime.now()),
      status: map['status'] ?? 'جاري',
      deliveryCompany: map['delivery_company'] ?? map['deliveryCompany'] ?? 'طلبات', // ✅ جديد

      // حقول جديدة للشحن
      type: map['type'] ?? 'transaction',
      phoneNumber: map['phone_number'],
      rechargeType: map['recharge_type'],
      network: map['network'],
      paymentMethod: map['payment_method'],
      senderWallet: map['sender_wallet'],
      hasWatchedAd: map['has_watched_ad'] ?? false,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : (map['date'] != null ? DateTime.parse(map['date'].toString()) : DateTime.now()),
    );
  }

  // إضافة factory method جديد للـ JSON
  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction.fromMap(json, (json['id'] ?? '').toString());
  }

  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'talabat_account_number': talabatAccountNumber,
      'sender_wallet_number': senderWalletNumber,
      'amount': amount,
      'commission': commission,
      'total_amount': totalAmount,
      'wallet_type': walletType,
      'created_at': date.toIso8601String(),
      'status': status,
      'delivery_company': deliveryCompany, // ✅ جديد

      // حقول جديدة للشحن
      'type': type,
      'phone_number': phoneNumber,
      'recharge_type': rechargeType,
      'network': network,
      'payment_method': paymentMethod,
      'sender_wallet': senderWallet,
      'has_watched_ad': hasWatchedAd,
    };
  }

  Transaction copyWith({
    String? id,
    String? userId,
    String? talabatAccountNumber,
    String? senderWalletNumber, // ✅ جديد
    double? amount,
    double? commission,
    double? totalAmount,
    String? walletType,
    DateTime? date,
    String? status,
    String? deliveryCompany,

    // حقول جديدة للشحن
    String? type,
    String? phoneNumber,
    String? rechargeType,
    String? network,
    String? paymentMethod,
    String? senderWallet,
    bool? hasWatchedAd,
    DateTime? createdAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      talabatAccountNumber: talabatAccountNumber ?? this.talabatAccountNumber,
      senderWalletNumber: senderWalletNumber ?? this.senderWalletNumber, // ✅ جديد
      amount: amount ?? this.amount,
      commission: commission ?? this.commission,
      totalAmount: totalAmount ?? this.totalAmount,
      walletType: walletType ?? this.walletType,
      date: date ?? this.date,
      status: status ?? this.status,
      deliveryCompany: deliveryCompany ?? this.deliveryCompany,

      // حقول جديدة للشحن
      type: type ?? this.type,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      rechargeType: rechargeType ?? this.rechargeType,
      network: network ?? this.network,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      senderWallet: senderWallet ?? this.senderWallet,
      hasWatchedAd: hasWatchedAd ?? this.hasWatchedAd,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
