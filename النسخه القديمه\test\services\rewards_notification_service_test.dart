import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:wardlytec_app/models/user.dart';
import 'package:wardlytec_app/services/rewards_notification_service.dart';

void main() {
  group('RewardsNotificationService Tests', () {
    late UserModel testUser;

    setUp(() {
      testUser = UserModel(
        id: 1,
        phoneNumber: '***********',
        name: 'أحمد محمد',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        favoriteAccounts: [],
        adViewsRemaining: 7,
        rechargeAdViewsRemaining: 5,
        lastRewardsRenewal: DateTime.now().subtract(const Duration(days: 15)),
        nextRewardsRenewal: DateTime.now().add(const Duration(days: 15)),
        renewalCount: 2,
        monthlySupplyRewards: 10,
        monthlyRechargeRewards: 10,
      );
    });

    testWidgets('should show renewal notification for user due for renewal', (WidgetTester tester) async {
      // مستخدم مستحق للتجديد
      final dueUser = testUser.copyWith(
        nextRewardsRenewal: DateTime.now().subtract(const Duration(days: 1)),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    RewardsNotificationService.showRenewalNotification(context, dueUser);
                  },
                  child: const Text('Show Notification'),
                );
              },
            ),
          ),
        ),
      );

      // الضغط على الزر لإظهار الإشعار
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // التحقق من ظهور الإشعار
      expect(find.text('🎉 تم تجديد مكافآتك الشهرية! حصلت على 10 مرات توريد و 10 مرات شحن'), findsOneWidget);
    });





    testWidgets('should show ad watched success notification', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    RewardsNotificationService.showAdWatchedSuccessNotification(
                      context,
                      isSupplyAd: true,
                      remainingViews: 9,
                    );
                  },
                  child: const Text('Show Success'),
                );
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      expect(find.text('🎉 ممتاز! ستحصل على المبلغ كاملاً بدون خصم عمولة! المتبقي: 9 مرة'), findsOneWidget);
    });

    testWidgets('should show ad watch failed notification', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    RewardsNotificationService.showAdWatchFailedNotification(
                      context,
                      'انقطع الاتصال',
                    );
                  },
                  child: const Text('Show Failed'),
                );
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      expect(find.text('لم يتم إكمال مشاهدة الإعلان. تم إعادة تفعيل الزر - يمكنك المحاولة مرة أخرى'), findsOneWidget);
    });



    testWidgets('should not show notifications for users not due for renewal', (WidgetTester tester) async {
      // مستخدم غير مستحق للتجديد
      final notDueUser = testUser.copyWith(
        nextRewardsRenewal: DateTime.now().add(const Duration(days: 10)),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    RewardsNotificationService.checkAndShowRenewalNotification(context, notDueUser);
                  },
                  child: const Text('Check Notifications'),
                );
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // لا يجب أن تظهر أي إشعارات
      expect(find.byType(SnackBar), findsNothing);
    });


  });
}
