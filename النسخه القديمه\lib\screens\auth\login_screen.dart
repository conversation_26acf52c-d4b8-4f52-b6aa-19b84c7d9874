import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/screens/auth/secret_code_screen.dart';
import 'package:wardlytec_app/screens/auth/existing_user_login_screen.dart';
import 'package:wardlytec_app/utils/app_theme.dart';
import 'package:wardlytec_app/widgets/zoomable_image_viewer.dart';

import 'package:wardlytec_app/widgets/single_tap_button.dart';
import 'package:wardlytec_app/widgets/connectivity_wrapper.dart';
import 'package:wardlytec_app/utils/network_helper.dart';
import 'package:wardlytec_app/services/connectivity_service.dart';
import 'package:wardlytec_app/widgets/guest_drawer.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  Timer? _countdownTimer;
  bool _isNameValid = false;
  bool _isPhoneValid = false;
  bool _isButtonDisabled = false; // لتعطيل الزر لمدة 5 ثوانٍ
  bool _showLegalCard = false; // لإظهار بطاقة الالتزام القانوني

  @override
  void initState() {
    super.initState();
    // إظهار بطاقة الالتزام القانوني بعد ثانيتين
    Timer(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showLegalCard = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  // التحقق من صحة اسم العميل
  void _validateName(String value) {
    setState(() {
      // التحقق من أن الاسم يحتوي على حروف فقط وأكثر من حرفين
      final nameRegex = RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$'); // حروف عربية وإنجليزية ومسافات
      _isNameValid = value.trim().length >= 2 && nameRegex.hasMatch(value.trim());
    });
  }

  // التحقق من صحة رقم الهاتف
  void _validatePhone(String value) {
    setState(() {
      // التحقق من أن الرقم يحتوي على 11 رقم ويبدأ بـ 01
      _isPhoneValid = value.length == 11 && 
                     value.startsWith('01') && 
                     RegExp(r'^[0-9]+$').hasMatch(value);
    });
  }

  // التحقق من صحة النموذج
  bool get _isFormValid => _isNameValid && _isPhoneValid;

  // إنشاء رمز سري عشوائي
  String _generateSecretCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString(); // رقم من 6 خانات
  }



  // تعطيل الزر لمدة 5 ثوانٍ
  void _disableButtonTemporarily() {
    setState(() {
      _isButtonDisabled = true;
    });

    Timer(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _isButtonDisabled = false;
        });
      }
    });
  }

  // التحقق من وجود رقم الهاتف
  Future<void> _requestAccountActivation(BuildContext context) async {
    // تعطيل الزر فوراً لمدة 5 ثوانٍ
    _disableButtonTemporarily();

    if (!_formKey.currentState!.validate()) {
      return;
    }

    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
    
    // التحقق من الاتصال بالإنترنت
    try {
      if (!await NetworkHelper.checkConnectionBeforeAction(context)) {
        return;
      }
    } catch (e) {
      print('خطأ في فحص الاتصال: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطأ في فحص الاتصال بالإنترنت'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }
    }

    final String name = _nameController.text.trim();
    final String phone = _phoneController.text.trim();
    final String fullPhoneNumber = '+2$phone';

    if (name.isEmpty || phone.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('⚠️ يرجى ملء جميع الحقول'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    // عرض رسالة تحميل أثناء فحص رقم الهاتف
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🔍 جاري التحقق من رقم الهاتف...'),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 2),
        ),
      );
    }

    // فحص وجود رقم الهاتف
    final phoneExists = await authProvider.checkPhoneNumberExists(fullPhoneNumber);

    if (phoneExists) {
      if (context.mounted) {
        // إخفاء رسالة التحميل
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        // عرض رسالة تفصيلية مع خيار الانتقال لتسجيل الدخول
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue),
                  const SizedBox(width: 8),
                  const Text('حساب موجود'),
                  const Spacer(),
                  // زر X للإغلاق
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey.shade200,
                      foregroundColor: Colors.grey.shade600,
                      padding: const EdgeInsets.all(8),
                      minimumSize: const Size(32, 32),
                    ),
                    tooltip: 'إغلاق',
                  ),
                ],
              ),
              content: Text(
                'رقم الهاتف $fullPhoneNumber مسجل بالفعل.\n\nهل تريد تسجيل الدخول بدلاً من ذلك؟',
                style: const TextStyle(fontSize: 16),
              ),
              actions: [
                // زر تسجيل الدخول في المنتصف محمي من الضغط المتعدد
                Center(
                  child: SingleTapButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const ExistingUserLoginScreen(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 2,
                    ),
                    loadingText: 'جاري التحميل...',
                    child: const Text(
                      'تسجيل الدخول',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      }
      return;
    }

    // إخفاء رسالة التحميل السابقة
    if (context.mounted) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
    }

    // التحقق من وجود طلب تفعيل نشط لنفس الرقم
    final prefs = await SharedPreferences.getInstance();
    final existingPhone = prefs.getString('temp_phone_number');
    final existingTimestamp = prefs.getInt('temp_code_timestamp');

    if (existingPhone == fullPhoneNumber && existingTimestamp != null) {
      final existingTime = DateTime.fromMillisecondsSinceEpoch(existingTimestamp);
      final timeDifference = DateTime.now().difference(existingTime);

      // إذا كان هناك طلب نشط (أقل من 10 دقائق)
      if (timeDifference.inSeconds < 600) { // 10 دقائق = 600 ثانية
        // حساب الوقت المتبقي بالثواني بدقة
        int totalRemainingSeconds = 600 - timeDifference.inSeconds; // 10 دقائق - الوقت المنقضي
        int remainingMinutes = totalRemainingSeconds ~/ 60;
        int remainingSeconds = totalRemainingSeconds % 60;

        if (context.mounted) {
          final existingName = prefs.getString('temp_customer_name') ?? name;

          // عرض Bottom Sheet تفاعلي مع عداد تنازلي
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            isDismissible: true,
            enableDrag: true,
            builder: (context) => StatefulBuilder(
              builder: (context, setModalState) {
                // إلغاء العداد السابق إذا كان موجوداً
                _countdownTimer?.cancel();

                // استخدام الوقت المحسوب مسبقاً
                int currentTotalSeconds = (remainingMinutes * 60) + remainingSeconds;

                // بدء العداد التنازلي
                if (currentTotalSeconds > 0) {
                  _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
                    // إعادة حساب الوقت المتبقي الفعلي في كل ثانية
                    final currentTime = DateTime.now();
                    final existingTime = DateTime.fromMillisecondsSinceEpoch(existingTimestamp!);
                    final actualTimeDifference = currentTime.difference(existingTime);
                    final actualRemainingSeconds = 600 - actualTimeDifference.inSeconds;

                    if (actualRemainingSeconds > 0) {
                      setModalState(() {
                        remainingMinutes = actualRemainingSeconds ~/ 60;
                        remainingSeconds = actualRemainingSeconds % 60;
                      });
                    } else {
                      timer.cancel();
                      _countdownTimer = null;
                      // إغلاق Bottom Sheet عند انتهاء الوقت
                      if (Navigator.of(context).canPop()) {
                        Navigator.of(context).pop();
                      }
                    }
                  });
                }

                return Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // مقبض السحب
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[600]
                            : Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // أيقونة رئيسية
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.orange.shade900.withValues(alpha: 0.3)
                            : Colors.orange.shade100,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.pending_actions,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.orange.shade300
                            : Colors.orange,
                        size: 40,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // العنوان
                    Text(
                      'طلب تفعيل قيد المعالجة',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.titleLarge?.color,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),

                    // الوصف
                    Text(
                      'تم إرسال طلب تفعيل لهذا الرقم مسبقاً\nيجب طلب كلمة السر من الدعم الفني على واتساب لإكمال التفعيل',
                      style: TextStyle(
                        fontSize: 16,
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),

                    // بطاقة الوقت المتبقي
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.orange.shade900.withValues(alpha: 0.3)
                            : Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.orange.shade700
                              : Colors.orange.shade200,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.timer,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.orange.shade300
                                : Colors.orange.shade700,
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'الوقت المتبقي: ${remainingMinutes}:${remainingSeconds.toString().padLeft(2, '0')} دقيقة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.orange.shade300
                                  : Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 32),

                    // زر استكمال التفعيل في المنتصف
                    Center(
                      child: SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            _countdownTimer?.cancel();
                            _countdownTimer = null;
                            Navigator.of(context).pop();
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => SecretCodeScreen(
                                  phoneNumber: fullPhoneNumber,
                                  customerName: existingName,
                                ),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 18),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 3,
                          ),
                          child: const Text(
                            'طلب كلمة السر',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // مساحة إضافية للأجهزة مع notch
                    SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
                  ],
                ),
              ),
            );
              },
            ),
          );
        }
        return;
      }
    }

    // إنشاء رقم سري عشوائي
    final secretCode = _generateSecretCode();

    // حذف أي بيانات مؤقتة سابقة وحفظ البيانات الجديدة
    await prefs.remove('temp_phone_number');
    await prefs.remove('temp_secret_code');
    await prefs.remove('temp_customer_name');
    await prefs.remove('temp_code_timestamp');

    // حفظ البيانات الجديدة
    await prefs.setString('temp_phone_number', fullPhoneNumber);
    await prefs.setString('temp_secret_code', secretCode);
    await prefs.setString('temp_customer_name', name);
    await prefs.setInt('temp_code_timestamp', DateTime.now().millisecondsSinceEpoch);

    // إرسال إشعار للبوت
    await _notifyTelegramBot(name, fullPhoneNumber, secretCode);

    if (context.mounted) {
      // عرض رسالة نجاح محسنة
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'تم إرسال طلب التفعيل! يرجى طلب كلمة السر على واتساب لتفعيل الحساب',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green[600],
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );

      // الانتقال لشاشة إدخال الرمز السري
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => SecretCodeScreen(
            phoneNumber: fullPhoneNumber,
            customerName: name,
          ),
        ),
      );
    }
  }

  // إرسال إشعار للبوت
  Future<void> _notifyTelegramBot(String name, String phoneNumber, String secretCode) async {
    try {
      // إرسال لبوت تفعيل الحسابات الجديد
      const botToken = '8490725623:AAEdga2ReK32vRhGx26hFeOR82YiWHVzLlA';
      const adminChatId = '8289609318';

      final message = '''
🔔 [تفعيل حساب] طلب تفعيل جديد!

👤 الاسم: $name
📱 رقم الهاتف: $phoneNumber
🔑 الرقم السري: $secretCode
📅 الوقت: ${DateTime.now().toString().substring(0, 16)}

💬 طلب تفعيل حساب جديد من التطبيق
🎯 اضغط الزر أدناه لإرسال الرقم السري للعميل
''';

      final url = Uri.parse('https://api.telegram.org/bot$botToken/sendMessage');

      // إنشاء رسالة الرقم السري لـ WhatsApp
      final whatsappMessage = 'مرحباً، رقم التفعيل الخاص بك في تطبيق وردلي هو: $secretCode';
      final whatsappUrl = 'https://wa.me/${phoneNumber.replaceAll('+', '')}?text=${Uri.encodeComponent(whatsappMessage)}';

      // إنشاء لوحة مفاتيح مع زر WhatsApp
      final keyboard = {
        'inline_keyboard': [
          [
            {
              'text': '📱 إرسال الرقم السري',
              'url': whatsappUrl
            }
          ]
        ]
      };

      await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'chat_id': adminChatId,
          'text': message,
          'reply_markup': keyboard,
        }),
      );

      print('✅ تم إرسال إشعار التفعيل للبوت');
    } catch (e) {
      print('❌ خطأ في إرسال إشعار البوت: $e');
      // نتجاهل الخطأ ونكمل العملية
    }
  }

  // فتح واتساب
  Future<void> _launchWhatsApp(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن فتح واتساب. تأكد من تثبيت التطبيق.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('خطأ في فتح واتساب: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في فتح واتساب'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<SupabaseAuthProvider>(context);

    return ConnectivityWrapper(
      showNoInternetCard: true, // تظهر بطاقة عدم الاتصال
      showQualityIndicator: false, // إخفاء مؤشر جودة الاتصال
      requiredQuality: ActionType.normalBrowsing, // تتطلب تصفح عادي للمصادقة
      child: Scaffold(
      drawer: const GuestDrawer(),
      body: Stack(
        children: [
          SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 40),
                      // لوجو التطبيق
                      Center(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  'وردلي',
                                  style: TextStyle(
                                    fontSize: 26,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    height: 1.0,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                SizedBox(height: 13),
                                Text(
                                  'تك',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white70,
                                    height: 0.8,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 48),
                      // عنوان الشاشة
                      const Text(
                        'تسجيل الدخول',
                        style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      // وصف الشاشة
                      Text(
                        'أدخل اسمك ورقم هاتفك لتفعيل حسابك',
                        style: TextStyle(
                          fontSize: 16,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      // حقل إدخال اسم العميل
                      TextFormField(
                        key: const Key('name_field'),
                        controller: _nameController,
                        keyboardType: TextInputType.name,
                        textDirection: TextDirection.rtl,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'[\u0600-\u06FFa-zA-Z\s]')), // حروف عربية وإنجليزية ومسافات فقط
                        ],
                        decoration: const InputDecoration(
                          labelText: 'اسم العميل',
                          hintText: 'أدخل اسمك',
                          prefixIcon: Icon(Icons.person),
                        ),
                        onChanged: _validateName,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال اسم العميل';
                          }
                          if (value.trim().length < 2) {
                            return 'يجب أن يكون الاسم أكثر من حرفين';
                          }
                          final nameRegex = RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$');
                          if (!nameRegex.hasMatch(value.trim())) {
                            return 'يجب أن يحتوي الاسم على حروف فقط';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      // حقل إدخال رقم الهاتف
                      TextFormField(
                        key: const Key('phone_field'),
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        textDirection: TextDirection.ltr,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly, // أرقام فقط
                          LengthLimitingTextInputFormatter(11), // حد أقصى 11 رقم بعد +2
                        ],
                        decoration: InputDecoration(
                          labelText: 'رقم الهاتف',
                          hintText: 'أدخل رقم هاتفك مسجل في واتساب',
                          prefixIcon: const Icon(Icons.phone),
                          prefixText: '+2 ',
                          prefixStyle: TextStyle(
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          counterText: '', // إخفاء عداد الأحرف
                        ),
                        onChanged: _validatePhone,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال رقم الهاتف';
                          }
                          if (value.length != 11) {
                            return 'رقم الهاتف يجب أن يكون 11 رقم بالضبط';
                          }
                          if (!value.startsWith('01')) {
                            return 'رقم الهاتف يجب أن يبدأ بـ 01';
                          }
                          if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
                            return 'يرجى إدخال أرقام فقط';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 32),

                      // زر تفعيل الحساب - ظاهر دائماً مع فحص الاتصال
                      SingleTapButton(
                        onPressed: (_isFormValid && !authProvider.isLoading && !_isButtonDisabled) ? () => _requestAccountActivation(context) : null,
                        checkConnection: true, // فحص الاتصال قبل إنشاء الحساب
                        connectionTitle: 'يتطلب اتصال بالإنترنت',
                        connectionMessage: 'إنشاء حساب جديد يتطلب اتصال بالإنترنت لإرسال طلب التفعيل.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                        icon: (authProvider.isLoading || _isButtonDisabled)
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Icon(
                                Icons.person_add,
                                size: 24,
                                color: Colors.white,
                              ),
                        loadingText: 'جاري إنشاء الحساب...',
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isFormValid ? AppTheme.primaryColor : Colors.grey[400],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 18),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: _isFormValid ? 3 : 1,
                          minimumSize: const Size(double.infinity, 56),
                        ),
                        child: const Text(
                          'إنشاء حساب جديد',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // زر تسجيل الدخول - مفعل دائماً بنفس شكل زر تفعيل الحساب
                      SingleTapButton(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const ExistingUserLoginScreen(),
                            ),
                          );
                        },
                        checkConnection: true, // فحص الاتصال قبل تسجيل الدخول
                        connectionTitle: 'يتطلب اتصال بالإنترنت',
                        connectionMessage: 'تسجيل الدخول يتطلب اتصال بالإنترنت للتحقق من بياناتك.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                        icon: const Icon(
                          Icons.login,
                          size: 24,
                          color: Colors.white,
                        ),
                        loadingText: 'جاري التحميل...',
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 18),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 3,
                          minimumSize: const Size(double.infinity, 56),
                        ),
                        child: const Text(
                          'تسجيل الدخول',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                      ),

                      const SizedBox(height: 40),
                      // نص الشروط والأحكام
                      // ...تمت إزالة جملة الشروط والأحكام بناءً على طلب المستخدم...
                    ],
                  ),
                ),
              ),
            ),
          ),

          // الأزرار العائمة
          // زر القائمة (أعلى يمين)
          Positioned(
            top: 50,
            right: 16,
            child: Container(
              margin: const EdgeInsets.all(8.0),
              child: CircleAvatar(
                backgroundColor: Theme.of(context).colorScheme.primary,
                child: Builder(
                  builder: (BuildContext scaffoldContext) {
                    return IconButton(
                      icon: const Icon(
                        Icons.menu,
                        color: Colors.white,
                        size: 20,
                      ),
                      onPressed: () {
                        Scaffold.of(scaffoldContext).openDrawer();
                      },
                      tooltip: 'القائمة',
                    );
                  },
                ),
              ),
            ),
          ),

          // زر الدعم (أعلى يسار)
          Positioned(
            top: 50,
            left: 16,
            child: Container(
              margin: const EdgeInsets.all(8.0),
              child: CircleAvatar(
                backgroundColor: Theme.of(context).colorScheme.primary,
                child: IconButton(
                  icon: const Icon(
                    Icons.support_agent,
                    color: Colors.white,
                    size: 20,
                  ),
                  onPressed: () {
                    _launchWhatsApp('https://wa.me/+201201937252?text=مرحباً، أحتاج مساعدة في تطبيق وردلي تك');
                  },
                  tooltip: 'الدعم الفني',
                ),
              ),
            ),
          ),

          // بطاقة الالتزام القانوني
          if (_showLegalCard)
            Center(
              child: AnimatedOpacity(
                opacity: _showLegalCard ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 500),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 24),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    // خلفية صلبة غير شفافة مع تدرج لوني جميل
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: Theme.of(context).brightness == Brightness.dark
                          ? [
                              const Color(0xFF1E3A8A), // أزرق داكن صلب
                              const Color(0xFF1E40AF), // أزرق متوسط صلب
                            ]
                          : [
                              const Color(0xFFE3F2FD), // أزرق فاتح صلب
                              const Color(0xFFBBDEFB), // أزرق فاتح أكثر صلب
                            ],
                    ),
                    borderRadius: BorderRadius.circular(20), // زوايا أكثر نعومة
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.blue.withOpacity(0.4)
                            : Colors.blue.withOpacity(0.2),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                        spreadRadius: 2,
                      ),
                    ],
                    border: Border.all(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.blue.shade300
                          : Colors.blue.shade400,
                      width: 2,
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // العنوان مع أيقونة الإغلاق
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.blue.shade300.withOpacity(0.2)
                                  : Colors.blue.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.verified_user,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.blue.shade200
                                  : Colors.blue.shade700,
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'التزام قانوني كامل',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.blue.shade100
                                    : Colors.blue.shade800,
                              ),
                            ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.red.shade800.withOpacity(0.8)
                                  : Colors.red.shade100,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.close,
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.red.shade200
                                    : Colors.red.shade700,
                                size: 20,
                              ),
                              onPressed: () {
                                setState(() {
                                  _showLegalCard = false;
                                });
                              },
                              tooltip: 'إغلاق',
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      // المحتوى
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFF2D3748) // رمادي داكن صلب
                              : Colors.white, // أبيض صلب
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.blue.shade400
                                : Colors.blue.shade300,
                            width: 1.5,
                          ),
                        ),
                        child: Text(
                          'وردلي تك مش مجرد فكرة أو تطبيق، لكنه كيان قانوني رسمي شغال في مصر، ومعاه:',
                          style: TextStyle(
                            fontSize: 15,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.blue.shade100
                                : Colors.blue.shade800,
                            height: 1.5,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 12),
                      // النقاط
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? const Color(0xFF1A202C) // رمادي داكن صلب
                              : const Color(0xFFF0FDF4), // أخضر فاتح صلب
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.green.shade500
                                : Colors.green.shade300,
                            width: 1.5,
                          ),
                        ),
                        child: Column(
                          children: [
                            InkWell(
                              onTap: () => _showCommercialRegisterDialog(context),
                              borderRadius: BorderRadius.circular(10),
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: Theme.of(context).brightness == Brightness.dark
                                        ? Colors.green.shade400.withOpacity(0.3)
                                        : Colors.green.shade200,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).brightness == Brightness.dark
                                            ? Colors.green.shade700.withOpacity(0.4)
                                            : Colors.green.shade100,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Icon(
                                        Icons.receipt_long,
                                        color: Theme.of(context).brightness == Brightness.dark
                                            ? Colors.green.shade200
                                            : Colors.green.shade700,
                                        size: 18,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'سجل تجاري موثق',
                                            style: TextStyle(
                                              fontSize: 15,
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.green.shade100
                                                  : Colors.green.shade800,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Text(
                                            'اضغط للعرض والتكبير',
                                            style: TextStyle(
                                              fontSize: 11,
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.green.shade300
                                                  : Colors.green.shade600,
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Icon(
                                      Icons.check_circle,
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? Colors.green.shade300
                                          : Colors.green.shade600,
                                      size: 20,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 12),
                            InkWell(
                              onTap: () => _showTaxCardDialog(context),
                              borderRadius: BorderRadius.circular(10),
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: Theme.of(context).brightness == Brightness.dark
                                        ? Colors.orange.shade400.withOpacity(0.3)
                                        : Colors.orange.shade200,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).brightness == Brightness.dark
                                            ? Colors.orange.shade800.withOpacity(0.4)
                                            : Colors.orange.shade100,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Icon(
                                        Icons.credit_card,
                                        color: Theme.of(context).brightness == Brightness.dark
                                            ? Colors.orange.shade200
                                            : Colors.orange.shade700,
                                        size: 18,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'بطاقة ضريبية سارية',
                                            style: TextStyle(
                                              fontSize: 15,
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.orange.shade100
                                                  : Colors.orange.shade800,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Text(
                                            'اضغط للعرض والتكبير',
                                            style: TextStyle(
                                              fontSize: 11,
                                              color: Theme.of(context).brightness == Brightness.dark
                                                  ? Colors.orange.shade300
                                                  : Colors.orange.shade600,
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Icon(
                                      Icons.check_circle,
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? Colors.orange.shade300
                                          : Colors.orange.shade600,
                                      size: 20,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      // النص الختامي بتصميم تنبيه مهم
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: Theme.of(context).brightness == Brightness.dark
                                ? [
                                    const Color(0xFF6B46C1), // بنفسجي داكن صلب
                                    const Color(0xFF4338CA), // نيلي داكن صلب
                                  ]
                                : [
                                    const Color(0xFFF3E8FF), // بنفسجي فاتح صلب
                                    const Color(0xFFE0E7FF), // نيلي فاتح صلب
                                  ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.purple.shade400.withOpacity(0.6)
                                : Colors.purple.shade200,
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.purple.withOpacity(0.2)
                                  : Colors.purple.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).brightness == Brightness.dark
                                        ? Colors.purple.shade600.withOpacity(0.4)
                                        : Colors.purple.shade100,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.security,
                                    color: Theme.of(context).brightness == Brightness.dark
                                        ? Colors.purple.shade200
                                        : Colors.purple.shade700,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'وده معناه:',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).brightness == Brightness.dark
                                        ? Colors.purple.shade100
                                        : Colors.purple.shade800,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? const Color(0xFF2D3748) // رمادي داكن صلب
                                    : Colors.white, // أبيض صلب
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.purple.shade400
                                      : Colors.purple.shade200,
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                'إن أي تعامل من خلال التطبيق بيتم بشفافية كاملة، تحت إشراف قانوني، وبثقة تامة للمستخدم.',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.purple.shade100
                                      : Colors.purple.shade800,
                                  height: 1.4,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    ),
    );
  }

  // عرض صورة السجل التجاري مع إمكانية التكبير
  void _showCommercialRegisterDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return ZoomableImageViewer(
          imagePath: 'assets/images/commercial_register.jpg',
          title: 'السجل التجاري',
          subtitle: 'وثيقة رسمية معتمدة • يمكن تكبيرها',
          primaryColor: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF059669)
              : const Color(0xFF047857),
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.green.shade800
              : Colors.green.shade100,
          fallbackWidget: Container(
            padding: const EdgeInsets.all(40),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long,
                  size: 80,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.green.shade400
                      : Colors.green.shade600,
                ),
                const SizedBox(height: 20),
                Text(
                  'السجل التجاري',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.green.shade300
                        : Colors.green.shade700,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'رقم السجل: 123456789\nتاريخ الإصدار: 2024/01/01\nالنشاط: خدمات الدفع الإلكتروني',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.green.shade400
                        : Colors.green.shade600,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.green.shade800.withOpacity(0.3)
                        : Colors.green.shade100,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.verified,
                        size: 16,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.green.shade300
                            : Colors.green.shade700,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'معتمد ومُوثق',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.green.shade300
                              : Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.green.shade900.withOpacity(0.3)
                        : Colors.green.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.green.shade600
                          : Colors.green.shade200,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.green.shade300
                            : Colors.green.shade700,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'هذا السجل التجاري صادر من وزارة التجارة والصناعة المصرية ويؤكد أن وردلي تك شركة مسجلة رسمياً',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.green.shade300
                                : Colors.green.shade700,
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // عرض صورة البطاقة الضريبية مع إمكانية التكبير
  void _showTaxCardDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return ZoomableImageViewer(
          imagePath: 'assets/images/tax_card.jpg',
          title: 'البطاقة الضريبية',
          subtitle: 'وثيقة رسمية من مصلحة الضرائب • يمكن تكبيرها',
          primaryColor: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFFEA580C)
              : const Color(0xFFDC2626),
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.orange.shade800
              : Colors.orange.shade100,
          fallbackWidget: Container(
            padding: const EdgeInsets.all(40),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.credit_card,
                  size: 80,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.orange.shade400
                      : Colors.orange.shade600,
                ),
                const SizedBox(height: 20),
                Text(
                  'البطاقة الضريبية',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.orange.shade300
                        : Colors.orange.shade700,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'الرقم الضريبي: 987654321\nتاريخ الإصدار: 2024/01/01\nحالة البطاقة: سارية',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.orange.shade400
                        : Colors.orange.shade600,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.orange.shade800.withOpacity(0.3)
                        : Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.verified,
                        size: 16,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.orange.shade300
                            : Colors.orange.shade700,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'سارية ومُحدثة',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.orange.shade300
                              : Colors.orange.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.orange.shade900.withOpacity(0.3)
                        : Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.orange.shade600
                          : Colors.orange.shade200,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.orange.shade300
                            : Colors.orange.shade700,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'هذه البطاقة الضريبية صادرة من مصلحة الضرائب المصرية وتؤكد التزام الشركة بدفع الضرائب',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.orange.shade300
                                : Colors.orange.shade700,
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
