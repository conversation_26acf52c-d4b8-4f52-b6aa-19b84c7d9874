/// نموذج حالة الخدمة
class ServiceStatus {
  final String serviceName;
  final bool isEnabled;
  final String disabledMessage;
  final String customTitle;
  final DateTime updatedAt;
  final String updatedBy;

  const ServiceStatus({
    required this.serviceName,
    required this.isEnabled,
    required this.disabledMessage,
    required this.customTitle,
    required this.updatedAt,
    this.updatedBy = 'system',
  });

  /// إنشاء ServiceStatus من JSON
  factory ServiceStatus.fromJson(Map<String, dynamic> json) {
    return ServiceStatus(
      serviceName: json['service_name'] ?? '',
      isEnabled: json['is_enabled'] ?? true,
      disabledMessage: json['disabled_message'] ?? 'الخدمة غير متاحة حالياً',
      customTitle: json['custom_title'] ?? 'الخدمة غير متاحة',
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'])
          : DateTime.now(),
      updatedBy: json['updated_by'] ?? 'system',
    );
  }

  /// تحويل ServiceStatus إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'service_name': serviceName,
      'is_enabled': isEnabled,
      'disabled_message': disabledMessage,
      'custom_title': customTitle,
      'updated_at': updatedAt.toIso8601String(),
      'updated_by': updatedBy,
    };
  }

  /// إنشاء نسخة معدلة من ServiceStatus
  ServiceStatus copyWith({
    String? serviceName,
    bool? isEnabled,
    String? disabledMessage,
    String? customTitle,
    DateTime? updatedAt,
    String? updatedBy,
  }) {
    return ServiceStatus(
      serviceName: serviceName ?? this.serviceName,
      isEnabled: isEnabled ?? this.isEnabled,
      disabledMessage: disabledMessage ?? this.disabledMessage,
      customTitle: customTitle ?? this.customTitle,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  /// التحقق من المساواة
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ServiceStatus &&
        other.serviceName == serviceName &&
        other.isEnabled == isEnabled &&
        other.disabledMessage == disabledMessage &&
        other.customTitle == customTitle;
  }

  @override
  int get hashCode {
    return serviceName.hashCode ^
        isEnabled.hashCode ^
        disabledMessage.hashCode ^
        customTitle.hashCode;
  }

  @override
  String toString() {
    return 'ServiceStatus(serviceName: $serviceName, isEnabled: $isEnabled, disabledMessage: $disabledMessage, customTitle: $customTitle, updatedAt: $updatedAt, updatedBy: $updatedBy)';
  }

  /// الحصول على أيقونة الحالة
  String get statusIcon {
    return isEnabled ? '✅' : '❌';
  }

  /// الحصول على نص الحالة
  String get statusText {
    return isEnabled ? 'مفعلة' : 'معطلة';
  }

  /// الحصول على لون الحالة (للواجهة)
  String get statusColorName {
    return isEnabled ? 'green' : 'red';
  }

  /// التحقق من صحة البيانات
  bool get isValid {
    return serviceName.isNotEmpty && 
           disabledMessage.isNotEmpty && 
           customTitle.isNotEmpty;
  }

  /// الحصول على رسالة مناسبة للعرض
  String getDisplayMessage() {
    if (isEnabled) {
      return 'الخدمة متاحة حالياً';
    }
    return disabledMessage.isNotEmpty ? disabledMessage : 'الخدمة غير متاحة حالياً';
  }

  /// الحصول على عنوان مناسب للعرض
  String getDisplayTitle() {
    if (isEnabled) {
      return _getServiceDisplayName();
    }
    return customTitle.isNotEmpty ? customTitle : 'الخدمة غير متاحة';
  }

  /// الحصول على اسم الخدمة للعرض
  String _getServiceDisplayName() {
    switch (serviceName) {
      case 'supply_transactions':
        return 'خدمة التوريد';
      case 'recharge_transactions':
        return 'خدمة الشحن';
      default:
        return serviceName;
    }
  }

  /// الحصول على وصف الخدمة
  String getServiceDescription() {
    switch (serviceName) {
      case 'supply_transactions':
        return 'توريد الأموال إلى حسابات شركات التوصيل';
      case 'recharge_transactions':
        return 'شحن أرصدة الهواتف والمحافظ الإلكترونية';
      default:
        return 'خدمة غير محددة';
    }
  }

  /// الحصول على أيقونة الخدمة
  String getServiceIcon() {
    switch (serviceName) {
      case 'supply_transactions':
        return '💰';
      case 'recharge_transactions':
        return '📱';
      default:
        return '⚙️';
    }
  }
}

/// أنواع الخدمات المتاحة
enum ServiceType {
  supply('supply_transactions', 'خدمة التوريد', '💰'),
  recharge('recharge_transactions', 'خدمة الشحن', '📱');

  const ServiceType(this.value, this.displayName, this.icon);

  final String value;
  final String displayName;
  final String icon;

  /// الحصول على نوع الخدمة من القيمة
  static ServiceType? fromValue(String value) {
    for (ServiceType type in ServiceType.values) {
      if (type.value == value) {
        return type;
      }
    }
    return null;
  }

  /// الحصول على جميع أنواع الخدمات
  static List<ServiceType> get allTypes => ServiceType.values;
}

/// حالة تحديث الخدمة
class ServiceStatusUpdate {
  final String serviceName;
  final bool isEnabled;
  final String? disabledMessage;
  final String? customTitle;
  final String updatedBy;

  const ServiceStatusUpdate({
    required this.serviceName,
    required this.isEnabled,
    this.disabledMessage,
    this.customTitle,
    this.updatedBy = 'admin',
  });

  /// تحويل إلى JSON للإرسال
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'service_name': serviceName,
      'is_enabled': isEnabled,
      'updated_by': updatedBy,
    };

    if (disabledMessage != null) {
      json['disabled_message'] = disabledMessage;
    }

    if (customTitle != null) {
      json['custom_title'] = customTitle;
    }

    return json;
  }

  @override
  String toString() {
    return 'ServiceStatusUpdate(serviceName: $serviceName, isEnabled: $isEnabled, disabledMessage: $disabledMessage, customTitle: $customTitle, updatedBy: $updatedBy)';
  }
}

/// استجابة عملية تحديث حالة الخدمة
class ServiceStatusResponse {
  final bool success;
  final String message;
  final ServiceStatus? serviceStatus;
  final String? error;

  const ServiceStatusResponse({
    required this.success,
    required this.message,
    this.serviceStatus,
    this.error,
  });

  /// إنشاء استجابة نجاح
  factory ServiceStatusResponse.success({
    required String message,
    ServiceStatus? serviceStatus,
  }) {
    return ServiceStatusResponse(
      success: true,
      message: message,
      serviceStatus: serviceStatus,
    );
  }

  /// إنشاء استجابة خطأ
  factory ServiceStatusResponse.error({
    required String message,
    String? error,
  }) {
    return ServiceStatusResponse(
      success: false,
      message: message,
      error: error,
    );
  }

  @override
  String toString() {
    return 'ServiceStatusResponse(success: $success, message: $message, serviceStatus: $serviceStatus, error: $error)';
  }
}
