import 'package:flutter_test/flutter_test.dart';
import 'package:wardlytec_app/config/app_config.dart';

void main() {
  group('AppConfig Tests', () {
    test('AppConfig should have default values', () {
      expect(AppConfig.supabaseUrl, isNotEmpty);
      expect(AppConfig.supabaseAnonKey, isNotEmpty);
      expect(AppConfig.adminWhatsApp, isNotEmpty);
    });

    test('AppConfig should use test ads in debug mode', () {
      // في وضع الاختبار، useTestAds يجب أن يكون true
      expect(AppConfig.useTestAds, isTrue);
    });

    test('AppConfig should have test ad unit IDs', () {
      expect(AppConfig.testRewardedAdUnitIdAndroid, 'ca-app-pub-3940256099942544/5224354917');
      expect(AppConfig.testRewardedAdUnitIdIOS, 'ca-app-pub-3940256099942544/1712485313');
    });

    test('getRewardedAdUnitId should return test IDs in debug mode', () {
      // Android
      final androidAdId = AppConfig.getRewardedAdUnitId(true);
      expect(androidAdId, AppConfig.testRewardedAdUnitIdAndroid);

      // iOS
      final iosAdId = AppConfig.getRewardedAdUnitId(false);
      expect(iosAdId, AppConfig.testRewardedAdUnitIdIOS);
    });

    test('AppConfig should have production ad unit IDs', () {
      expect(AppConfig.prodRewardedAdUnitIdAndroid, isNotEmpty);
      expect(AppConfig.prodRewardedAdUnitIdIOS, isNotEmpty);
    });

    test('printSecurityWarning should not throw error', () {
      expect(() => AppConfig.printSecurityWarning(), returnsNormally);
    });

    test('Supabase URL should be valid format', () {
      expect(AppConfig.supabaseUrl, startsWith('https://'));
      expect(AppConfig.supabaseUrl, contains('supabase.co'));
    });

    test('Admin WhatsApp should be valid format', () {
      expect(AppConfig.adminWhatsApp, startsWith('+'));
      expect(AppConfig.adminWhatsApp.length, greaterThan(10));
    });

    test('Supabase anon key should be JWT format', () {
      final parts = AppConfig.supabaseAnonKey.split('.');
      expect(parts.length, 3); // JWT has 3 parts separated by dots
    });
  });

  group('AppConfig Environment Variables', () {
    test('Should handle missing environment variables gracefully', () {
      // هذا الاختبار يتأكد من أن التطبيق لا يتعطل عند عدم وجود متغيرات البيئة
      expect(AppConfig.supabaseUrl, isNotNull);
      expect(AppConfig.supabaseAnonKey, isNotNull);
      expect(AppConfig.adminWhatsApp, isNotNull);
    });
  });
}
