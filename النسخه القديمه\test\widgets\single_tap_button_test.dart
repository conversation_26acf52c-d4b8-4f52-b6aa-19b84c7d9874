import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:wardlytec_app/widgets/single_tap_button.dart';

void main() {
  group('🔘 SingleTapButton Tests', () {
    testWidgets('should prevent multiple taps', (WidgetTester tester) async {
      int tapCount = 0;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleTapButton(
              onPressed: () {
                tapCount++;
              },
              child: const Text('اضغط هنا'),
            ),
          ),
        ),
      );

      // الضغط الأول
      await tester.tap(find.text('اضغط هنا'));
      await tester.pump();

      // محاولة الضغط مرة أخرى أثناء التحميل
      await tester.tap(find.text('اضغط هنا'));
      await tester.pump();

      // يجب أن يتم الضغط مرة واحدة فقط
      expect(tapCount, 1);
    });

    testWidgets('should show loading state', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleTapButton(
              onPressed: () async {
                // محاكاة عملية طويلة
                await Future.delayed(const Duration(milliseconds: 100));
              },
              loadingText: 'جاري التحميل...',
              child: const Text('اضغط هنا'),
            ),
          ),
        ),
      );

      // الضغط على الزر
      await tester.tap(find.text('اضغط هنا'));
      await tester.pump();

      // التحقق من حالة التحميل
      expect(find.text('جاري التحميل...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should be disabled when enabled is false', (WidgetTester tester) async {
      int tapCount = 0;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleTapButton(
              enabled: false,
              onPressed: () {
                tapCount++;
              },
              child: const Text('اضغط هنا'),
            ),
          ),
        ),
      );

      // محاولة الضغط على الزر المعطل
      await tester.tap(find.text('اضغط هنا'));
      await tester.pump();

      // يجب ألا يتم تنفيذ العملية
      expect(tapCount, 0);
    });

    testWidgets('reactivateButton should reset loading state', (WidgetTester tester) async {
      final GlobalKey<State<SingleTapButton>> buttonKey = GlobalKey<State<SingleTapButton>>();
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleTapButton(
              key: buttonKey,
              onPressed: () async {
                // محاكاة عملية طويلة
                await Future.delayed(const Duration(seconds: 10));
              },
              loadingText: 'جاري التحميل...',
              child: const Text('اضغط هنا'),
            ),
          ),
        ),
      );

      // الضغط على الزر لبدء التحميل
      await tester.tap(find.text('اضغط هنا'));
      await tester.pump();

      // التحقق من حالة التحميل
      expect(find.text('جاري التحميل...'), findsOneWidget);

      // إعادة تفعيل الزر
      SingleTapButton.reactivateButton(buttonKey);
      await tester.pump();

      // التحقق من إعادة تفعيل الزر
      expect(find.text('اضغط هنا'), findsOneWidget);
      expect(find.text('جاري التحميل...'), findsNothing);
    });

    testWidgets('should work with icon', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleTapButton(
              onPressed: () async {
                await Future.delayed(const Duration(milliseconds: 100));
              },
              icon: const Icon(Icons.play_arrow),
              child: const Text('تشغيل'),
            ),
          ),
        ),
      );

      // الضغط على الزر
      await tester.tap(find.text('تشغيل'));
      await tester.pump();

      // التحقق من حالة التحميل مع الأيقونة
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
  });

  group('🔘 SingleTapTextButton Tests', () {
    testWidgets('should work correctly', (WidgetTester tester) async {
      int tapCount = 0;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleTapTextButton(
              onPressed: () {
                tapCount++;
              },
              child: const Text('نص'),
            ),
          ),
        ),
      );

      await tester.tap(find.text('نص'));
      await tester.pump();

      expect(tapCount, 1);
    });
  });

  group('🔘 SingleTapIconButton Tests', () {
    testWidgets('should work correctly', (WidgetTester tester) async {
      int tapCount = 0;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleTapIconButton(
              onPressed: () {
                tapCount++;
              },
              icon: const Icon(Icons.star),
            ),
          ),
        ),
      );

      await tester.tap(find.byIcon(Icons.star));
      await tester.pump();

      expect(tapCount, 1);
    });
  });
}
