import 'package:flutter/foundation.dart';
import 'package:wardlytec_app/supabase_config.dart';
import 'package:wardlytec_app/models/service_status.dart';

/// خدمة إدارة حالة الخدمات
class ServiceStatusService {
  static final _client = SupabaseConfig.client;

  /// جلب حالة خدمة معينة
  static Future<ServiceStatus?> getServiceStatus(String serviceName) async {
    try {
      if (kDebugMode) {
        print('🔍 جلب حالة الخدمة: $serviceName');
      }

      final response = await _client
          .from('service_status')
          .select('*')
          .eq('service_name', serviceName)
          .maybeSingle();

      if (response == null) {
        if (kDebugMode) {
          print('❌ لم يتم العثور على الخدمة: $serviceName');
        }
        return null;
      }

      final serviceStatus = ServiceStatus.fromJson(response);
      
      if (kDebugMode) {
        print('✅ تم جلب حالة الخدمة: ${serviceStatus.serviceName} - ${serviceStatus.statusText}');
      }

      return serviceStatus;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب حالة الخدمة $serviceName: $e');
      }
      return null;
    }
  }

  /// جلب حالة جميع الخدمات
  static Future<List<ServiceStatus>> getAllServiceStatuses() async {
    try {
      if (kDebugMode) {
        print('🔍 جلب حالة جميع الخدمات...');
      }

      final response = await _client
          .from('service_status')
          .select('*')
          .order('service_name');

      final services = response
          .map<ServiceStatus>((json) => ServiceStatus.fromJson(json))
          .toList();

      if (kDebugMode) {
        print('✅ تم جلب ${services.length} خدمة');
        for (final service in services) {
          print('   - ${service.serviceName}: ${service.statusText}');
        }
      }

      return services;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب حالة الخدمات: $e');
      }
      return [];
    }
  }

  /// تحديث حالة خدمة معينة
  static Future<ServiceStatusResponse> updateServiceStatus(
    ServiceStatusUpdate update,
  ) async {
    try {
      if (kDebugMode) {
        print('🔄 تحديث حالة الخدمة: ${update.serviceName} إلى ${update.isEnabled ? "مفعلة" : "معطلة"}');
      }

      // التحقق من وجود الخدمة أولاً
      final existingService = await getServiceStatus(update.serviceName);
      if (existingService == null) {
        return ServiceStatusResponse.error(
          message: 'الخدمة غير موجودة: ${update.serviceName}',
          error: 'SERVICE_NOT_FOUND',
        );
      }

      // تحضير البيانات للتحديث
      final updateData = <String, dynamic>{
        'is_enabled': update.isEnabled,
        'updated_by': update.updatedBy,
      };

      // إضافة الرسالة المخصصة إذا تم توفيرها
      if (update.disabledMessage != null) {
        updateData['disabled_message'] = update.disabledMessage;
      }

      // إضافة العنوان المخصص إذا تم توفيره
      if (update.customTitle != null) {
        updateData['custom_title'] = update.customTitle;
      }

      // تنفيذ التحديث
      final response = await _client
          .from('service_status')
          .update(updateData)
          .eq('service_name', update.serviceName)
          .select()
          .single();

      final updatedService = ServiceStatus.fromJson(response);

      if (kDebugMode) {
        print('✅ تم تحديث حالة الخدمة بنجاح: ${updatedService.serviceName}');
      }

      return ServiceStatusResponse.success(
        message: 'تم تحديث حالة الخدمة بنجاح',
        serviceStatus: updatedService,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث حالة الخدمة: $e');
      }

      return ServiceStatusResponse.error(
        message: 'فشل في تحديث حالة الخدمة',
        error: e.toString(),
      );
    }
  }

  /// التحقق من تفعيل خدمة التوريد
  static Future<bool> isSupplyServiceEnabled() async {
    final service = await getServiceStatus('supply_transactions');
    return service?.isEnabled ?? true; // افتراضي مفعل إذا لم توجد البيانات
  }

  /// التحقق من تفعيل خدمة الشحن
  static Future<bool> isRechargeServiceEnabled() async {
    final service = await getServiceStatus('recharge_transactions');
    return service?.isEnabled ?? true; // افتراضي مفعل إذا لم توجد البيانات
  }

  /// الحصول على رسالة تعطيل خدمة التوريد
  static Future<ServiceStatus?> getSupplyServiceStatus() async {
    return await getServiceStatus('supply_transactions');
  }

  /// الحصول على رسالة تعطيل خدمة الشحن
  static Future<ServiceStatus?> getRechargeServiceStatus() async {
    return await getServiceStatus('recharge_transactions');
  }

  /// تفعيل خدمة التوريد
  static Future<ServiceStatusResponse> enableSupplyService({
    String updatedBy = 'admin',
  }) async {
    return await updateServiceStatus(
      ServiceStatusUpdate(
        serviceName: 'supply_transactions',
        isEnabled: true,
        updatedBy: updatedBy,
      ),
    );
  }

  /// تعطيل خدمة التوريد
  static Future<ServiceStatusResponse> disableSupplyService({
    String? customMessage,
    String? customTitle,
    String updatedBy = 'admin',
  }) async {
    return await updateServiceStatus(
      ServiceStatusUpdate(
        serviceName: 'supply_transactions',
        isEnabled: false,
        disabledMessage: customMessage,
        customTitle: customTitle,
        updatedBy: updatedBy,
      ),
    );
  }

  /// تفعيل خدمة الشحن
  static Future<ServiceStatusResponse> enableRechargeService({
    String updatedBy = 'admin',
  }) async {
    return await updateServiceStatus(
      ServiceStatusUpdate(
        serviceName: 'recharge_transactions',
        isEnabled: true,
        updatedBy: updatedBy,
      ),
    );
  }

  /// تعطيل خدمة الشحن
  static Future<ServiceStatusResponse> disableRechargeService({
    String? customMessage,
    String? customTitle,
    String updatedBy = 'admin',
  }) async {
    return await updateServiceStatus(
      ServiceStatusUpdate(
        serviceName: 'recharge_transactions',
        isEnabled: false,
        disabledMessage: customMessage,
        customTitle: customTitle,
        updatedBy: updatedBy,
      ),
    );
  }

  /// الحصول على إحصائيات سريعة للخدمات
  static Future<Map<String, bool>> getServicesQuickStatus() async {
    try {
      final services = await getAllServiceStatuses();
      final status = <String, bool>{};

      for (final service in services) {
        status[service.serviceName] = service.isEnabled;
      }

      // إضافة القيم الافتراضية للخدمات المفقودة
      status.putIfAbsent('supply_transactions', () => true);
      status.putIfAbsent('recharge_transactions', () => true);

      return status;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب الحالة السريعة للخدمات: $e');
      }
      // إرجاع حالة افتراضية في حالة الخطأ
      return {
        'supply_transactions': true,
        'recharge_transactions': true,
      };
    }
  }

  /// فحص الاتصال وحالة الخدمات
  static Future<bool> checkServiceAvailability() async {
    try {
      // محاولة جلب خدمة واحدة للتأكد من الاتصال
      await _client
          .from('service_status')
          .select('service_name')
          .limit(1);
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الاتصال بخدمة حالة الخدمات: $e');
      }
      return false;
    }
  }

  /// تهيئة الخدمات الافتراضية (للاستخدام عند التثبيت الأول)
  static Future<bool> initializeDefaultServices() async {
    try {
      if (kDebugMode) {
        print('🔧 تهيئة الخدمات الافتراضية...');
      }

      final defaultServices = [
        {
          'service_name': 'supply_transactions',
          'is_enabled': true,
          'disabled_message': 'خدمة التوريد غير متاحة حالياً. نعتذر عن الإزعاج ونعمل على إعادة تفعيلها في أقرب وقت ممكن.',
          'custom_title': 'خدمة التوريد غير متاحة',
        },
        {
          'service_name': 'recharge_transactions',
          'is_enabled': true,
          'disabled_message': 'خدمة الشحن غير متاحة حالياً. نعتذر عن الإزعاج ونعمل على إعادة تفعيلها في أقرب وقت ممكن.',
          'custom_title': 'خدمة الشحن غير متاحة',
        },
      ];

      for (final serviceData in defaultServices) {
        await _client
            .from('service_status')
            .upsert(serviceData);
      }

      if (kDebugMode) {
        print('✅ تم تهيئة الخدمات الافتراضية بنجاح');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة الخدمات الافتراضية: $e');
      }
      return false;
    }
  }
}
