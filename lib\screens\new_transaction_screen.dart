import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/providers/transaction_provider.dart';
import 'package:wardlytec_app/screens/transaction_history_screen.dart';
import 'package:wardlytec_app/services/ad_manager.dart';
import 'package:wardlytec_app/services/rewards_notification_service.dart';
import 'package:wardlytec_app/services/connectivity_service.dart';
import 'package:wardlytec_app/utils/ad_connection_monitor.dart';
import 'package:wardlytec_app/widgets/connectivity_wrapper.dart';
import 'package:wardlytec_app/widgets/single_tap_button.dart';
import 'package:wardlytec_app/widgets/qr_code_dialog.dart';
import 'package:wardlytec_app/utils/app_theme.dart';
import 'package:wardlytec_app/utils/network_helper.dart';
import 'package:wardlytec_app/models/delivery_company.dart';

// إضافة typedef للوصول إلى State الصحيح
typedef SingleTapButtonState = State<SingleTapButton>;




class NewTransactionScreen extends StatefulWidget {
  final DeliveryCompany? selectedCompany;

  const NewTransactionScreen({
    Key? key,
    this.selectedCompany,
  }) : super(key: key);

  @override
  State<NewTransactionScreen> createState() => _NewTransactionScreenState();
}

class _NewTransactionScreenState extends State<NewTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountController = TextEditingController();
  final _amountController = TextEditingController();
  final _senderWalletController = TextEditingController();
  final _instapayNameController = TextEditingController();

  String? _selectedWallet; // لا توجد محفظة محددة افتراضياً
  String? _selectedBank; // لا يوجد بنك محدد افتراضياً
  double _amount = 0;
  double _commission = 0;
  double _totalAmount = 0;
  bool _hasWatchedAd = false;
  bool _hasWatchedAdInSession = false; // لتتبع مشاهدة الإعلان في الجلسة الحالية
  static String? _sessionId; // معرف فريد للجلسة
  final GlobalKey<State<SingleTapButton>> _adButtonKey = GlobalKey<State<SingleTapButton>>(); // مفتاح زر الإعلان
  bool _isWalletSelectorExpanded = false;
  bool _isAtmSelectorExpanded = false;

  // 📸 متغيرات الصورة
  File? _proofImage;
  String? _proofImageBase64;
  final ImagePicker _imagePicker = ImagePicker();

  // 🛡️ حماية من الضغط المتكرر
  bool _isSubmitting = false;
  Timer? _debounceTimer;
  Timer? _amountDebounceTimer;

  final List<Map<String, dynamic>> _wallets = [
    {
      'name': 'Vodafone Cash',
      'displayName': 'فودافون كاش',
      'icon': Icons.phone_android,
      'color': Colors.red,
    },
    {
      'name': 'Orange Cash',
      'displayName': 'أورنج كاش',
      'icon': Icons.phone_iphone,
      'color': Colors.orange,
    },
    {
      'name': 'Etisalat Cash',
      'displayName': 'اتصالات كاش',
      'icon': Icons.smartphone,
      'color': Colors.green,
    },
    {
      'name': 'CIB Smart Wallet',
      'displayName': 'CIB Smart Wallet',
      'icon': Icons.account_balance,
      'color': Colors.blue,
    },
  ];

  final List<Map<String, dynamic>> _banks = [
    {
      'name': 'National Bank of Egypt',
      'displayName': 'البنك الأهلي المصري',
      'icon': Icons.account_balance,
      'color': Colors.red,
    },
    {
      'name': 'Banque Misr',
      'displayName': 'بنك مصر',
      'icon': Icons.account_balance,
      'color': Colors.red.shade700,
    },
    {
      'name': 'Banque du Caire',
      'displayName': 'بنك القاهرة',
      'icon': Icons.account_balance,
      'color': Colors.blue,
    },
    {
      'name': 'CIB Bank',
      'displayName': 'بنك CIB',
      'icon': Icons.account_balance,
      'color': Colors.grey.shade700,
    },
  ];

  final Map<String, String> _walletNumbers = {
    'Vodafone Cash': '***********',
    'Etisalat Cash': '***********',
    'Orange Cash': '***********',
    'CIB Smart Wallet': '***********',
    'Instapay': 'wardlytec@instapay',
  };



  // بناء واجهة اختيار المحفظة الرقمية
  Widget _buildWalletSelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade600
              : Colors.grey.shade300,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // الزر الرئيسي لاختيار المحفظة
          InkWell(
            onTap: () {
              setState(() {
                _isWalletSelectorExpanded = !_isWalletSelectorExpanded;
                _isAtmSelectorExpanded = false; // إغلاق قائمة ATM
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // أيقونة السهم
                  Icon(
                    _isWalletSelectorExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                  ),
                  const Spacer(),
                  // النص والأيقونة
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          _getSelectedWalletDisplayName(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: _selectedWallet == null
                                ? (Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey.shade400
                                    : Colors.grey.shade600)
                                : (Theme.of(context).brightness == Brightness.dark
                                    ? Theme.of(context).textTheme.bodyLarge?.color
                                    : Colors.black87),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.account_balance_wallet,
                          color: _selectedWallet == null
                              ? (Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey.shade600
                                  : Colors.grey.shade400)
                              : AppTheme.primaryColor,
                          size: 20,
                        ),
                      ],
                    ),
                  ),


                ],
              ),
            ),
          ),

          // خيارات المحافظ (قابلة للتوسيع)
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isWalletSelectorExpanded ? null : 0,
            child: _isWalletSelectorExpanded
                ? Container(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      children: [
                        GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 3,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: _wallets.length,
                      itemBuilder: (context, index) {
                        final wallet = _wallets[index];
                        final isSelected = _selectedWallet == wallet['name'];

                        return InkWell(
                          onTap: () {
                            setState(() {
                              _selectedWallet = wallet['name'];
                              _isWalletSelectorExpanded = false;
                              _selectedBank = null; // إلغاء اختيار البنك
                            });
                            _clearProofImageOnMethodChange(); // حذف الصورة عند تغيير الوسيلة
                            _updateFormState();
                          },
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: isSelected
                                    ? AppTheme.primaryColor
                                    : (Theme.of(context).brightness == Brightness.dark
                                        ? Colors.grey.shade600
                                        : Colors.grey.shade300),
                                width: isSelected ? 2 : 1,
                              ),
                              borderRadius: BorderRadius.circular(8),
                              color: isSelected
                                  ? AppTheme.primaryColor.withOpacity(0.1)
                                  : (Theme.of(context).brightness == Brightness.dark
                                      ? Theme.of(context).cardColor
                                      : Colors.white),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  wallet['icon'],
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? (wallet['color'] == Colors.red
                                          ? Colors.red.shade300
                                          : wallet['color'] == Colors.orange
                                              ? Colors.orange.shade300
                                              : wallet['color'] == Colors.green
                                                  ? Colors.green.shade300
                                                  : wallet['color'] == Colors.blue
                                                      ? Colors.blue.shade300
                                                      : wallet['color'])
                                      : wallet['color'],
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    wallet['displayName'],
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                      color: isSelected
                                          ? AppTheme.primaryColor
                                          : (Theme.of(context).brightness == Brightness.dark
                                              ? Theme.of(context).textTheme.bodyLarge?.color
                                              : Colors.black87),
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),

                    // زر إنستاباي بعرض كامل (منفصل عن المحافظ الأخرى)
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () {
                        setState(() {
                          _selectedWallet = 'Instapay';
                          _isWalletSelectorExpanded = false;
                        });
                        _clearProofImageOnMethodChange(); // حذف الصورة عند تغيير الوسيلة
                        _updateFormState();
                      },
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: _selectedWallet == 'Instapay'
                                ? AppTheme.primaryColor
                                : (Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey.shade600
                                    : Colors.grey.shade300),
                            width: _selectedWallet == 'Instapay' ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                          color: _selectedWallet == 'Instapay'
                              ? AppTheme.primaryColor.withOpacity(0.1)
                              : (Theme.of(context).brightness == Brightness.dark
                                  ? Theme.of(context).cardColor
                                  : Colors.white),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.payment,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.blue.shade300
                                  : Colors.blue.shade500,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'إنستاباي',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: _selectedWallet == 'Instapay'
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: _selectedWallet == 'Instapay'
                                    ? AppTheme.primaryColor
                                    : (Theme.of(context).brightness == Brightness.dark
                                        ? Theme.of(context).textTheme.bodyLarge?.color
                                        : Colors.black87),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  // الحصول على اسم المحفظة المحددة للعرض
  String _getSelectedWalletDisplayName() {
    if (_selectedWallet == null) {
      return 'اختر المحفظة الإلكترونية';
    }

    // التحقق من إنستاباي أولاً
    if (_selectedWallet == 'Instapay') {
      return 'إنستاباي';
    }

    // البحث في المحافظ العادية
    try {
      final selectedWallet = _wallets.firstWhere(
        (wallet) => wallet['name'] == _selectedWallet,
      );
      return selectedWallet['displayName'];
    } catch (e) {
      // إذا لم يتم العثور على المحفظة في القائمة، إرجاع الاسم مباشرة
      return _selectedWallet ?? 'المحفظة الإلكترونية';
    }
  }

  // الحصول على اسم البنك المحدد للعرض
  String _getSelectedBankDisplayName() {
    if (_selectedBank == null) {
      return 'اختر البنك';
    }

    final selectedBank = _banks.firstWhere(
      (bank) => bank['name'] == _selectedBank,
      orElse: () => {'displayName': 'البنك'},
    );
    return selectedBank['displayName'] ?? 'البنك';
  }

  // بناء واجهة اختيار إيداع ATM
  Widget _buildAtmSelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade600
              : Colors.grey.shade300,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // الزر الرئيسي لاختيار إيداع ATM
          InkWell(
            onTap: () {
              setState(() {
                _isAtmSelectorExpanded = !_isAtmSelectorExpanded;
                _isWalletSelectorExpanded = false; // إغلاق قائمة المحافظ
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // أيقونة السهم
                  Icon(
                    _isAtmSelectorExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                  ),
                  const Spacer(),
                  // النص والأيقونة
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          _selectedBank == null ? 'اختر بنك' : _getSelectedBankDisplayName(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: _selectedBank == null
                                ? (Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey.shade400
                                    : Colors.grey.shade600)
                                : (Theme.of(context).brightness == Brightness.dark
                                    ? Theme.of(context).textTheme.bodyLarge?.color
                                    : Colors.black87),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.atm,
                          color: _selectedBank == null
                              ? (Theme.of(context).brightness == Brightness.dark
                                  ? Colors.grey.shade600
                                  : Colors.grey.shade400)
                              : AppTheme.primaryColor,
                          size: 20,
                        ),
                      ],
                    ),
                  ),


                ],
              ),
            ),
          ),

          // خيارات البنوك (قابلة للتوسيع)
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isAtmSelectorExpanded ? null : 0,
            child: _isAtmSelectorExpanded
                ? Container(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      children: [
                        // البنوك في شبكة 2×2
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 3,
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                          ),
                          itemCount: _banks.length,
                          itemBuilder: (context, index) {
                            final bank = _banks[index];
                            final isSelected = _selectedBank == bank['name'];

                            return InkWell(
                              onTap: () {
                                setState(() {
                                  _selectedBank = bank['name'];
                                  _isAtmSelectorExpanded = false;
                                  _selectedWallet = null; // إلغاء اختيار المحفظة
                                });
                                _clearProofImageOnMethodChange(); // حذف الصورة عند تغيير الوسيلة
                                _updateFormState();

                                // عرض رسالة أن الخدمة ستكون متاحة قريباً
                                _showBankComingSoonDialog(bank['displayName']);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: isSelected
                                        ? AppTheme.primaryColor
                                        : (Theme.of(context).brightness == Brightness.dark
                                            ? Colors.grey.shade600
                                            : Colors.grey.shade300),
                                    width: isSelected ? 2 : 1,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                  color: isSelected
                                      ? AppTheme.primaryColor.withOpacity(0.1)
                                      : (Theme.of(context).brightness == Brightness.dark
                                          ? Theme.of(context).cardColor
                                          : Colors.white),
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      bank['icon'],
                                      color: Theme.of(context).brightness == Brightness.dark
                                          ? (bank['color'] == Colors.red
                                              ? Colors.red.shade300
                                              : bank['color'] == Colors.red.shade700
                                                  ? Colors.red.shade400
                                                  : bank['color'] == Colors.blue
                                                      ? Colors.blue.shade300
                                                      : bank['color'] == Colors.grey.shade700
                                                          ? Colors.grey.shade400
                                                          : bank['color'])
                                          : bank['color'],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        bank['displayName'],
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: isSelected
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                          color: isSelected
                                              ? AppTheme.primaryColor
                                              : (Theme.of(context).brightness == Brightness.dark
                                                  ? Theme.of(context).textTheme.bodyLarge?.color
                                                  : Colors.black87),
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),


                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _amountController.addListener(_onAmountChanged);
    _accountController.addListener(_updateFormState);
    _senderWalletController.addListener(_updateFormState);
    _instapayNameController.addListener(_updateFormState);

    // تعيين القيمة الافتراضية لرقم المحفظة (فارغ)
    _senderWalletController.text = '';

    // تحميل حالة مشاهدة الإعلان في الجلسة
    _loadAdWatchedState();

    _loadAdUsageCount(); // تحميل عدد مرات الاستخدام
  }

  // تحميل عدد مرات استخدام الإعلان من قاعدة البيانات
  Future<void> _loadAdUsageCount() async {
    // في التطبيق الحقيقي، يجب تحميل هذا من Supabase أو SharedPreferences
    // هنا نقوم بمحاكاة تحميل البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    // لا حاجة لتعيين عدد المشاهدات - سيتم أخذه من المستخدم
  }

  // 📸 اختيار صورة إثبات التحويل
  Future<void> _pickProofImage() async {
    try {
      // فحص جودة الاتصال قبل رفع الصورة
      final canUpload = await NetworkHelper.canPerformAction(
        context,
        actionType: ActionType.imageUpload,
        showDialog: true,
      );

      if (!canUpload) {
        // المستخدم اختار عدم المتابعة أو الاتصال ضعيف جداً
        return;
      }

      // عرض خيارات اختيار الصورة أولاً
      final source = await _showImageSourceDialog();
      if (source == null) return;

      // طلب الأذونات حسب المصدر المختار
      bool hasPermission = false;

      if (source == ImageSource.camera) {
        final cameraPermission = await Permission.camera.request();
        hasPermission = cameraPermission.isGranted;

        if (!hasPermission) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'يجب السماح بالوصول للكاميرا',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.orange[600],
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.all(16),
              ),
            );
          }
          return;
        }
      } else {
        // للمعرض، نحتاج أذونات مختلفة حسب إصدار Android
        hasPermission = await _requestGalleryPermissions();

        if (!hasPermission) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.error_outline, color: Colors.white),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text('يجب السماح بالوصول للمعرض لاختيار الصور'),
                    ),
                  ],
                ),
                backgroundColor: Colors.red,
                action: SnackBarAction(
                  label: 'الإعدادات',
                  textColor: Colors.white,
                  onPressed: () => openAppSettings(),
                ),
              ),
            );
          }
          return;
        }
      }

      // اختيار الصورة
      try {
        final XFile? pickedFile = await _imagePicker.pickImage(
          source: source,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 80,
        );

        if (pickedFile != null) {
          final File imageFile = File(pickedFile.path);
          final List<int> imageBytes = await imageFile.readAsBytes();
          final String base64Image = base64Encode(imageBytes);

          setState(() {
            _proofImage = imageFile;
            _proofImageBase64 = base64Image;
            // تحديث حالة النموذج لتفعيل زر التحويل
          });
          _updateFormState();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'تم اختيار صورة إثبات التحويل بنجاح',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.green[600],
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.all(16),
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ خطأ في اختيار الصورة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('❌ خطأ في اختيار الصورة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ حدث خطأ في اختيار الصورة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 📸 طلب أذونات المعرض
  Future<bool> _requestGalleryPermissions() async {
    try {
      // محاولة طلب إذن الصور أولاً (Android 13+)
      PermissionStatus photosPermission = await Permission.photos.request();

      if (photosPermission.isGranted) {
        return true;
      }

      // إذا لم ينجح، نحاول إذن التخزين (للإصدارات الأقدم)
      PermissionStatus storagePermission = await Permission.storage.request();

      return storagePermission.isGranted;
    } catch (e) {
      print('خطأ في طلب أذونات المعرض: $e');
      return false;
    }
  }

  // 📸 عرض خيارات اختيار الصورة
  Future<ImageSource?> _showImageSourceDialog() async {
    return await showModalBottomSheet<ImageSource>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[600]
                    : Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'إضافة صورة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildImageSourceOption(
                  icon: Icons.camera_alt,
                  label: 'التقاط صورة',
                  onTap: () => Navigator.pop(context, ImageSource.camera),
                ),
                _buildImageSourceOption(
                  icon: Icons.photo_library,
                  label: 'من المعرض',
                  onTap: () => Navigator.pop(context, ImageSource.gallery),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // 📸 بناء خيار مصدر الصورة
  Widget _buildImageSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.blue.shade900.withValues(alpha: 0.3)
              : Colors.blue.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.blue.shade700
                : Colors.blue.shade200,
          ),
        ),
        child: Column(
          children: [
            Icon(icon, size: 40, color: AppTheme.primaryColor),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).textTheme.bodyLarge?.color
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 🗑️ حذف صورة إثبات التحويل
  void _removeProofImage() {
    setState(() {
      _proofImage = null;
      _proofImageBase64 = null;
      // تحديث حالة النموذج لتعطيل زر التحويل
    });
    _updateFormState();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.delete_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Expanded(
              child: Text(
                'تم حذف صورة إثبات التحويل',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange[600],
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  // 🔄 حذف الصورة عند تغيير وسيلة التحويل
  void _clearProofImageOnMethodChange() {
    if (_proofImage != null || _proofImageBase64 != null) {
      setState(() {
        _proofImage = null;
        _proofImageBase64 = null;
      });
    }
  }

  @override
  void dispose() {
    _accountController.dispose();
    _amountController.dispose();
    _senderWalletController.dispose();
    _instapayNameController.dispose();
    _debounceTimer?.cancel();
    _amountDebounceTimer?.cancel();

    // تنظيف مراقب الاتصال
    AdConnectionMonitor.dispose();

    super.dispose();
  }

  // 🛡️ دالة wrapper مع حماية من الضغط المتكرر
  void _handleSubmitTransaction() {
    // إلغاء أي timer سابق
    _debounceTimer?.cancel();

    // إنشاء timer جديد مع تأخير 500ms
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _submitTransaction();
    });
  }









  // دالة مع debouncing لحقل المبلغ
  void _onAmountChanged() {
    _amountDebounceTimer?.cancel();
    _amountDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _calculateCommission();
    });
  }

  void _calculateCommission() {
    final amountText = _amountController.text.trim();
    if (amountText.isEmpty) {
      setState(() {
        _amount = 0;
        _commission = 0;
        _totalAmount = 0;
      });
      return;
    }

    try {
      final requestedAmount = double.parse(amountText); // المبلغ المطلوب توريده

      // حساب العمولة: 0.5% من المبلغ بحد أقصى 5 جنيه
      double commission = requestedAmount * 0.005;

      // إذا شاهد الإعلان، العمولة = 0، وإلا الحد الأقصى 5 جنيه
      if (_hasWatchedAd) {
        commission = 0;
      } else if (commission > 5) {
        commission = 5;
      }

      // المبلغ المحول من العميل
      final transferredAmount = requestedAmount;
      // المبلغ الفعلي المورد (بعد خصم العمولة)
      final actualSuppliedAmount = requestedAmount - commission;

      setState(() {
        _amount = actualSuppliedAmount; // المبلغ الفعلي المورد (بعد خصم العمولة)
        _commission = commission;
        _totalAmount = transferredAmount; // المبلغ المحول من العميل
      });
    } catch (e) {
      setState(() {
        _amount = 0;
        _commission = 0;
        _totalAmount = 0;
      });
    }
  }

  // التحقق من صحة رقم الهاتف المصري
  bool _isValidEgyptianPhoneNumber(String phone) {
    // إزالة المسافات والرموز
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // التحقق من الطول (11 رقم)
    if (cleanPhone.length != 11) return false;

    // التحقق من أن الرقم يبدأ بالأرقام الصحيحة
    final validPrefixes = ['010', '011', '012', '015'];
    final prefix = cleanPhone.substring(0, 3);

    return validPrefixes.contains(prefix);
  }

  // تحديث حالة النموذج عند تغيير النصوص
  void _updateFormState() {
    setState(() {
      // تحديث الحالة لإعادة بناء الواجهة فقط (بدون إعادة حساب العمولة)
    });
  }

  // التحقق من صحة جميع الحقول
  bool get _isFormValid {
    // الحقول الأساسية المطلوبة دائماً
    final basicFieldsValid = _accountController.text.trim().isNotEmpty && _amount > 0;

    // التحقق من صورة إثبات التحويل فقط عند اختيار وسيلة التحويل
    final needsProofImage = _selectedWallet != null || _selectedBank != null;
    final proofImageValid = !needsProofImage || (_proofImageBase64 != null && _proofImageBase64!.isNotEmpty);

    // إذا تم اختيار محفظة إلكترونية (غير إنستاباي)
    if (_selectedWallet != null && _selectedWallet != 'Instapay') {
      final senderWallet = _senderWalletController.text.trim();
      final isSenderWalletValid = senderWallet.isNotEmpty && _isValidEgyptianPhoneNumber(senderWallet);

      return basicFieldsValid &&
             proofImageValid &&
             isSenderWalletValid;
    }

    // إذا تم اختيار بنك من ATM
    if (_selectedBank != null) {
      return basicFieldsValid && proofImageValid;
    }

    // إذا تم اختيار إنستاباي من قائمة المحافظ
    if (_selectedWallet == 'Instapay') {
      return basicFieldsValid &&
             proofImageValid &&
             _instapayNameController.text.trim().isNotEmpty;
    }

    // لا يوجد اختيار
    return false;
  }

  // إنشاء معرف فريد للجلسة
  String _getSessionId() {
    if (_sessionId == null) {
      _sessionId = 'session_${DateTime.now().millisecondsSinceEpoch}';
      _cleanupOldSessions(); // تنظيف الجلسات القديمة
    }
    return _sessionId!;
  }

  // إنشاء session ID جديد (لإعادة تعيين حالة الإعلان)
  void _generateNewSessionId() {
    _sessionId = 'session_${DateTime.now().millisecondsSinceEpoch}';
    // إعادة تحميل حالة الإعلان للجلسة الجديدة
    _loadAdWatchedState();
  }

  // تنظيف الجلسات القديمة (أكثر من 24 ساعة)
  Future<void> _cleanupOldSessions() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys();
    final now = DateTime.now().millisecondsSinceEpoch;

    for (final key in keys) {
      if (key.startsWith('ad_watched_session_')) {
        try {
          final sessionTime = int.parse(key.split('_').last);
          // حذف الجلسات الأقدم من 24 ساعة
          if (now - sessionTime > 24 * 60 * 60 * 1000) {
            await prefs.remove(key);
          }
        } catch (e) {
          // في حالة خطأ في التحليل، احذف المفتاح
          await prefs.remove(key);
        }
      }
    }
  }

  // تحميل حالة مشاهدة الإعلان في الجلسة الحالية
  Future<void> _loadAdWatchedState() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionKey = 'ad_watched_${_getSessionId()}';
    final watchedInSession = prefs.getBool(sessionKey) ?? false;

    if (mounted) {
      setState(() {
        _hasWatchedAdInSession = watchedInSession;
      });
      _updateFormState(); // إضافة تحديث حالة النموذج
    }
  }

  // حفظ حالة مشاهدة الإعلان في الجلسة الحالية
  Future<void> _saveAdWatchedState() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionKey = 'ad_watched_${_getSessionId()}';
    await prefs.setBool(sessionKey, true);

    if (mounted) {
      setState(() {
        _hasWatchedAdInSession = true;
      });
      _updateFormState(); // إضافة تحديث حالة النموذج
    }
  }

  // إعادة تعيين حالة الإعلان (للاستخدام بعد إرسال المعاملة)
  Future<void> _resetAdState() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionKey = 'ad_watched_${_getSessionId()}';
    await prefs.remove(sessionKey);

    if (mounted) {
      setState(() {
        _hasWatchedAd = false;
        _hasWatchedAdInSession = false;
      });
      _updateFormState(); // إضافة تحديث حالة النموذج
    }
  }

  // إعادة تعيين حالة الإعلان للمحاولة مرة أخرى (عند فشل الإعلان)
  void _resetAdStateForRetry() {
    if (mounted) {
      setState(() {
        _hasWatchedAdInSession = false; // السماح بالمحاولة مرة أخرى
      });

      // إعادة تفعيل زر الإعلان
      SingleTapButton.reactivateButton(_adButtonKey);
    }
  }

  // استعادة الاتصال - إعادة تفعيل زر الإعلان
  void _onConnectionRestored() async {
    if (mounted) {
      // إجبار ConnectivityService على إعادة فحص الاتصال
      final connectivityService = ConnectivityService();
      await connectivityService.checkConnection(forceCheck: true);

      // إعادة تفعيل زر الإعلان
      SingleTapButton.reactivateButton(_adButtonKey);
    }
  }

  // مشاهدة الإعلان لإلغاء العمولة
  Future<void> _watchAd() async {
    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);

    // التحقق من عدد مرات الاستخدام
    if (authProvider.currentUser == null || authProvider.currentUser!.adViewsRemaining <= 0) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.warning, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    '⚠️ لقد استنفدت عدد مرات مشاهدة الإعلان المسموحة لهذا الشهر',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.orange[600],
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
      return;
    }

    // عرض حوار تحميل الإعلان
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return PopScope(
            canPop: false,
            child: const AlertDialog(
              title: Text(
                'تحضير الإعلان',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'جاري تحضير الإعلان...',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }

    // بدء مراقبة الاتصال أثناء تحميل الإعلان
    AdConnectionMonitor.startMonitoring(
      onConnectionLost: () {
        if (mounted) {
          // إغلاق حوار التحميل
          Navigator.of(context).pop();

          // إعادة تعيين حالة الإعلان للمحاولة مرة أخرى
          _resetAdStateForRetry();

          // إظهار رسالة
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.wifi_off, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'انقطع الاتصال أثناء تحضير الإعلان. يرجى المحاولة مرة أخرى',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.orange[600],
              duration: const Duration(seconds: 4),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      },
    );



    try {
      print('🚀 بدء عرض الإعلان...');

      // التحقق من الاتصال مرة أخرى قبل عرض الإعلان
      final connectivityService = ConnectivityService();
      final isConnected = await connectivityService.checkConnection();

      if (!isConnected) {
        // إيقاف مراقبة الاتصال
        AdConnectionMonitor.stopMonitoring();

        // إغلاق حوار التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        print('❌ انقطع الاتصال أثناء تحضير الإعلان');
        if (mounted) {
          // إعادة تعيين حالة الإعلان للمحاولة مرة أخرى
          _resetAdStateForRetry();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.wifi_off, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'انقطع الاتصال أثناء تحضير الإعلان. يرجى المحاولة مرة أخرى',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.orange[600],
              duration: const Duration(seconds: 4),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
        return;
      }

      // عرض الإعلان
      final result = await AdManager.showRewardAd(context);

      // إيقاف مراقبة الاتصال
      AdConnectionMonitor.stopMonitoring();

      // إغلاق حوار التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      print('📋 نتيجة مشاهدة الإعلان: ${result ? "نجح" : "فشل"}');

      if (result) {
        print('✅ تم مشاهدة الإعلان بنجاح');
        // تم مشاهدة الإعلان بنجاح - الآن نحفظ الحالة
        await _saveAdWatchedState();

        final success = await authProvider.decrementAdViews();
        if (success && mounted) {
          setState(() {
            _hasWatchedAd = true;
          });

          // إعادة حساب العمولة
          _calculateCommission();

          final remainingUses = authProvider.currentUser?.adViewsRemaining ?? 0;
          // إظهار إشعار نجاح مشاهدة الإعلان
          RewardsNotificationService.showAdWatchedSuccessNotification(
            context,
            isSupplyAd: true,
            remainingViews: remainingUses,
          );
        }
      } else {
        // لم يتم إكمال مشاهدة الإعلان - إعادة تعيين الحالة للسماح بالمحاولة مرة أخرى
        print('❌ لم يتم إكمال مشاهدة الإعلان');
        _resetAdStateForRetry();

        RewardsNotificationService.showAdWatchFailedNotification(
          context,
          'لم يتم إكمال مشاهدة الإعلان',
        );
      }
    } catch (e) {
      // إيقاف مراقبة الاتصال في حالة الخطأ
      AdConnectionMonitor.stopMonitoring();

      // إغلاق حوار التحميل في حالة الخطأ
      if (mounted) {
        Navigator.of(context).pop();

        // إعادة تعيين حالة الإعلان للمحاولة مرة أخرى
        _resetAdStateForRetry();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'خطأ في عرض الإعلان: $e',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    }
  }



  Future<void> _submitTransaction() async {
    // 🛡️ منع الضغط المتكرر
    if (_isSubmitting) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('⏳ جاري معالجة الطلب، يرجى الانتظار...'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    if (!_formKey.currentState!.validate()) return;

    // 🛡️ تفعيل حالة الإرسال
    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

      if (authProvider.currentUser == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('يرجى تسجيل الدخول أولاً')),
          );
        }
        return;
      }

      if (_selectedWallet == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('يرجى اختيار المحفظة الإلكترونية')),
          );
        }
        return;
      }

      // 🛡️ التحقق من وجود صورة إثبات التحويل (إجباري)
      if (_proofImageBase64 == null || _proofImageBase64!.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('📸 يجب إضافة صورة إثبات التحويل'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
        return;
      }

    final success = await transactionProvider.addTransaction(
      userId: authProvider.currentUser!.id.toString(),
      talabatAccountNumber: _accountController.text.trim(),
      amount: _amount,
      walletType: _selectedWallet == 'Instapay' ? 'Instapay' : _selectedWallet!,
      senderWalletNumber: _selectedWallet == 'Instapay' ? _instapayNameController.text.trim() : _senderWalletController.text.trim(),
      commission: _commission,
      proofImageBase64: _proofImageBase64!, // صورة إثبات التحويل (مطلوبة)
      hasWatchedAd: _hasWatchedAd, // إضافة حالة مشاهدة الإعلان
    );

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'تم إرسال طلبك! سيتم مراجعته قريباً',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green[600],
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );

      // إنشاء session جديد للمعاملة التالية
      _generateNewSessionId();

      // الانتقال إلى صفحة سجل المعاملات مع تحديد نوع التوريد
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const TransactionHistoryScreen(
            initialTabIndex: 0, // تبويب التوريد
          ),
        ),
      );
    } else if (transactionProvider.error != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.error,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  transactionProvider.error!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[600],
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
      transactionProvider.resetError();
    }

    } catch (e) {
      // 🛡️ معالجة الأخطاء غير المتوقعة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ غير متوقع: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // 🛡️ إعادة تعيين حالة الإرسال في جميع الحالات
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }



  // فتح تطبيق إنستاباي
  Future<void> _openInstapay() async {
    try {
      // فتح رابط Instapay المخصص
      final Uri instapayUri = Uri.parse('https://ipn.eg/S/wardlytec/instapay/3HSwEq');

      if (await canLaunchUrl(instapayUri)) {
        await launchUrl(instapayUri, mode: LaunchMode.externalApplication);

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تم فتح رابط Instapay',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green[600],
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
        return;
      }

      // إذا فشل فتح الرابط، عرض رسالة خطأ
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.error,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'تعذر فتح رابط Instapay',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[600],
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.error,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'خطأ في فتح Instapay: $e',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[600],
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  // فتح تطبيق فودافون كاش
  Future<void> _openVodafoneCash() async {
    try {
      // فتح رابط فودافون كاش المخصص
      final Uri vodafoneUri = Uri.parse('http://vf.eg/vfcash?id=mt&qrId=v3udAu');

      if (await canLaunchUrl(vodafoneUri)) {
        await launchUrl(vodafoneUri, mode: LaunchMode.externalApplication);

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تم فتح رابط فودافون كاش',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
        return;
      }

      // إذا فشل فتح التطبيق، محاولة فتح متجر التطبيقات
      final Uri playStoreUri = Uri.parse('https://play.google.com/store/apps/details?id=com.vodafone.wallet');

      if (await canLaunchUrl(playStoreUri)) {
        await launchUrl(playStoreUri, mode: LaunchMode.externalApplication);

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.download,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تم فتح متجر التطبيقات لتحميل فودافون كاش',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.orange[600],
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
        return;
      }

      // إذا فشل كل شيء
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.error,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'تعذر فتح رابط فودافون كاش',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[600],
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.error,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'خطأ في فتح فودافون كاش: $e',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[600],
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  // فتح تطبيق اتصالات كاش
  Future<void> _openEtisalatCash() async {
    try {
      // فتح رابط اتصالات كاش المخصص
      final Uri etisalatUri = Uri.parse('https://flous.page.link/VQD9');

      if (await canLaunchUrl(etisalatUri)) {
        await launchUrl(etisalatUri, mode: LaunchMode.externalApplication);

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تم فتح رابط اتصالات كاش',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green[600],
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
        return;
      }

      // إذا فشل فتح الرابط، محاولة فتح متجر التطبيقات
      final Uri playStoreUri = Uri.parse('https://play.google.com/store/apps/details?id=com.etisalat.flous');

      if (await canLaunchUrl(playStoreUri)) {
        await launchUrl(playStoreUri, mode: LaunchMode.externalApplication);

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.download,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تم فتح متجر التطبيقات لتحميل اتصالات كاش',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.orange[600],
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
        return;
      }

      // إذا فشل كل شيء
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.error,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'تعذر فتح رابط اتصالات كاش',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[600],
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.error,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'خطأ في فتح اتصالات كاش: $e',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[600],
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  // فتح تطبيق أورنج كاش
  Future<void> _openOrangeCash() async {
    try {
      // فتح رابط أورنج كاش المخصص
      final Uri orangeUri = Uri.parse('https://backend.orange.eg/deeplinkredirect.html?screen=oci_moneytransfer%26recipientDial=***********');

      if (await canLaunchUrl(orangeUri)) {
        await launchUrl(orangeUri, mode: LaunchMode.externalApplication);

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تم فتح رابط أورنج كاش',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.orange[600],
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
        return;
      }

      // إذا فشل فتح الرابط، محاولة فتح متجر التطبيقات
      final Uri playStoreUri = Uri.parse('https://play.google.com/store/apps/details?id=com.orange.orangecash');

      if (await canLaunchUrl(playStoreUri)) {
        await launchUrl(playStoreUri, mode: LaunchMode.externalApplication);

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.download,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تم فتح متجر التطبيقات لتحميل أورنج كاش',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.orange[600],
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
        return;
      }

      // إذا فشل كل شيء
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.error,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'تعذر فتح رابط أورنج كاش',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[600],
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(
                Icons.error,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'خطأ في فتح أورنج كاش: $e',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red[600],
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  // عرض رسالة أن خدمة البنك ستكون متاحة قريباً
  void _showBankComingSoonDialog(String bankName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.orange.shade300
                    : Colors.orange.shade600,
                size: 28,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'خدمة قريباً',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // زر X للإغلاق
              IconButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {
                    _selectedBank = null; // إلغاء اختيار البنك
                  });
                  _updateFormState(); // إضافة تحديث حالة النموذج
                },
                icon: const Icon(Icons.close),
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey.shade800
                      : Colors.grey.shade200,
                  foregroundColor: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey.shade300
                      : Colors.grey.shade600,
                  padding: const EdgeInsets.all(8),
                  minimumSize: const Size(32, 32),
                ),
                tooltip: 'إغلاق',
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'خدمة إيداع ATM عبر $bankName ستكون متاحة قريباً.',
                style: const TextStyle(
                  fontSize: 16,
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.blue.shade900.withValues(alpha: 0.3)
                      : Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.blue.shade700
                        : Colors.blue.shade200,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.blue.shade300
                          : Colors.blue.shade600,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'خدمة البنوك ستكون متاحة قريباً. يمكنك استخدام المحافظ الرقمية المتاحة حالياً.',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            // زر استخدام انستا باي الوحيد
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {
                    _selectedBank = null;
                    _selectedWallet = 'Instapay'; // اختيار انستا باي
                  });
                  _updateFormState();
                },
                icon: const Icon(Icons.payment, color: Colors.white, size: 20),
                label: const Text(
                  'استخدام انستا باي',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1E88E5),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final transactionProvider = Provider.of<TransactionProvider>(context);
    final walletNumber = _selectedWallet != null ? (_walletNumbers[_selectedWallet] ?? '') : '';

    return ConnectivityWrapper(
      showNoInternetCard: true, // تظهر بطاقة عدم الاتصال
      showQualityIndicator: false, // إخفاء مؤشر جودة الاتصال
      requiredQuality: ActionType.imageUpload, // تتطلب جودة جيدة لرفع الصور
      onConnectionRestored: _onConnectionRestored, // إعادة تفعيل زر الإعلان عند استعادة الاتصال
      child: Scaffold(
      appBar: AppBar(
        title: const Text('توريد جديد'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Center(
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(40),
                    ),
                    child: const Icon(Icons.account_balance_wallet, size: 40, color: AppTheme.primaryColor),
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'توريد أموال إلى حساب طلبات',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'أدخل بيانات التوريد لإتمام العملية',
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Theme.of(context).textTheme.bodyMedium?.color
                        : Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                TextFormField(
                  controller: _accountController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  decoration: InputDecoration(
                    labelText: 'رقم حساب طلبات',
                    hintText: 'ID Talabat',
                    prefixIcon: const Icon(Icons.account_circle),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                    ),
                  ),
                  validator: (value) => value == null || value.isEmpty ? 'يرجى إدخال رقم الحساب' : null,
                ),

                const SizedBox(height: 16),
                TextFormField(
                  controller: _amountController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'المبلغ المطلوب توريده (جنيه)',
                    hintText: '1000',
                    prefixIcon: Icon(Icons.attach_money),
                  ),
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  validator: (value) {
                    final amount = double.tryParse(value ?? '');
                    if (amount == null || amount <= 0) return 'يرجى إدخال مبلغ صحيح';
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                // اختيار المحفظة الرقمية بالتصميم الجديد
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المحفظة الإلكترونية',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.bodyLarge?.color
                            : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildWalletSelector(),
                  ],
                ),
                const SizedBox(height: 16),

                // قائمة إيداع ATM
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إيداع ATM',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Theme.of(context).textTheme.bodyLarge?.color
                            : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildAtmSelector(),
                  ],
                ),
                const SizedBox(height: 16),



                // بطاقة رقم التحويل تظهر منفصلة أسفل اختيار المحفظة (مخفية لانستا باي)
                if (walletNumber.isNotEmpty) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? (_selectedWallet == 'Instapay'
                              ? Colors.blue.shade800.withOpacity(0.4)
                              : Colors.green.shade900.withOpacity(0.3))
                          : (_selectedWallet == 'Instapay'
                              ? Colors.blue.shade50
                              : Colors.green.shade50),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? (_selectedWallet == 'Instapay'
                                ? Colors.blue.shade600
                                : Colors.green.shade700)
                            : (_selectedWallet == 'Instapay'
                                ? Colors.blue.shade300
                                : Colors.green.shade300),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? (_selectedWallet == 'Instapay'
                                  ? Colors.blue.shade900.withValues(alpha: 0.2)
                                  : Colors.green.shade900.withValues(alpha: 0.2))
                              : (_selectedWallet == 'Instapay'
                                  ? Colors.blue.shade100
                                  : Colors.green.shade100),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              _selectedWallet == 'Instapay' ? Icons.payment : Icons.account_balance_wallet,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? (_selectedWallet == 'Instapay'
                                      ? Colors.blue.shade200
                                      : Colors.green.shade300)
                                  : (_selectedWallet == 'Instapay'
                                      ? Colors.blue.shade700
                                      : Colors.green.shade700),
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _selectedWallet == 'Instapay'
                                  ? 'التحويل عبر إنستاباي'
                                  : 'رقم التحويل إلى $_selectedWallet',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? (_selectedWallet == 'Instapay'
                                        ? Colors.blue.shade300
                                        : Colors.green.shade300)
                                    : (_selectedWallet == 'Instapay'
                                        ? Colors.blue.shade700
                                        : Colors.green.shade700),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Theme.of(context).cardColor
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? (_selectedWallet == 'Instapay'
                                      ? Colors.blue.shade600
                                      : Colors.green.shade600)
                                  : (_selectedWallet == 'Instapay'
                                      ? Colors.blue.shade200
                                      : Colors.green.shade200),
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  walletNumber,
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 1.5,
                                    color: Theme.of(context).brightness == Brightness.dark
                                        ? Theme.of(context).textTheme.bodyLarge?.color
                                        : Colors.black87,
                                  ),
                                  textDirection: TextDirection.ltr,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(width: 8),
                              // زر QR Code
                              Container(
                                decoration: BoxDecoration(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? (_selectedWallet == 'Instapay'
                                          ? Colors.blue.shade700
                                          : Colors.green.shade700)
                                      : (_selectedWallet == 'Instapay'
                                          ? Colors.blue.shade600
                                          : Colors.green.shade600),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  icon: const Icon(Icons.qr_code, color: Colors.white),
                                  onPressed: () {
                                    QrCodeDialog.show(
                                      context,
                                      data: walletNumber,
                                      title: _selectedWallet == 'Instapay'
                                          ? 'QR Code - إنستاباي'
                                          : 'QR Code - $_selectedWallet',
                                      walletType: _selectedWallet,
                                    );
                                  },
                                  tooltip: 'عرض QR Code',
                                ),
                              ),
                              const SizedBox(width: 8),
                              // زر النسخ
                              Container(
                                decoration: BoxDecoration(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? (_selectedWallet == 'Instapay'
                                          ? Colors.blue.shade700
                                          : Colors.green.shade700)
                                      : (_selectedWallet == 'Instapay'
                                          ? Colors.blue.shade600
                                          : Colors.green.shade600),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  icon: const Icon(Icons.copy, color: Colors.white),
                                  onPressed: () {
                                    Clipboard.setData(ClipboardData(text: walletNumber));
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Row(
                                          children: [
                                            const Icon(Icons.check_circle, color: Colors.white),
                                            const SizedBox(width: 8),
                                            Text(_selectedWallet == 'Instapay'
                                                ? 'تم نسخ عنوان إنستاباي بنجاح'
                                                : 'تم نسخ رقم المحفظة بنجاح'),
                                          ],
                                        ),
                                        backgroundColor: _selectedWallet == 'Instapay' ? Colors.blue : Colors.green,
                                        behavior: SnackBarBehavior.floating,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                      ),
                                    );
                                  },
                                  tooltip: 'نسخ الرقم',
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),

                        // زر فتح إنستاباي (يظهر فقط عند اختيار إنستاباي)
                        if (_selectedWallet == 'Instapay') ...[
                          const SizedBox(height: 8),
                          SizedBox(
                            width: double.infinity,
                            child: SingleTapButton(
                              onPressed: _openInstapay,
                              checkConnection: true,
                              connectionTitle: 'يتطلب اتصال بالإنترنت',
                              connectionMessage: 'فتح تطبيق Instapay يتطلب اتصال بالإنترنت للوصول للرابط.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                              icon: const Icon(Icons.open_in_new, color: Colors.white),
                              loadingText: 'جاري فتح Instapay...',
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF1E88E5),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Text(
                                'افتح إنستاباي',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                        ],

                        // زر فتح فودافون كاش (يظهر فقط عند اختيار فودافون كاش)
                        if (_selectedWallet == 'Vodafone Cash') ...[
                          const SizedBox(height: 8),
                          SizedBox(
                            width: double.infinity,
                            child: SingleTapButton(
                              onPressed: _openVodafoneCash,
                              checkConnection: true,
                              connectionTitle: 'يتطلب اتصال بالإنترنت',
                              connectionMessage: 'فتح تطبيق فودافون كاش قد يتطلب اتصال بالإنترنت.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                              icon: const Icon(Icons.phone_android, color: Colors.white),
                              loadingText: 'جاري فتح فودافون كاش...',
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red[600],
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Text(
                                'افتح فودافون كاش',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                        ],

                        // زر فتح اتصالات كاش (يظهر فقط عند اختيار اتصالات كاش)
                        if (_selectedWallet == 'Etisalat Cash') ...[
                          const SizedBox(height: 8),
                          SizedBox(
                            width: double.infinity,
                            child: SingleTapButton(
                              onPressed: _openEtisalatCash,
                              checkConnection: true,
                              connectionTitle: 'يتطلب اتصال بالإنترنت',
                              connectionMessage: 'فتح تطبيق اتصالات كاش يتطلب اتصال بالإنترنت للوصول للرابط.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                              icon: const Icon(Icons.smartphone, color: Colors.white),
                              loadingText: 'جاري فتح اتصالات كاش...',
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green[600],
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Text(
                                'افتح اتصالات كاش',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                        ],

                        // زر فتح أورنج كاش (يظهر فقط عند اختيار أورنج كاش)
                        if (_selectedWallet == 'Orange Cash') ...[
                          const SizedBox(height: 8),
                          SizedBox(
                            width: double.infinity,
                            child: SingleTapButton(
                              onPressed: _openOrangeCash,
                              checkConnection: true,
                              connectionTitle: 'يتطلب اتصال بالإنترنت',
                              connectionMessage: 'فتح تطبيق أورنج كاش يتطلب اتصال بالإنترنت للوصول للرابط.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                              icon: const Icon(Icons.phone_iphone, color: Colors.white),
                              loadingText: 'جاري فتح أورنج كاش...',
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange[600],
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Text(
                                'افتح أورنج كاش',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                        ],

                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? (_selectedWallet == 'Instapay'
                                      ? Colors.blue.shade300
                                      : Colors.green.shade300)
                                  : (_selectedWallet == 'Instapay'
                                      ? Colors.blue.shade600
                                      : Colors.green.shade600),
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                _selectedWallet == 'Instapay'
                                    ? 'اضغط على أيقونة النسخ لنسخ عنوان إنستاباي'
                                    : 'اضغط على أيقونة النسخ لنسخ الرقم',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? (_selectedWallet == 'Instapay'
                                          ? Colors.blue.shade300
                                          : Colors.green.shade300)
                                      : (_selectedWallet == 'Instapay'
                                          ? Colors.blue.shade600
                                          : Colors.green.shade600),
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),



                  // حقل رقم المحفظة المرسلة
                  const SizedBox(height: 16),
                  if (_selectedWallet != null && _selectedWallet != 'Instapay') ...[
                    // حقل رقم المحفظة المرسلة للمحافظ الرقمية
                    TextFormField(
                    controller: _senderWalletController,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(11),
                    ],
                    decoration: InputDecoration(
                      labelText: 'رقم المحفظة المرسلة',
                      hintText: '01012345678',
                      prefixIcon: const Icon(Icons.phone_android),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.red, width: 2),
                      ),
                      helperText: _senderWalletController.text.trim().isNotEmpty && !_isValidEgyptianPhoneNumber(_senderWalletController.text.trim())
                          ? 'رقم الهاتف يجب أن يكون 11 رقم ويبدأ بـ 010, 011, 012, أو 015'
                          : null,
                      helperStyle: const TextStyle(color: Colors.red, fontSize: 12),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال رقم المحفظة';
                      }
                      if (!_isValidEgyptianPhoneNumber(value)) {
                        return 'رقم هاتف غير صحيح';
                      }
                      return null;
                    },
                  ),
                  ] else if (_selectedWallet == 'Instapay') ...[
                    // حقل اسم صاحب حساب إنستاباي
                    TextFormField(
                      controller: _instapayNameController,
                      keyboardType: TextInputType.name,
                      textCapitalization: TextCapitalization.words,
                      decoration: InputDecoration(
                        labelText: 'اسم صاحب حساب إنستاباي',
                        hintText: 'أدخل اسمك كما هو مسجل في إنستاباي',
                        prefixIcon: const Icon(Icons.person),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اسم صاحب الحساب';
                        }
                        if (value.trim().length < 2) {
                          return 'الاسم يجب أن يكون حرفين على الأقل';
                        }
                        return null;
                      },
                    ),
                  ],
                ],


                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.blue.shade900.withValues(alpha: 0.3)
                        : Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.blue.shade700
                          : Colors.blue.shade200,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '💰 نسبة العمولة:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '• نصف جنيه على كل 100 جنيه (0.5%)',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                      Text(
                        '• أقصى عمولة: 5 جنيه فقط',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.green.shade300
                              : Colors.green,
                        ),
                      ),
                      Consumer<SupabaseAuthProvider>(
                        builder: (context, authProvider, child) {
                          final remainingViews = authProvider.currentUser?.adViewsRemaining ?? 0;
                          return Text(
                            '• مشاهدة الإعلان: $remainingViews مرة متبقية x شهر ',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: remainingViews <= 0
                                  ? (Theme.of(context).brightness == Brightness.dark
                                      ? Colors.red.shade300
                                      : Colors.red)
                                  : (Theme.of(context).brightness == Brightness.dark
                                      ? Colors.blue.shade300
                                      : Colors.blue),
                            ),
                          );
                        },
                      ),
                      Consumer<SupabaseAuthProvider>(
                        builder: (context, authProvider, child) {
                          final remainingViews = authProvider.currentUser?.adViewsRemaining ?? 0;
                          // إظهار الزر دائماً من البداية
                          return Column(
                            children: [
                      const SizedBox(height: 8),
                      const Divider(),
                      const SizedBox(height: 8),
                      SizedBox(
                        width: double.infinity,
                        child: SingleTapButton(
                          key: _adButtonKey,
                          onPressed: (_commission > 0 && remainingViews > 0 && !_hasWatchedAd && !_hasWatchedAdInSession) ? _watchAd : null,
                          checkConnection: true,
                          connectionTitle: 'يتطلب اتصال بالإنترنت',
                          connectionMessage: 'مشاهدة الإعلان تتطلب اتصال بالإنترنت لتحميل المحتوى.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                          icon: const Icon(Icons.play_circle_fill, color: Colors.white),
                          loadingText: 'جاري تحميل الإعلان...',
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            '💸 شاهد إعلان لإلغاء العمولة',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                            ],
                          );
                        },
                      ),
                      // تم إزالة قسم "لا توجد إعلانات" لأن الزر سيكون ظاهراً دائماً
                      if (_hasWatchedAd) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.green.shade900.withValues(alpha: 0.3)
                                : Colors.green.shade100,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.check_circle,
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.green.shade300
                                    : Colors.green,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '🎉 ممتاز! ستحصل على المبلغ كاملاً بدون خصم عمولة',
                                  style: TextStyle(
                                    color: Theme.of(context).brightness == Brightness.dark
                                        ? Colors.green.shade300
                                        : Colors.green,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),



                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Theme.of(context).cardColor
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('المبلغ المطلوب:'),
                          Text('${_totalAmount == _totalAmount.roundToDouble() ? _totalAmount.toInt() : _totalAmount.toStringAsFixed(2)} جنيه', style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('العمولة (0.5%):'),
                          Text('${_commission == _commission.roundToDouble() ? _commission.toInt() : _commission.toStringAsFixed(2)} جنيه',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _commission > 0
                                  ? (Theme.of(context).brightness == Brightness.dark
                                      ? Colors.red.shade300
                                      : Colors.red)
                                  : (Theme.of(context).brightness == Brightness.dark
                                      ? Colors.green.shade300
                                      : Colors.green),
                            ),
                          ),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('المبلغ المُورد:', style: TextStyle(fontWeight: FontWeight.bold)),
                          Text('${_amount == _amount.roundToDouble() ? _amount.toInt() : _amount.toStringAsFixed(2)} جنيه',
                            style: const TextStyle(fontWeight: FontWeight.bold, color: AppTheme.primaryColor, fontSize: 16),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // 📸 قسم صورة إثبات التحويل (يظهر فقط عند اختيار وسيلة التحويل)
                if (_selectedWallet != null || _selectedBank != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _proofImageBase64 == null
                        ? (Theme.of(context).brightness == Brightness.dark
                            ? Colors.red.shade900.withValues(alpha: 0.3)
                            : Colors.red.shade50)
                        : (Theme.of(context).brightness == Brightness.dark
                            ? Colors.green.shade900.withValues(alpha: 0.3)
                            : Colors.green.shade50),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _proofImageBase64 == null
                          ? (Theme.of(context).brightness == Brightness.dark
                              ? Colors.red.shade700
                              : Colors.red.shade300)
                          : (Theme.of(context).brightness == Brightness.dark
                              ? Colors.green.shade700
                              : Colors.green.shade300),
                      width: 2,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.camera_alt,
                            color: _proofImageBase64 == null
                                ? (Theme.of(context).brightness == Brightness.dark
                                    ? Colors.red.shade300
                                    : Colors.red)
                                : (Theme.of(context).brightness == Brightness.dark
                                    ? Colors.green.shade300
                                    : Colors.green),
                            size: 20
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'صورة إثبات التحويل',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'مطلوب',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      if (_proofImage == null) ...[
                        // زر اختيار الصورة
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: _pickProofImage,
                            icon: const Icon(Icons.add_a_photo, size: 20),
                            label: const Text('إضافة صورة'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.blue.shade300
                                  : Colors.blue,
                              side: BorderSide(
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.blue.shade600
                                    : Colors.blue.shade300,
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ] else ...[
                        // عرض الصورة المختارة
                        Container(
                          width: double.infinity,
                          height: 200,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.green.shade600
                                  : Colors.green.shade300,
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              _proofImage!,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.green.shade300
                                  : Colors.green,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'تم اختيار صورة إثبات التحويل',
                                style: TextStyle(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.green.shade300
                                      : Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.red.shade900.withOpacity(0.3)
                                    : Colors.red.shade50,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.red.shade700
                                      : Colors.red.shade300,
                                  width: 1,
                                ),
                              ),
                              child: IconButton(
                                onPressed: _removeProofImage,
                                icon: Icon(
                                  Icons.delete,
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.red.shade300
                                      : Colors.red,
                                  size: 18,
                                ),
                                tooltip: 'حذف الصورة',
                                padding: const EdgeInsets.all(8),
                                constraints: const BoxConstraints(
                                  minWidth: 36,
                                  minHeight: 36,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                ],
                SingleTapButton(
                  onPressed: _isFormValid && !transactionProvider.isLoading && !_isSubmitting ? _handleSubmitTransaction : null,
                  checkConnection: true, // فحص الاتصال قبل إرسال المعاملة
                  connectionTitle: 'يتطلب اتصال بالإنترنت',
                  connectionMessage: 'إرسال طلب التوريد يتطلب اتصال بالإنترنت لمعالجة العملية.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                  icon: (transactionProvider.isLoading || _isSubmitting)
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.check_circle, size: 20),
                  loadingText: 'جاري المعالجة...',
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    minimumSize: const Size(double.infinity, 50),
                  ),
                  child: const Text(
                    'تم التحويل',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.orange.shade900.withValues(alpha: 0.3)
                        : Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.orange.shade700
                          : Colors.orange.shade200,
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.warning_amber,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.orange.shade300
                                : Colors.orange,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'تنبيه مهم:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.orange.shade300
                                  : Colors.orange,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'ستحصل على المبلغ المُورد بعد خصم العمولة. شاهد الإعلان لتحصل على المبلغ كاملاً!',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Theme.of(context).textTheme.bodyMedium?.color
                              : Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    );
  }
}
