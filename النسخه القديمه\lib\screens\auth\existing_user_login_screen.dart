import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/screens/home_screen.dart';
import 'package:wardlytec_app/widgets/single_tap_button.dart';
import 'package:wardlytec_app/utils/app_theme.dart';
import 'package:wardlytec_app/config/app_config.dart';

class ExistingUserLoginScreen extends StatefulWidget {
  const ExistingUserLoginScreen({super.key});

  @override
  State<ExistingUserLoginScreen> createState() => _ExistingUserLoginScreenState();
}

class _ExistingUserLoginScreenState extends State<ExistingUserLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _secretCodeController = TextEditingController();

  bool _isFormValid = false;
  bool _isButtonDisabled = false; // لتعطيل الزر لمدة 5 ثوانٍ

  @override
  void initState() {
    super.initState();
    _phoneController.addListener(_validateForm);
    _secretCodeController.addListener(_validateForm);
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _secretCodeController.dispose();
    super.dispose();
  }

  // دالة التواصل مع الدعم لاستعادة كلمة المرور
  Future<void> _contactSupport() async {
    final phoneNumber = _phoneController.text.trim();

    final message = '''مرحباً، أحتاج لاستعادة كلمة السر الخاصة بي في تطبيق وردلي.

📱 رقم الهاتف: ${phoneNumber.isNotEmpty ? '+2$phoneNumber' : 'لم يتم إدخاله'}

يرجى مساعدتي في استعادة كلمة السر للدخول إلى التطبيق.

شكراً لكم.''';

    // رقم WhatsApp للدعم من الإعدادات الآمنة
    final supportWhatsApp = AppConfig.adminWhatsApp;

    // إنشاء رابط WhatsApp
    final whatsappUrl = 'https://wa.me/$supportWhatsApp?text=${Uri.encodeComponent(message)}';

    try {
      final Uri uri = Uri.parse(whatsappUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);

        // عرض رسالة تأكيد
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم فتح WhatsApp. يرجى إرسال الرسالة للحصول على المساعدة.'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      } else {
        throw 'لا يمكن فتح WhatsApp';
      }
    } catch (e) {
      // للويب: فتح في نافذة جديدة
      await launchUrl(Uri.parse(whatsappUrl), mode: LaunchMode.platformDefault);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم فتح WhatsApp في المتصفح. يرجى إرسال الرسالة.'),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _validateForm() {
    setState(() {
      _isFormValid = _phoneController.text.trim().length == 11 &&
                    _secretCodeController.text.trim().length >= 4;
    });
  }






  // تعطيل الزر لمدة 5 ثوانٍ
  void _disableButtonTemporarily() {
    setState(() {
      _isButtonDisabled = true;
    });

    Timer(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _isButtonDisabled = false;
        });
      }
    });
  }

  // تسجيل الدخول
  Future<void> _login() async {
    // تعطيل الزر فوراً لمدة 5 ثوانٍ
    _disableButtonTemporarily();

    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
    final phoneNumber = '+2${_phoneController.text.trim()}';
    final secretCode = _secretCodeController.text.trim();

    try {
      final success = await authProvider.signInWithPhoneAndCode(phoneNumber, secretCode);

      if (!mounted) return;

      if (success) {
        if (!mounted) return;

        // تسجيل دخول ناجح محسن
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'تم تسجيل الدخول بنجاح!',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green[600],
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );

        // الانتقال إلى الشاشة الرئيسية
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const HomeScreen()),
          (route) => false,
        );
      } else {
        // فشل تسجيل الدخول محسن
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    '❌ رقم الهاتف أو الرمز السري غير صحيح',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ خطأ في تسجيل الدخول: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تسجيل الدخول'),
        centerTitle: true,
      ),
      body: Consumer<SupabaseAuthProvider>(
        builder: (context, authProvider, child) {
          return SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                    const SizedBox(height: 40),

                    // أيقونة تسجيل الدخول
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.login,
                        size: 50,
                        color: AppTheme.primaryColor,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // عنوان الشاشة
                    Text(
                      'أهلاً بعودتك!',
                      style: Theme.of(context).textTheme.displaySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    // وصف الشاشة
                    Text(
                      'أدخل رقم هاتفك والرمز السري للدخول',
                      style: TextStyle(
                        fontSize: 16,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 32),

                    // حقل رقم الهاتف
                    TextFormField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(11),
                      ],
                      decoration: InputDecoration(
                        labelText: 'رقم الهاتف',
                        hintText: 'أدخل رقم هاتفك',
                        prefixIcon: const Icon(Icons.phone),
                        prefixText: '+2 ',
                        prefixStyle: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontWeight: FontWeight.bold,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: AppTheme.primaryColor, width: 2),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال رقم الهاتف';
                        }
                        if (value.trim().length != 11) {
                          return 'رقم الهاتف يجب أن يكون 11 رقم';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // حقل الرمز السري
                    TextFormField(
                      controller: _secretCodeController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(10),
                      ],
                      decoration: InputDecoration(
                        labelText: 'الرمز السري',
                        hintText: 'أدخل الرمز السري',
                        prefixIcon: const Icon(Icons.lock),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: AppTheme.primaryColor, width: 2),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال الرمز السري';
                        }
                        if (value.trim().length < 4) {
                          return 'الرمز السري يجب أن يكون 4 أرقام على الأقل';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 32),

                    // زر الدخول مع فحص الاتصال
                    SingleTapButton(
                      onPressed: (_isFormValid && !authProvider.isLoading && !_isButtonDisabled) ? _login : null,
                      checkConnection: true, // فحص الاتصال قبل تسجيل الدخول
                      connectionTitle: 'يتطلب اتصال بالإنترنت',
                      connectionMessage: 'تسجيل الدخول يتطلب اتصال بالإنترنت للتحقق من بيانات المستخدم.\n\nيرجى التحقق من اتصالك والمحاولة مرة أخرى.',
                      icon: (authProvider.isLoading || _isButtonDisabled)
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(
                              Icons.login,
                              size: 24,
                              color: Colors.white,
                            ),
                      loadingText: 'جاري تسجيل الدخول...',
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _isFormValid ? AppTheme.primaryColor : Colors.grey[400],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: _isFormValid ? 3 : 1,
                        minimumSize: const Size(double.infinity, 56),
                      ),
                      child: const Text(
                        'دخول',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                    ),

                    const SizedBox(height: 40),

                    // نص المساعدة مع رابط قابل للضغط
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'إذا نسيت كلمة السر، ',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).textTheme.bodySmall?.color,
                          ),
                        ),
                        GestureDetector(
                          onTap: _contactSupport,
                          child: Text(
                            'تواصل معنا',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
