import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:wardlytec_app/models/user.dart';
import 'package:wardlytec_app/widgets/rewards_renewal_card.dart';

void main() {
  group('RewardsRenewalCard Tests', () {
    late UserModel testUser;

    setUp(() {
      testUser = UserModel(
        id: 1,
        phoneNumber: '***********',
        name: 'أحمد محمد',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        favoriteAccounts: [],
        adViewsRemaining: 7,
        rechargeAdViewsRemaining: 5,
        lastRewardsRenewal: DateTime.now().subtract(const Duration(days: 15)),
        nextRewardsRenewal: DateTime.now().add(const Duration(days: 15)),
        renewalCount: 2,
        monthlySupplyRewards: 10,
        monthlyRechargeRewards: 10,
      );
    });

    testWidgets('should display rewards renewal card with user data', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardsRenewalCard(user: testUser),
          ),
        ),
      );

      // التحقق من وجود العنوان
      expect(find.text('نظام المكافآت الشهرية'), findsOneWidget);
      expect(find.text('تجديد تلقائي كل 30 يوم'), findsOneWidget);

      // التحقق من عدادات المكافآت
      expect(find.text('التوريد'), findsOneWidget);
      expect(find.text('الشحن'), findsOneWidget);
      expect(find.text('7/10'), findsOneWidget);
      expect(find.text('5/10'), findsOneWidget);

      // التحقق من وجود أيقونة الهدية
      expect(find.byIcon(Icons.card_giftcard), findsOneWidget);
    });

    testWidgets('should show correct renewal status for user due for renewal', (WidgetTester tester) async {
      // مستخدم مستحق للتجديد
      final dueUser = testUser.copyWith(
        nextRewardsRenewal: DateTime.now().subtract(const Duration(days: 1)),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardsRenewalCard(user: dueUser),
          ),
        ),
      );

      // التحقق من حالة التجديد
      expect(find.text('مستحق للتجديد'), findsOneWidget);
      expect(find.byIcon(Icons.notification_important), findsOneWidget);
    });

    testWidgets('should show correct renewal status for upcoming renewal', (WidgetTester tester) async {
      // مستخدم التجديد خلال 3 أيام
      final upcomingUser = testUser.copyWith(
        nextRewardsRenewal: DateTime.now().add(const Duration(days: 3)),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardsRenewalCard(user: upcomingUser),
          ),
        ),
      );

      // التحقق من حالة التجديد
      expect(find.text('التجديد خلال 3 أيام'), findsOneWidget);
      expect(find.byIcon(Icons.schedule), findsOneWidget);
    });

    testWidgets('should display renewal count and membership duration', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardsRenewalCard(user: testUser),
          ),
        ),
      );

      // التحقق من عدد مرات التجديد
      expect(find.text('2'), findsOneWidget);
      expect(find.text('مرات التجديد'), findsOneWidget);

      // التحقق من مدة العضوية
      expect(find.text('عضو منذ'), findsOneWidget);
    });

    testWidgets('should show progress bars for rewards', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardsRenewalCard(user: testUser),
          ),
        ),
      );

      // التحقق من وجود شريط التقدم
      expect(find.byType(LinearProgressIndicator), findsNWidgets(2));
    });

    testWidgets('should call onRefresh when refresh button is pressed', (WidgetTester tester) async {
      bool refreshCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardsRenewalCard(
              user: testUser,
              onRefresh: () {
                refreshCalled = true;
              },
            ),
          ),
        ),
      );

      // البحث عن زر التحديث والضغط عليه
      final refreshButton = find.byIcon(Icons.refresh);
      expect(refreshButton, findsOneWidget);

      await tester.tap(refreshButton);
      await tester.pump();

      // التحقق من استدعاء دالة التحديث
      expect(refreshCalled, isTrue);
    });

    testWidgets('should display correct colors based on renewal status', (WidgetTester tester) async {
      // مستخدم مستحق للتجديد (لون برتقالي)
      final dueUser = testUser.copyWith(
        nextRewardsRenewal: DateTime.now().subtract(const Duration(days: 1)),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardsRenewalCard(user: dueUser),
          ),
        ),
      );

      await tester.pump();

      // يمكن إضافة المزيد من اختبارات الألوان هنا
      // لكن هذا يتطلب فحص أعمق للـ widget tree
    });

    testWidgets('should handle user with no renewal dates', (WidgetTester tester) async {
      final userWithoutDates = testUser.copyWith(
        lastRewardsRenewal: null,
        nextRewardsRenewal: null,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardsRenewalCard(user: userWithoutDates),
          ),
        ),
      );

      // التحقق من عدم حدوث أخطاء
      expect(find.byType(RewardsRenewalCard), findsOneWidget);
    });

    testWidgets('should display full rewards correctly', (WidgetTester tester) async {
      final fullRewardsUser = testUser.copyWith(
        adViewsRemaining: 10,
        rechargeAdViewsRemaining: 10,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardsRenewalCard(user: fullRewardsUser),
          ),
        ),
      );

      // التحقق من عرض المكافآت الكاملة
      expect(find.text('10/10'), findsNWidgets(2));
    });

    testWidgets('should display empty rewards correctly', (WidgetTester tester) async {
      final emptyRewardsUser = testUser.copyWith(
        adViewsRemaining: 0,
        rechargeAdViewsRemaining: 0,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RewardsRenewalCard(user: emptyRewardsUser),
          ),
        ),
      );

      // التحقق من عرض المكافآت الفارغة
      expect(find.text('0/10'), findsNWidgets(2));
    });
  });
}
