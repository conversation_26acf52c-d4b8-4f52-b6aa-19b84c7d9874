import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة الإشعارات المحلية
class LocalNotificationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة المناطق الزمنية
      tz.initializeTimeZones();

      // إعدادات Android
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // إعدادات iOS
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // الإعدادات العامة
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // تهيئة الإشعارات
      await _notificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ تم تهيئة خدمة الإشعارات المحلية بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة خدمة الإشعارات: $e');
      }
    }
  }

  /// معالجة الضغط على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    if (kDebugMode) {
      print('🔔 تم الضغط على الإشعار: ${response.payload}');
    }
    
    // يمكن إضافة منطق للتنقل لصفحة معينة حسب نوع الإشعار
    // مثل فتح صفحة تفاصيل المعاملة
  }

  /// طلب الأذونات (خاص بـ Android 13+)
  static Future<bool> requestPermissions() async {
    try {
      final bool? result = await _notificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
      
      return result ?? true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في طلب أذونات الإشعارات: $e');
      }
      return false;
    }
  }

  /// إرسال إشعار فوري
  static Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.defaultPriority,
    String? channelId,
    String? channelName,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // التحقق من إعدادات المستخدم
      if (!await _isNotificationEnabled('transaction_updates')) {
        return;
      }

      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        channelId ?? 'transaction_updates',
        channelName ?? 'تحديثات المعاملات',
        channelDescription: 'إشعارات تحديثات حالة المعاملات',
        importance: _getImportance(priority),
        priority: _getPriority(priority),
        icon: '@mipmap/ic_launcher',
        color: const Color(0xFF2196F3),
        enableVibration: true,
        playSound: true,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notificationsPlugin.show(
        id,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      if (kDebugMode) {
        print('📱 تم إرسال الإشعار: $title');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال الإشعار: $e');
      }
    }
  }

  /// إرسال إشعار تحديث المعاملة
  static Future<void> showTransactionUpdateNotification({
    required String transactionId,
    required String oldStatus,
    required String newStatus,
    required String transactionType,
    double? amount,
  }) async {
    final String title = _getTransactionUpdateTitle(transactionType, newStatus);
    final String body = _getTransactionUpdateBody(
      transactionId,
      oldStatus,
      newStatus,
      transactionType,
      amount,
    );

    await showNotification(
      id: transactionId.hashCode,
      title: title,
      body: body,
      payload: 'transaction_update:$transactionId',
      priority: NotificationPriority.high,
      channelId: 'transaction_updates',
      channelName: 'تحديثات المعاملات',
    );
  }

  /// إرسال إشعار تعطيل الخدمة
  static Future<void> showServiceDisabledNotification({
    required String serviceName,
    required String message,
  }) async {
    final String title = _getServiceDisabledTitle(serviceName);
    
    await showNotification(
      id: serviceName.hashCode,
      title: title,
      body: message,
      payload: 'service_disabled:$serviceName',
      priority: NotificationPriority.high,
      channelId: 'service_updates',
      channelName: 'تحديثات الخدمات',
    );
  }

  /// إلغاء إشعار معين
  static Future<void> cancelNotification(int id) async {
    try {
      await _notificationsPlugin.cancel(id);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إلغاء الإشعار: $e');
      }
    }
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    try {
      await _notificationsPlugin.cancelAll();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إلغاء جميع الإشعارات: $e');
      }
    }
  }

  /// تفعيل/تعطيل نوع إشعار معين
  static Future<void> setNotificationEnabled(
    String notificationType,
    bool enabled,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notification_$notificationType', enabled);
      
      if (kDebugMode) {
        print('⚙️ تم ${enabled ? 'تفعيل' : 'تعطيل'} إشعارات $notificationType');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث إعدادات الإشعارات: $e');
      }
    }
  }

  /// التحقق من تفعيل نوع إشعار معين
  static Future<bool> _isNotificationEnabled(String notificationType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('notification_$notificationType') ?? true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في قراءة إعدادات الإشعارات: $e');
      }
      return true; // افتراضي مفعل
    }
  }

  /// الحصول على حالة تفعيل الإشعارات
  static Future<Map<String, bool>> getNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'transaction_updates': prefs.getBool('notification_transaction_updates') ?? true,
        'service_updates': prefs.getBool('notification_service_updates') ?? true,
        'promotional': prefs.getBool('notification_promotional') ?? false,
        'maintenance': prefs.getBool('notification_maintenance') ?? true,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في قراءة إعدادات الإشعارات: $e');
      }
      return {
        'transaction_updates': true,
        'service_updates': true,
        'promotional': false,
        'maintenance': true,
      };
    }
  }

  // ========================================
  // دوال مساعدة
  // ========================================

  /// تحويل الأولوية إلى Importance
  static Importance _getImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.min:
        return Importance.min;
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.defaultPriority:
        return Importance.defaultImportance;
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.max:
        return Importance.max;
    }
  }

  /// تحويل الأولوية إلى Priority
  static Priority _getPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.min:
        return Priority.min;
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.defaultPriority:
        return Priority.defaultPriority;
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.max:
        return Priority.max;
    }
  }

  /// الحصول على عنوان إشعار تحديث المعاملة
  static String _getTransactionUpdateTitle(String type, String status) {
    final String typeText = type == 'supply' ? 'التوريد' : 'الشحن';
    
    switch (status.toLowerCase()) {
      case 'pending':
        return '⏳ معاملة $typeText قيد المراجعة';
      case 'approved':
        return '✅ تم قبول معاملة $typeText';
      case 'rejected':
        return '❌ تم رفض معاملة $typeText';
      case 'completed':
        return '🎉 تم إنجاز معاملة $typeText';
      default:
        return '📱 تحديث معاملة $typeText';
    }
  }

  /// الحصول على محتوى إشعار تحديث المعاملة
  static String _getTransactionUpdateBody(
    String transactionId,
    String oldStatus,
    String newStatus,
    String type,
    double? amount,
  ) {
    final String amountText = amount != null ? ' بقيمة ${amount.toStringAsFixed(2)} ج.م' : '';
    final String shortId = transactionId.length > 8 
        ? transactionId.substring(0, 8) 
        : transactionId;
    
    return 'المعاملة #$shortId$amountText\nتم تحديث الحالة إلى: $newStatus';
  }

  /// الحصول على عنوان إشعار تعطيل الخدمة
  static String _getServiceDisabledTitle(String serviceName) {
    switch (serviceName) {
      case 'supply_transactions':
        return '⚠️ تعطيل خدمة التوريد';
      case 'recharge_transactions':
        return '⚠️ تعطيل خدمة الشحن';
      default:
        return '⚠️ تعطيل الخدمة';
    }
  }
}

/// أولويات الإشعارات
enum NotificationPriority {
  min,
  low,
  defaultPriority,
  high,
  max,
}
