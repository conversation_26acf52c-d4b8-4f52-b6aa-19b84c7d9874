import 'package:flutter/material.dart';
import 'package:wardlytec_app/models/transaction.dart';
import 'package:wardlytec_app/config/app_config.dart';
import 'package:wardlytec_app/supabase_config.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class TransactionProvider extends ChangeNotifier {
  List<Transaction> _transactions = [];
  bool _isLoading = false;
  String? _error;

  // إعدادات Supabase من الإعدادات الآمنة
  static String get _supabaseUrl => AppConfig.supabaseUrl;
  static String get _supabaseKey => AppConfig.supabaseAnonKey;

  // Getters
  List<Transaction> get transactions => _transactions;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> fetchUserTransactions(String userId) async {
    _setLoading(true);
    _error = null;

    try {
      // استرداد معاملات التوريد
      final supplyUrl = Uri.parse('$_supabaseUrl/rest/v1/supply_transactions?user_id=eq.$userId&order=created_at.desc');
      final supplyResponse = await http.get(
        supplyUrl,
        headers: {
          'apikey': _supabaseKey,
          'Authorization': 'Bearer $_supabaseKey',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 15));

      // استرداد معاملات الشحن
      final rechargeUrl = Uri.parse('$_supabaseUrl/rest/v1/recharge_transactions?user_id=eq.$userId&order=created_at.desc');
      final rechargeResponse = await http.get(
        rechargeUrl,
        headers: {
          'apikey': _supabaseKey,
          'Authorization': 'Bearer $_supabaseKey',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 15));

      if (supplyResponse.statusCode == 200 && rechargeResponse.statusCode == 200) {
        final List<dynamic> supplyData = json.decode(supplyResponse.body);
        final List<dynamic> rechargeData = json.decode(rechargeResponse.body);

        // تحويل معاملات التوريد
        final supplyTransactions = supplyData.map((data) {
          return Transaction(
            id: data['id'].toString(),
            userId: data['user_id'].toString(),
            talabatAccountNumber: data['talabat_account_number'] ?? '',
            senderWalletNumber: data['sender_wallet_number'] ?? '',
            amount: (data['amount'] ?? 0).toDouble(),
            commission: (data['commission'] ?? 0).toDouble(),
            totalAmount: (data['total_amount'] ?? 0).toDouble(),
            walletType: data['wallet_type'] ?? '',
            date: DateTime.parse(data['created_at']),
            status: data['status'] ?? 'جاري',
            type: 'transaction', // نوع التوريد
            hasWatchedAd: data['has_watched_ad'] ?? false,
          );
        }).toList();

        // تحويل معاملات الشحن
        final rechargeTransactions = rechargeData.map((data) {
          return Transaction(
            id: data['id'].toString(),
            userId: data['user_id'].toString(),
            talabatAccountNumber: null, // غير مطلوب للشحن
            senderWalletNumber: data['sender_wallet'] ?? '',
            amount: (data['amount'] ?? 0).toDouble(),
            commission: (data['commission'] ?? 0).toDouble(),
            totalAmount: (data['total_amount'] ?? 0).toDouble(),
            walletType: data['payment_method'] ?? '',
            date: DateTime.parse(data['created_at']),
            status: data['status'] ?? 'جاري',
            type: 'recharge', // نوع الشحن
            phoneNumber: data['phone_number'],
            rechargeType: data['recharge_type'],
            network: data['network'],
            paymentMethod: data['payment_method'],
            senderWallet: data['sender_wallet'],
            hasWatchedAd: data['has_watched_ad'] ?? false,
          );
        }).toList();

        // دمج المعاملات وترتيبها حسب التاريخ
        _transactions = [...supplyTransactions, ...rechargeTransactions];
        _transactions.sort((a, b) => b.date.compareTo(a.date));
      } else {
        _error = 'تعذر تحميل المعاملات. يرجى المحاولة مرة أخرى.';
        _transactions = [];
      }

    } catch (e) {
      if (e.toString().contains('timeout')) {
        _error = 'انتهت مهلة الاتصال. يرجى التحقق من الإنترنت والمحاولة مرة أخرى.';
      } else {
        _error = 'حدث خطأ في تحميل المعاملات. يرجى المحاولة مرة أخرى.';
      }
      _transactions = []; // قائمة فارغة في حالة الخطأ
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> addTransaction({
    required String userId,
    required String talabatAccountNumber,
    required String senderWalletNumber,
    required double amount,
    required String walletType,
    required double commission,
    required String proofImageBase64, // صورة إثبات التحويل (مطلوب)
    required bool hasWatchedAd, // إضافة معامل مشاهدة الإعلان
  }) async {
    _setLoading(true);
    _error = null;

    // 🛡️ التحقق من وجود صورة إثبات التحويل
    if (proofImageBase64.isEmpty) {
      _error = 'صورة إثبات التحويل مطلوبة';
      _setLoading(false);
      return false;
    }

    try {
      final totalAmount = amount + commission;
      final now = DateTime.now();

      // 🔧 الحل المؤقت: استخدام HTTP مع تعطيل RLS مؤقتاً
      // TODO: تطبيق نظام مصادقة Supabase Auth لاحقاً

      final requestBody = {
        'user_id': int.tryParse(userId) ?? 0, // تحويل إلى رقم
        'talabat_account_number': talabatAccountNumber,
        'sender_wallet_number': senderWalletNumber,
        'amount': amount,
        'commission': commission,
        'total_amount': totalAmount,
        'wallet_type': walletType,
        'has_watched_ad': hasWatchedAd, // إضافة حالة مشاهدة الإعلان
        'status': 'جاري',
        'created_at': now.toIso8601String(),
      };

      // طباعة تفاصيل الطلب للتشخيص
      print('🔄 إرسال طلب إلى Supabase:');
      print('📋 Request Body: ${json.encode(requestBody)}');

      // استخدام Supabase Client بدلاً من HTTP المباشر
      final response = await SupabaseConfig.client
          .from('supply_transactions')
          .insert(requestBody)
          .select();

      print('📤 Response: $response');

      if (response.isNotEmpty) {
        final responseData = response[0];

        // إنشاء Transaction object من الاستجابة
        final newTransaction = Transaction(
          id: responseData['id'].toString(),
          userId: responseData['user_id'].toString(),
          type: 'transaction', // نوع المعاملة: توريد
          talabatAccountNumber: responseData['talabat_account_number'],
          senderWalletNumber: responseData['sender_wallet_number'],
          amount: responseData['amount'].toDouble(),
          commission: responseData['commission'].toDouble(),
          totalAmount: responseData['total_amount'].toDouble(),
          walletType: responseData['wallet_type'],
          date: DateTime.parse(responseData['created_at']),
          status: responseData['status'],
          hasWatchedAd: responseData['has_watched_ad'] ?? false, // إضافة حالة مشاهدة الإعلان
        );

        // إضافة المعاملة للقائمة المحلية
        _transactions.insert(0, newTransaction);
        notifyListeners();

        // إرسال إشعار للمدير عن العملية الجديدة
        try {
          await _sendTransactionNotification(newTransaction, proofImageBase64);
        } catch (e) {
          // فشل الإشعار لا يؤثر على نجاح المعاملة
          _error = null; // نتجاهل خطأ الإشعار

          // محاولة إعادة الإرسال مرة واحدة
          try {
            await Future.delayed(const Duration(seconds: 2));
            await _sendTransactionNotification(newTransaction, proofImageBase64);
          } catch (retryError) {
            // تجاهل خطأ إعادة المحاولة
          }
        }

        // إعادة تحميل المعاملات من قاعدة البيانات للتأكد من التحديث
        await _refreshTransactions(userId);

        _setLoading(false);
        return true;
      } else {
        // طباعة تفاصيل الخطأ للتشخيص
        print('❌ فشل حفظ المعاملة - استجابة فارغة');

        // إظهار تفاصيل الخطأ للمستخدم للتشخيص
        _error = 'تعذر حفظ المعاملة - لم يتم إرجاع بيانات من الخادم';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      // طباعة تفاصيل الخطأ للتشخيص
      print('❌ خطأ في addTransaction: $e');
      print('📍 نوع الخطأ: ${e.runtimeType}');

      if (e.toString().contains('timeout')) {
        _error = 'انتهت مهلة الإرسال. يرجى التحقق من الإنترنت والمحاولة مرة أخرى.';
      } else {
        // إظهار تفاصيل الخطأ للمستخدم للتشخيص
        _error = 'حدث خطأ في إرسال المعاملة:\n'
                'نوع الخطأ: ${e.runtimeType}\n'
                'التفاصيل: ${e.toString().length > 200 ? e.toString().substring(0, 200) + "..." : e.toString()}';
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Transaction? getTransactionById(String transactionId) {
    try {
      return _transactions.firstWhere((t) => t.id == transactionId);
    } catch (e) {
      return null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    if (hasListeners) {
      notifyListeners();
    }
  }

  void resetError() {
    _error = null;
    if (hasListeners) {
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // تنظيف الموارد
    super.dispose();
  }

  // إنشاء معاملة شحن جديدة
  Future<void> createRechargeTransaction({
    required String userId,
    required String phoneNumber,
    required String rechargeType,
    required double amount,
    required double commission,
    required double totalAmount,
    required String network,
    required String paymentMethod,
    required String senderWallet,
    required String proofImageBase64,
    required bool hasWatchedAd,
    String? userName,
    String? userPhoneNumber,
  }) async {
    try {
      // 🔧 استخدام Supabase Client بدلاً من HTTP المباشر
      final requestBody = {
        'user_id': int.tryParse(userId) ?? 0, // تحويل إلى رقم
        'phone_number': phoneNumber,
        'recharge_type': rechargeType,
        'network': network,
        'amount': amount,
        'commission': commission,
        'total_amount': totalAmount,
        'payment_method': paymentMethod,
        'sender_wallet': senderWallet,
        'proof_image_url': proofImageBase64,
        'has_watched_ad': hasWatchedAd,
        'status': 'pending',
        'created_at': DateTime.now().toIso8601String(),
      };

      print('🔄 إرسال طلب شحن إلى Supabase:');
      print('📋 Request Body: ${jsonEncode(requestBody)}');

      // استخدام Supabase Client
      final response = await SupabaseConfig.client
          .from('recharge_transactions')
          .insert(requestBody)
          .select();

      print('📤 Response: $response');

      if (response.isEmpty) {
        throw Exception('لم يتم إرجاع بيانات المعاملة');
      }

      final responseData = response[0];

      // إنشاء كائن المعاملة
      final transaction = Transaction.fromJson(responseData);

      // إضافة المعاملة للقائمة
      _transactions.insert(0, transaction);
      notifyListeners();

      // إرسال إشعار للبوت مع البيانات الإضافية
      try {
        await _sendRechargeNotification(transaction, proofImageBase64, userName, userPhoneNumber);
      } catch (e) {
        // محاولة إعادة الإرسال مرة واحدة
        try {
          await Future.delayed(const Duration(seconds: 2));
          await _sendRechargeNotification(transaction, proofImageBase64, userName, userPhoneNumber);
        } catch (retryError) {
          // لا نرمي خطأ هنا لأن المعاملة تم إنشاؤها بنجاح
        }
      }

    } catch (e) {
      print('خطأ في إنشاء معاملة الشحن: $e');
      throw Exception('فشل في إنشاء معاملة الشحن: $e');
    }
  }

  // إرسال إشعار الشحن لبوت الشحن المخصص مع جميع البيانات
  Future<void> _sendRechargeNotification(Transaction transaction, String proofImageBase64, String? userName, String? userPhoneNumber) async {
    try {
      // إرسال لبوت الشحن المخصص (طلبات الشحن فقط)
      const botToken = '8094539279:AAGAjcHys0XZOXiG35Yw7dGCwtZ0jLDUJX0';
      const adminChatId = '8289609318';

      // التحقق من صحة البيانات قبل الإرسال
      if (proofImageBase64.isEmpty) {
        throw Exception('صورة إثبات الدفع مفقودة');
      }

      // تحويل الصورة من base64 إلى bytes
      final imageBytes = base64Decode(proofImageBase64);

      // تنسيق التاريخ والوقت
      final formattedDate = '${transaction.date.day.toString().padLeft(2, '0')}/${transaction.date.month.toString().padLeft(2, '0')}/${transaction.date.year}';
      final formattedTime = '${transaction.date.hour.toString().padLeft(2, '0')}:${transaction.date.minute.toString().padLeft(2, '0')}';

      // تحديد نص الحقل حسب نوع المحفظة
      final isInstapay = transaction.paymentMethod?.toLowerCase() == 'instapay';
      final senderFieldLabel = isInstapay ? '• اسم صاحب حساب انستاباي' : '• رقم المحفظة المرسلة';

      // إنشاء رسالة الإشعار بالتنسيق الجديد
      final message = '''
📱 *طلب شحن رصيد جديد*

🆔 *رقم المعاملة:* ${transaction.id}

━━━━━━━━━━━━━━━━

📱 *بيانات الشحن:*
• الشبكة: ${transaction.network ?? 'غير محدد'}
• الرقم المراد شحنه: ${transaction.phoneNumber ?? 'غير محدد'}
• نوع الشحن: ${transaction.rechargeType ?? 'غير محدد'}
• قيمة الشحن: ${transaction.amount != null ? (transaction.amount == transaction.amount!.roundToDouble() ? transaction.amount!.toInt() : transaction.amount) : '0'} جنيه

━━━━━━━━━━━━━━━━

💳 *بيانات الدفع:*
• المحفظة المرسلة منها: ${transaction.paymentMethod ?? 'غير محدد'}
$senderFieldLabel: ${transaction.senderWallet ?? 'غير محدد'}
• العمولة: ${transaction.commission != null ? (transaction.commission == transaction.commission!.roundToDouble() ? transaction.commission!.toInt() : transaction.commission) : '0'} جنيه${transaction.hasWatchedAd == true ? ' (ملغاة بالإعلان ✅)' : ''}
• إجمالي المبلغ: ${transaction.totalAmount != null ? (transaction.totalAmount == transaction.totalAmount!.roundToDouble() ? transaction.totalAmount!.toInt() : transaction.totalAmount) : '0'} جنيه

━━━━━━━━━━━━━━━━

📅 *الوقت:* ${DateTime.now().toString().substring(0, 16)}
⏳ *الحالة:* جاري المراجعة
      ''';

      // إرسال الرسالة مع الصورة
      final url = 'https://api.telegram.org/bot$botToken/sendPhoto';

      final request = http.MultipartRequest('POST', Uri.parse(url));
      request.fields['chat_id'] = adminChatId;
      request.fields['caption'] = message;
      request.fields['parse_mode'] = 'Markdown';

      // إضافة الصورة
      request.files.add(
        http.MultipartFile.fromBytes(
          'photo',
          imageBytes,
          filename: 'recharge_proof_${transaction.id}.jpg',
        ),
      );

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final responseData = json.decode(responseBody);
        if (responseData['ok'] != true) {
          throw Exception('Telegram API Error: ${responseData['description']}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode} - $responseBody');
      }

    } catch (e) {
      rethrow; // إعادة رمي الخطأ للمعالجة في المستوى الأعلى
    }
  }

  // دالة للحصول على اسم وسيلة الدفع بالعربية
  String _getPaymentMethodDisplayName(String? walletType) {
    final type = walletType?.toLowerCase() ?? '';

    switch (type) {
      case 'vodafone cash':
        return 'فودافون كاش';
      case 'etisalat cash':
        return 'اتصالات كاش';
      case 'orange cash':
        return 'اورنچ كاش';
      case 'cib smart wallet':
        return 'محفظة CIB الذكية';
      case 'instapay':
        return 'إنستاباي';
      default:
        return walletType ?? 'غير محدد';
    }
  }

  // إرسال إشعار المعاملة لبوت المعاملات المخصص
  Future<void> _sendTransactionNotification(Transaction transaction, String proofImageBase64) async {
    try {
      // إرسال لبوت المعاملات المخصص (معاملات التوريد فقط)
      const botToken = '8203859619:AAEEyd5ZMpco_nNjkVIPZh9mOmEnHJ4g7Ho';
      const adminChatId = '8289609318';

      // التحقق من صحة البيانات قبل الإرسال
      if (proofImageBase64.isEmpty) {
        throw Exception('صورة إثبات الدفع مفقودة');
      }

      // تحديد نوع الحقل حسب نوع المحفظة
      final isInstapay = transaction.walletType.toLowerCase() == 'instapay';
      final senderFieldLabel = isInstapay ? '👤 اسم صاحب حساب إنستاباي' : '📱 رقم المحفظة المرسلة';

      // تحديد نص العمولة حسب القيمة
      final commissionText = transaction.commission == 0
          ? '💰• العمولة: ${transaction.commission == transaction.commission.roundToDouble() ? transaction.commission.toInt() : transaction.commission} جنيه (ملغاة بالإعلان ✅)'
          : '💰• العمولة: ${transaction.commission == transaction.commission.roundToDouble() ? transaction.commission.toInt() : transaction.commission} جنيه';

      final message = '''
💰 *توريد جديد يحتاج موافقة*

🆔 *رقم المعاملة:* ${transaction.id}

━━━━━━━━━━━━━━━━


📋 *بيانات التوريد:*
🏪 رقم حساب طلبات: ${transaction.talabatAccountNumber}
💳 وسيلة الدفع: ${_getPaymentMethodDisplayName(transaction.walletType)}
$senderFieldLabel: ${transaction.senderWalletNumber}

━━━━━━━━━━━━━━━━

💰 *ملخص المبالغ:*
💵 المبلغ المراد توريده: ${transaction.totalAmount == transaction.totalAmount.roundToDouble() ? transaction.totalAmount.toInt() : transaction.totalAmount} جنيه
$commissionText
💸 الإجمالي: ${transaction.amount == transaction.amount.roundToDouble() ? transaction.amount.toInt() : transaction.amount} جنيه

━━━━━━━━━━━━━━━━

📅 *الوقت:* ${DateTime.now().toString().substring(0, 16)}
⏳ *الحالة:* جاري المراجعة
''';

      // لا توجد أزرار تحكم - رسالة إعلامية فقط
      final keyboard = null;



      // تحويل الصورة من base64 إلى bytes
      final imageBytes = base64Decode(proofImageBase64);

      // إرسال الصورة مع النص (بدون أزرار)
      await _sendPhotoWithMessage(botToken, adminChatId, proofImageBase64, message, keyboard, transaction.id);

    } catch (e) {
      rethrow; // إعادة رمي الخطأ للمعالجة في المستوى الأعلى
    }
  }

  // إرسال صورة مع رسالة مدمجة (بدون أزرار)
  Future<void> _sendPhotoWithMessage(
    String botToken,
    String chatId,
    String imageBase64,
    String message,
    Map<String, dynamic>? keyboard,
    String transactionId
  ) async {
    try {
      // تحويل Base64 إلى bytes
      final imageBytes = base64Decode(imageBase64);

      // إنشاء multipart request
      final uri = Uri.parse('https://api.telegram.org/bot$botToken/sendPhoto');
      final request = http.MultipartRequest('POST', uri);

      // إضافة البيانات
      request.fields['chat_id'] = chatId;
      request.fields['caption'] = message; // النص الكامل مع بيانات المعاملة
      request.fields['parse_mode'] = 'Markdown';

      // إضافة الأزرار فقط إذا كانت موجودة
      if (keyboard != null) {
        request.fields['reply_markup'] = json.encode(keyboard);
      }

      // إضافة الصورة
      request.files.add(
        http.MultipartFile.fromBytes(
          'photo',
          imageBytes,
          filename: 'proof_$transactionId.jpg',
        ),
      );

      // إرسال الطلب
      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final responseData = json.decode(responseBody);
        if (responseData['ok'] != true) {
          throw Exception('Telegram API Error: ${responseData['description']}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode} - $responseBody');
      }

    } catch (e) {
      rethrow; // إعادة رمي الخطأ للمعالجة في المستوى الأعلى
    }
  }



  // إعادة تحميل المعاملات من قاعدة البيانات
  Future<void> _refreshTransactions(String userId) async {
    try {
      // إعادة تحميل جميع المعاملات من الجداول الجديدة
      await fetchUserTransactions(userId);
    } catch (e) {
      // تجاهل أخطاء إعادة التحميل
    }
  }


}
