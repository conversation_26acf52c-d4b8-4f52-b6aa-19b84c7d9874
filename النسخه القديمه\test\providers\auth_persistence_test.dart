import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';

void main() {
  group('Auth Persistence Tests', () {
    late SupabaseAuthProvider authProvider;

    setUp(() {
      // تهيئة SharedPreferences للاختبار
      SharedPreferences.setMockInitialValues({});
      authProvider = SupabaseAuthProvider();
    });

    test('should persist authentication state', () async {
      // محاكاة حفظ بيانات المصادقة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_authenticated', true);
      await prefs.setString('user_id', '123');
      await prefs.setString('user_name', 'أحمد محمد');
      await prefs.setString('user_phone', '+201234567890');
      await prefs.setInt('ad_views_remaining', 20);

      // إنشاء مزود جديد لمحاكاة إعادة فتح التطبيق
      final newAuthProvider = SupabaseAuthProvider();
      
      // انتظار تحميل البيانات
      await Future.delayed(const Duration(milliseconds: 100));

      // التحقق من أن البيانات تم تحميلها
      expect(newAuthProvider.isAuthenticated, true);
      expect(newAuthProvider.currentUser?.name, 'أحمد محمد');
      expect(newAuthProvider.currentUser?.phoneNumber, '+201234567890');
    });

    test('should not authenticate if is_authenticated is false', () async {
      // محاكاة عدم وجود مصادقة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_authenticated', false);
      await prefs.setString('user_id', '123');
      await prefs.setString('user_name', 'أحمد محمد');
      await prefs.setString('user_phone', '+201234567890');

      // إنشاء مزود جديد
      final newAuthProvider = SupabaseAuthProvider();
      
      // انتظار تحميل البيانات
      await Future.delayed(const Duration(milliseconds: 100));

      // التحقق من عدم المصادقة
      expect(newAuthProvider.isAuthenticated, false);
      expect(newAuthProvider.currentUser, null);
    });

    test('should clear authentication on signOut', () async {
      // محاكاة تسجيل دخول
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_authenticated', true);
      await prefs.setString('user_id', '123');
      await prefs.setString('user_name', 'أحمد محمد');
      await prefs.setString('user_phone', '+201234567890');

      // تحميل البيانات
      final newAuthProvider = SupabaseAuthProvider();
      await Future.delayed(const Duration(milliseconds: 100));

      // التأكد من تسجيل الدخول
      expect(newAuthProvider.isAuthenticated, true);

      // تسجيل الخروج
      await newAuthProvider.signOut();

      // التحقق من حذف البيانات
      expect(newAuthProvider.isAuthenticated, false);
      expect(newAuthProvider.currentUser, null);
      
      // التحقق من حذف البيانات من SharedPreferences
      final isAuthenticated = prefs.getBool('is_authenticated') ?? true;
      expect(isAuthenticated, false);
    });

    test('should validate stored auth correctly', () async {
      final prefs = await SharedPreferences.getInstance();
      
      // حالة صحيحة
      await prefs.setBool('is_authenticated', true);
      await prefs.setString('user_id', '123');
      await prefs.setString('user_name', 'أحمد محمد');
      await prefs.setString('user_phone', '+201234567890');

      bool isValid = await authProvider.validateStoredAuth();
      expect(isValid, true);

      // حالة ناقصة
      await prefs.remove('user_name');
      isValid = await authProvider.validateStoredAuth();
      expect(isValid, false);

      // حالة غير مصادق عليها
      await prefs.setString('user_name', 'أحمد محمد');
      await prefs.setBool('is_authenticated', false);
      isValid = await authProvider.validateStoredAuth();
      expect(isValid, false);
    });
  });
}
