import 'package:flutter/material.dart';
import 'package:wardlytec_app/models/delivery_company.dart';
import 'package:wardlytec_app/screens/new_transaction_screen.dart';
import 'package:wardlytec_app/widgets/connectivity_wrapper.dart';
import 'package:wardlytec_app/utils/app_theme.dart';
import 'package:wardlytec_app/services/connectivity_service.dart';

class CompanySelectionScreen extends StatefulWidget {
  const CompanySelectionScreen({Key? key}) : super(key: key);

  @override
  State<CompanySelectionScreen> createState() => _CompanySelectionScreenState();
}

class _CompanySelectionScreenState extends State<CompanySelectionScreen> {
  DeliveryCompany? _selectedCompany;

  void _onCompanySelected(DeliveryCompany company) {
    setState(() {
      _selectedCompany = company;
    });

    if (company.isServiceAvailable) {
      // الانتقال إلى شاشة التوريد الحالية
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => NewTransactionScreen(
            selectedCompany: company,
          ),
        ),
      );
    } else {
      // إظهار رسالة أن الخدمة ستكون متاحة قريباً
      _showServiceNotAvailableDialog(company);
    }
  }

  void _showServiceNotAvailableDialog(DeliveryCompany company) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Text(company.icon, style: const TextStyle(fontSize: 24)),
              const SizedBox(width: 8),
              Text(
                company.nameAr,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.construction,
                size: 48,
                color: Colors.orange,
              ),
              const SizedBox(height: 16),
              Text(
                'نعمل على إضافة هذه الخدمة في أقرب وقت ممكن',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            // زر حسناً في المنتصف مع تصميم محسن
            Center(
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 2,
                ),
                child: const Text(
                  'حسناً',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      showNoInternetCard: true, // تظهر بطاقة عدم الاتصال
      showQualityIndicator: false, // إخفاء مؤشر جودة الاتصال
      requiredQuality: ActionType.lightBrowsing, // تتطلب تصفح خفيف
      child: Scaffold(
      appBar: AppBar(
        title: const Text('اختيار الشركة'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الصفحة
            const Text(
              'اختر شركة التوصيل',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اختر الشركة التي تعمل معها لبدء عملية التوريد',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
            const SizedBox(height: 32),

            // قائمة الشركات
            Expanded(
              child: ListView.builder(
                itemCount: DeliveryCompany.values.length,
                itemBuilder: (context, index) {
                  final company = DeliveryCompany.values[index];
                  return Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: company.isServiceAvailable 
                              ? AppTheme.primaryColor.withOpacity(0.1)
                              : Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: Center(
                          child: Text(
                            company.icon,
                            style: const TextStyle(fontSize: 24),
                          ),
                        ),
                      ),
                      title: Text(
                        company.nameAr,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: company.serviceStatusMessage.isNotEmpty
                          ? Text(
                              company.serviceStatusMessage,
                              style: TextStyle(
                                fontSize: 14,
                                color: company.isServiceAvailable
                                    ? Colors.green
                                    : Colors.orange,
                              ),
                            )
                          : null,
                      trailing: Icon(
                        company.isServiceAvailable 
                            ? Icons.arrow_forward_ios 
                            : Icons.schedule,
                        color: company.isServiceAvailable 
                            ? AppTheme.primaryColor 
                            : Colors.orange,
                      ),
                      onTap: () => _onCompanySelected(company),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    ),
    );
  }
}
