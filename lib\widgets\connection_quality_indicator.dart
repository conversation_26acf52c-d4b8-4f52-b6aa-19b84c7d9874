import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wardlytec_app/services/connectivity_service.dart';

/// Widget لإظهار مؤشر جودة الاتصال
class ConnectionQualityIndicator extends StatefulWidget {
  final bool showAlways; // إظهار المؤشر دائماً أم فقط عند ضعف الاتصال
  final EdgeInsets? margin;
  final bool compact; // عرض مضغوط

  const ConnectionQualityIndicator({
    Key? key,
    this.showAlways = false,
    this.margin,
    this.compact = false,
  }) : super(key: key);

  @override
  State<ConnectionQualityIndicator> createState() => _ConnectionQualityIndicatorState();
}

class _ConnectionQualityIndicatorState extends State<ConnectionQualityIndicator> {
  final ConnectivityService _connectivityService = ConnectivityService();
  ConnectionQuality _currentQuality = ConnectionQuality.good;
  ConnectionInfo? _connectionInfo;
  StreamSubscription<ConnectionQuality>? _qualitySubscription;

  @override
  void initState() {
    super.initState();
    _loadConnectionInfo();
    
    // الاستماع لتغييرات جودة الاتصال
    _qualitySubscription = _connectivityService.connectionQuality.listen((quality) {
      if (mounted) {
        setState(() {
          _currentQuality = quality;
        });
        _loadConnectionInfo();
      }
    });
  }

  Future<void> _loadConnectionInfo() async {
    final info = await _connectivityService.getDetailedConnectionInfo();
    if (mounted) {
      setState(() {
        _connectionInfo = info;
        _currentQuality = info.quality;
      });
    }
  }

  @override
  void dispose() {
    _qualitySubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // إذا لم نريد الإظهار دائماً ولكن الاتصال جيد، لا نعرض شيء
    if (!widget.showAlways && _currentQuality == ConnectionQuality.good) {
      return const SizedBox.shrink();
    }

    // إذا لم تكن هناك معلومات بعد، لا نعرض شيء
    if (_connectionInfo == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: widget.margin ?? const EdgeInsets.all(8),
      padding: EdgeInsets.symmetric(
        horizontal: widget.compact ? 8 : 12,
        vertical: widget.compact ? 4 : 8,
      ),
      decoration: BoxDecoration(
        color: Color(_connectivityService.getQualityColor(_currentQuality)).withOpacity(0.9),
        borderRadius: BorderRadius.circular(widget.compact ? 6 : 8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: widget.compact ? _buildCompactContent() : _buildFullContent(),
    );
  }

  Widget _buildCompactContent() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          _getQualityIcon(_currentQuality),
          color: Colors.white,
          size: 14,
        ),
        const SizedBox(width: 4),
        Text(
          '${_connectionInfo!.responseTime}ms',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildFullContent() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          _getQualityIcon(_currentQuality),
          color: Colors.white,
          size: 16,
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _connectionInfo!.qualityText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${_connectionInfo!.responseTime}ms - ${_connectionInfo!.connectionType}',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ],
    );
  }

  IconData _getQualityIcon(ConnectionQuality quality) {
    switch (quality) {
      case ConnectionQuality.none:
        return Icons.wifi_off;
      case ConnectionQuality.poor:
        return Icons.wifi_1_bar;
      case ConnectionQuality.slow:
        return Icons.wifi_2_bar;
      case ConnectionQuality.moderate:
        return Icons.network_wifi;
      case ConnectionQuality.good:
        return Icons.wifi;
    }
  }
}

/// Widget بسيط لإظهار أيقونة جودة الاتصال فقط
class SimpleConnectionIndicator extends StatefulWidget {
  final double size;
  final Color? color;

  const SimpleConnectionIndicator({
    Key? key,
    this.size = 20,
    this.color,
  }) : super(key: key);

  @override
  State<SimpleConnectionIndicator> createState() => _SimpleConnectionIndicatorState();
}

class _SimpleConnectionIndicatorState extends State<SimpleConnectionIndicator> {
  final ConnectivityService _connectivityService = ConnectivityService();
  ConnectionQuality _currentQuality = ConnectionQuality.good;
  StreamSubscription<ConnectionQuality>? _qualitySubscription;

  @override
  void initState() {
    super.initState();
    _loadQuality();
    
    _qualitySubscription = _connectivityService.connectionQuality.listen((quality) {
      if (mounted) {
        setState(() {
          _currentQuality = quality;
        });
      }
    });
  }

  Future<void> _loadQuality() async {
    final quality = await _connectivityService.getConnectionQuality();
    if (mounted) {
      setState(() {
        _currentQuality = quality;
      });
    }
  }

  @override
  void dispose() {
    _qualitySubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Icon(
      _getQualityIcon(_currentQuality),
      size: widget.size,
      color: widget.color ?? Color(_connectivityService.getQualityColor(_currentQuality)),
    );
  }

  IconData _getQualityIcon(ConnectionQuality quality) {
    switch (quality) {
      case ConnectionQuality.none:
        return Icons.wifi_off;
      case ConnectionQuality.poor:
        return Icons.wifi_1_bar;
      case ConnectionQuality.slow:
        return Icons.wifi_2_bar;
      case ConnectionQuality.moderate:
        return Icons.network_wifi;
      case ConnectionQuality.good:
        return Icons.wifi;
    }
  }
}
