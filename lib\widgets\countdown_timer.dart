import 'dart:async';
import 'package:flutter/material.dart';


class CountdownTimer extends StatefulWidget {
  final VoidCallback? onTimerFinished;

  const CountdownTimer({
    Key? key,
    this.onTimerFinished,
  }) : super(key: key);

  @override
  State<CountdownTimer> createState() => _CountdownTimerState();
}

class _CountdownTimerState extends State<CountdownTimer>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Timer _timer;
  int _remainingSeconds = 300; // 5 دقائق = 300 ثانية
  bool _isFinished = false;

  @override
  void initState() {
    super.initState();
    
    // إعداد animation controller
    _animationController = AnimationController(
      duration: const Duration(seconds: 300), // 5 دقائق
      vsync: this,
    );

    // بدء العد التنازلي
    _startTimer();
    
    // بدء الانيميشن
    _animationController.forward();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          _isFinished = true;
          _timer.cancel();
          _animationController.stop();
          if (widget.onTimerFinished != null) {
            widget.onTimerFinished!();
          }
        }
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _animationController.dispose();
    super.dispose();
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }



  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120,
      height: 120,
      child: _isFinished
          ? _buildFinishedState()
          : _buildTimer(),
    );
  }

  Widget _buildTimer() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // الدائرة الخارجية (الخلفية)
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[800]
                : Colors.grey[200],
          ),
        ),
        
        // الدائرة المتحركة (التقدم)
        SizedBox(
          width: 120,
          height: 120,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return CircularProgressIndicator(
                value: 1.0 - _animationController.value,
                strokeWidth: 8,
                backgroundColor: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[700]
                    : Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _remainingSeconds > 60
                      ? (Theme.of(context).brightness == Brightness.dark
                          ? Colors.blue[400]!
                          : Colors.blue)
                      : _remainingSeconds > 30
                          ? (Theme.of(context).brightness == Brightness.dark
                              ? Colors.orange[400]!
                              : Colors.orange)
                          : (Theme.of(context).brightness == Brightness.dark
                              ? Colors.red[400]!
                              : Colors.red),
                ),
              );
            },
          ),
        ),
        
        // النص في المنتصف
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.timer,
              size: 24,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(_remainingSeconds),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.titleLarge?.color,
              ),
            ),
            Text(
              'متبقي',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinishedState() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isDark ? Colors.green[900]?.withOpacity(0.3) : Colors.green[100],
        border: Border.all(
          color: isDark ? Colors.green[400]! : Colors.green[300]!,
          width: 2
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            size: 32,
            color: isDark ? Colors.green[400] : Colors.green[600],
          ),
          const SizedBox(height: 4),
          Text(
            'انتهى',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.green[300] : Colors.green[700],
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            'الوقت',
            style: TextStyle(
              fontSize: 12,
              color: isDark ? Colors.green[400] : Colors.green[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
