import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/screens/auth/secret_code_screen.dart';

void main() {
  group('SecretCodeScreen Widget Tests', () {
    late SupabaseAuthProvider mockAuthProvider;

    setUp(() {
      mockAuthProvider = SupabaseAuthProvider();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<SupabaseAuthProvider>(
          create: (_) => mockAuthProvider,
          child: const SecretCodeScreen(
            customerName: 'أحمد محمد',
            phoneNumber: '+201234567890',
          ),
        ),
      );
    }

    testWidgets('Should display page title', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من عنوان الصفحة
      expect(find.text('تفعيل الحساب'), findsOneWidget);
    });

    testWidgets('Should display password input field', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من وجود حقل كلمة السر
      expect(find.byType(TextFormField), findsOneWidget);
      expect(find.text('كلمة السر'), findsOneWidget);
      expect(find.text('123456'), findsOneWidget); // placeholder
    });

    testWidgets('Should display login button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من وجود زر تسجيل الدخول
      expect(find.text('تسجيل الدخول'), findsOneWidget);
      expect(find.byIcon(Icons.login), findsOneWidget);
    });

    testWidgets('Should display request password button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من وجود زر طلب كلمة السر
      expect(find.text('طلب كلمة السر'), findsOneWidget);
      expect(find.byIcon(Icons.message), findsOneWidget);
    });

    testWidgets('Should not display countdown timer initially', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من عدم وجود المؤقت في البداية
      expect(find.text('تم إرسال طلب كلمة السر'), findsNothing);
    });

    testWidgets('Should display countdown timer after requesting password and keep it visible', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // النقر على زر طلب كلمة السر
      await tester.tap(find.text('طلب كلمة السر'));
      await tester.pump();

      // التحقق من ظهور المؤقت
      expect(find.text('تم إرسال طلب كلمة السر'), findsOneWidget);

      // المؤقت يجب أن يبقى ظاهراً حتى لو انتهى العد
      // (في الاختبار لا يمكننا انتظار 5 دقائق، لكن المنطق موجود)
    });

    testWidgets('Should display user registration data', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من وجود بطاقة بيانات التسجيل (بطاقة واحدة فقط)
      expect(find.text('بيانات التسجيل'), findsOneWidget);
      expect(find.text('الاسم: أحمد محمد علي'), findsOneWidget);
      expect(find.text('الهاتف: +201234567890'), findsOneWidget);
    });

    testWidgets('Should validate password input', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // العثور على حقل كلمة السر
      final passwordField = find.byType(TextFormField);
      
      // إدخال كلمة سر قصيرة
      await tester.enterText(passwordField, '123');
      await tester.pump();

      // النقر على زر تسجيل الدخول لتفعيل التحقق
      await tester.tap(find.text('تسجيل الدخول'));
      await tester.pump();

      // التحقق من رسالة الخطأ
      expect(find.text('كلمة السر قصيرة جداً'), findsOneWidget);
    });

    testWidgets('Should accept valid password input', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // العثور على حقل كلمة السر
      final passwordField = find.byType(TextFormField);
      
      // إدخال كلمة سر صحيحة
      await tester.enterText(passwordField, '123456');
      await tester.pump();

      // التحقق من عدم وجود رسائل خطأ
      expect(find.text('كلمة السر قصيرة جداً'), findsNothing);
    });

    testWidgets('Should show loading state when requesting password', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // النقر على زر طلب كلمة السر
      await tester.tap(find.text('طلب كلمة السر'));
      await tester.pump();

      // التحقق من حالة التحميل
      expect(find.text('جاري الإرسال...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Should show loading state when logging in', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // إدخال كلمة سر صحيحة
      final passwordField = find.byType(TextFormField);
      await tester.enterText(passwordField, '123456');
      await tester.pump();

      // النقر على زر تسجيل الدخول
      await tester.tap(find.text('تسجيل الدخول'));
      await tester.pump();

      // التحقق من حالة التحميل
      expect(find.text('جاري تسجيل الدخول...'), findsOneWidget);
    });

    testWidgets('Should display page title correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من عنوان الصفحة
      expect(find.text('تفعيل الحساب'), findsOneWidget);
    });

    testWidgets('Should have proper styling for buttons', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من وجود الأزرار بالأنواع الصحيحة - كلاهما ElevatedButton الآن
      expect(find.byType(ElevatedButton), findsNWidgets(2)); // زر تسجيل الدخول + زر طلب كلمة السر
    });

    testWidgets('Should disable request password button during timer', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // النقر على زر طلب كلمة السر
      await tester.tap(find.text('طلب كلمة السر'));
      await tester.pump();

      // التحقق من تغيير نص الزر
      expect(find.text('تم إرسال الطلب'), findsOneWidget);

      // التحقق من ظهور المؤقت
      expect(find.textContaining('تم إرسال طلب كلمة السر'), findsOneWidget);
    });

    testWidgets('Should disable login button when password field is empty', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من أن زر تسجيل الدخول معطل في البداية
      expect(find.text('تسجيل الدخول'), findsOneWidget);
    });

    testWidgets('Should enable login button when password is entered', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // إدخال كلمة سر
      final passwordField = find.byType(TextFormField);
      await tester.enterText(passwordField, '123456');
      await tester.pump();

      // التحقق من تفعيل زر تسجيل الدخول
      expect(find.text('تسجيل الدخول'), findsOneWidget);

      // التحقق من تفعيل الزر
      expect(find.text('تسجيل الدخول'), findsOneWidget);
    });

    testWidgets('Login button should have same design as account creation button when enabled', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // إدخال كلمة سر لتفعيل الزر
      final passwordField = find.byType(TextFormField);
      await tester.enterText(passwordField, '123456');
      await tester.pump();

      // العثور على زر تسجيل الدخول
      final loginButton = find.ancestor(
        of: find.text('تسجيل الدخول'),
        matching: find.byType(ElevatedButton),
      );

      expect(loginButton, findsOneWidget);

      // التحقق من أن الزر من نوع ElevatedButton.icon (نفس نوع زر تفعيل الحساب)
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('Should disable login button again when password is cleared', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // إدخال كلمة سر ثم مسحها
      final passwordField = find.byType(TextFormField);
      await tester.enterText(passwordField, '123456');
      await tester.pump();

      await tester.enterText(passwordField, '');
      await tester.pump();

      // التحقق من تعطيل زر تسجيل الدخول مرة أخرى
      expect(find.text('أدخل كلمة السر أولاً'), findsOneWidget);
    });
  });
}
