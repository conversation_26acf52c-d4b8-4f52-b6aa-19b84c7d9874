import 'package:flutter/foundation.dart';

/// إعدادات الأمان للتطبيق
class SecurityConfig {
  // منع إنشاء instance من هذا الكلاس
  SecurityConfig._();

  /// التحقق من أن التطبيق يعمل في بيئة آمنة
  static bool get isSecureEnvironment {
    // في وضع الإنتاج، يجب أن تكون البيئة آمنة
    if (kReleaseMode) {
      return _validateProductionEnvironment();
    }
    // في وضع التطوير، نسمح بمرونة أكثر
    return true;
  }

  /// التحقق من صحة بيئة الإنتاج
  static bool _validateProductionEnvironment() {
    // التحقق من المتغيرات المطلوبة واحداً تلو الآخر
    const supabaseUrl = String.fromEnvironment('SUPABASE_URL');
    const supabaseKey = String.fromEnvironment('SUPABASE_ANON_KEY');

    if (supabaseUrl.isEmpty || supabaseUrl.contains('your_') || supabaseUrl.contains('example')) {
      if (kDebugMode) {
        print('⚠️ متغير البيئة المطلوب غير موجود أو غير صحيح: SUPABASE_URL');
      }
      return false;
    }

    if (supabaseKey.isEmpty || supabaseKey.contains('your_') || supabaseKey.contains('example')) {
      if (kDebugMode) {
        print('⚠️ متغير البيئة المطلوب غير موجود أو غير صحيح: SUPABASE_ANON_KEY');
      }
      return false;
    }

    return true;
  }

  /// التحقق من أن المفاتيح ليست قيم افتراضية
  static bool validateApiKeys() {
    final supabaseUrl = const String.fromEnvironment('SUPABASE_URL');
    final supabaseKey = const String.fromEnvironment('SUPABASE_ANON_KEY');

    // التحقق من أن المفاتيح ليست قيم افتراضية
    if (supabaseUrl.contains('your_') || 
        supabaseKey.contains('your_') ||
        supabaseUrl.contains('example') ||
        supabaseKey.contains('example')) {
      return false;
    }

    return true;
  }

  /// طباعة تحذيرات الأمان
  static void printSecurityWarnings() {
    if (!kReleaseMode) {
      print('🔒 تحذيرات الأمان:');
      
      if (!validateApiKeys()) {
        print('⚠️ يتم استخدام مفاتيح افتراضية - يجب تحديثها للإنتاج');
      }
      
      if (!isSecureEnvironment) {
        print('⚠️ البيئة الحالية غير آمنة للإنتاج');
      }
      
      print('📋 للإنتاج الآمن:');
      print('   1. استخدم متغيرات البيئة الحقيقية');
      print('   2. فعل وضع الإنتاج (Release Mode)');
      print('   3. استخدم مفاتيح قوية ومعقدة');
      print('   4. لا تشارك المفاتيح مع أي شخص');
    }
  }

  /// قائمة بالمجالات الموثوقة
  static const List<String> trustedDomains = [
    'supabase.co',
    'googleapis.com',
    'googleadservices.com',
    'whatsapp.com',
    'instapay.eg',
  ];

  /// التحقق من أن الرابط من مجال موثوق
  static bool isTrustedUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final host = uri.host.toLowerCase();
      
      return trustedDomains.any((domain) => 
        host == domain || host.endsWith('.$domain'));
    } catch (e) {
      return false;
    }
  }

  /// إعدادات الشبكة الآمنة
  static const Duration networkTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  
  /// حد أقصى لحجم البيانات المرسلة
  static const int maxDataSize = 10 * 1024 * 1024; // 10 MB

  /// التحقق من صحة رقم الهاتف
  static bool isValidPhoneNumber(String phone) {
    // نمط بسيط للتحقق من رقم الهاتف المصري
    final phoneRegex = RegExp(r'^(\+20|0)?1[0-9]{9}$');
    return phoneRegex.hasMatch(phone.replaceAll(RegExp(r'\s+'), ''));
  }

  /// التحقق من قوة كلمة المرور/الرمز السري
  static bool isStrongSecretCode(String code) {
    // يجب أن يكون الرمز السري على الأقل 6 أحرف
    if (code.length < 6) return false;
    
    // يجب أن يحتوي على أرقام وحروف
    final hasNumbers = RegExp(r'[0-9]').hasMatch(code);
    final hasLetters = RegExp(r'[a-zA-Z]').hasMatch(code);
    
    return hasNumbers && hasLetters;
  }

  /// تنظيف البيانات الحساسة من الذاكرة
  static void clearSensitiveData() {
    // في Flutter، الـ Garbage Collector يتولى هذا الأمر
    // لكن يمكن إضافة منطق إضافي هنا إذا لزم الأمر
    if (kDebugMode) {
      print('🧹 تم تنظيف البيانات الحساسة من الذاكرة');
    }
  }

  /// إعدادات التشفير (للمستقبل)
  static const String encryptionAlgorithm = 'AES-256-GCM';
  static const int keyLength = 32; // 256 bits
  static const int ivLength = 16; // 128 bits

  /// التحقق من إعدادات الأمان عند بدء التطبيق
  static void validateSecuritySettings() {
    if (kDebugMode) {
      print('🔐 فحص إعدادات الأمان...');
      
      if (isSecureEnvironment) {
        print('✅ البيئة آمنة');
      } else {
        print('❌ البيئة غير آمنة - يجب المراجعة');
      }
      
      if (validateApiKeys()) {
        print('✅ مفاتيح API صحيحة');
      } else {
        print('❌ مفاتيح API تحتاج للتحديث');
      }
      
      print('🔒 فحص الأمان مكتمل');
    }
  }
}
