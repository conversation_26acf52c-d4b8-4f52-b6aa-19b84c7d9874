#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بوت إشعارات وردلي - وظيفة واحدة فقط
Wardly Notifications Bot - Single Function Only

الوظيفة: استقبال المعاملات الجديدة وإرسال إشعار بسيط للمدير
Function: Receive new transactions and send simple notification to admin
"""

import os
import logging
import requests
import json
from datetime import datetime
from flask import Flask, request, jsonify

# إعدادات البوت - يجب استخدام متغيرات البيئة في الإنتاج
BOT_TOKEN = os.getenv('BOT_TOKEN')
ADMIN_CHAT_ID = os.getenv('ADMIN_CHAT_ID')

# إعدادات Supabase - يجب استخدام متغيرات البيئة في الإنتاج
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# إعداد Flask
app = Flask(__name__)

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# التحقق من وجود المتغيرات المطلوبة للإشعارات فقط
if not all([BOT_TOKEN, ADMIN_CHAT_ID, SUPABASE_URL, SUPABASE_KEY]):
    logger.error("❌ متغيرات البيئة المطلوبة للإشعارات غير موجودة!")
    logger.error("مطلوب: BOT_TOKEN, ADMIN_CHAT_ID, SUPABASE_URL, SUPABASE_KEY")
    logger.error("الوظيفة: إشعارات المعاملات فقط")
    exit(1)

def send_message(chat_id: str, text: str):
    """إرسال رسالة عادية"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
    data = {
        'chat_id': chat_id,
        'text': text,
        'parse_mode': 'Markdown'
    }

    try:
        response = requests.post(url, json=data)
        return response.json()
    except Exception as e:
        logger.error(f"خطأ في إرسال الرسالة: {e}")
        return None



def edit_message(chat_id: str, message_id: int, new_text: str):
    """تعديل رسالة موجودة"""
    url = f"https://api.telegram.org/bot{BOT_TOKEN}/editMessageText"
    data = {
        'chat_id': chat_id,
        'message_id': message_id,
        'text': new_text
    }

    try:
        response = requests.post(url, json=data)
        return response.json()
    except Exception as e:
        logger.error(f"خطأ في تعديل الرسالة: {e}")
        return None











def update_transaction_status(transaction_id: str, new_status: str) -> bool:
    """تحديث حالة المعاملة في Supabase"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/transactions"
        headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'status': new_status,
            'updated_at': datetime.now().isoformat()
        }
        
        response = requests.patch(
            f"{url}?id=eq.{transaction_id}",
            headers=headers,
            json=data
        )
        
        if response.status_code == 204:
            logger.info(f"✅ تم تحديث حالة المعاملة {transaction_id} إلى {new_status}")
            return True
        else:
            logger.error(f"❌ فشل تحديث المعاملة: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في تحديث حالة المعاملة: {e}")
        return False

def get_transaction_details(transaction_id: str) -> dict:
    """الحصول على تفاصيل المعاملة من قاعدة البيانات"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/transactions?id=eq.{transaction_id}&select=*"
        headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                return data[0]

        logger.error(f"❌ لم يتم العثور على المعاملة {transaction_id}")
        return {}

    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على تفاصيل المعاملة {transaction_id}: {e}")
        return {}



def handle_message(message):
    """معالجة الرسائل الواردة"""
    try:
        chat_id = str(message['chat']['id'])
        text = message.get('text', '')
        
        if text == '/start':
            welcome_message = """
🤖 بوت إشعارات وردلي

وظيفة واحدة: استقبال المعاملات الجديدة وإرسال إشعار للمدير.

📥 جاهز لاستقبال المعاملات
🚫 لا يتفاعل مع الرسائل
"""
            send_message(chat_id, welcome_message)
            
        elif text == '/help':
            help_message = """
📋 بوت معاملات وردلي:

🎯 الوظيفة الوحيدة:
- استقبال المعاملات الجديدة فقط
- إرسال إشعار بسيط للمدير
- عرض تفاصيل المعاملة مع الصورة

🚫 لا يتفاعل مع الأوامر أو الأزرار
💡 مخصص للإشعارات فقط
"""
            send_message(chat_id, help_message)
            
        elif text == '/status':
            status_message = f"""
📊 حالة البوت:

✅ نشط - جاهز لاستقبال المعاملات
🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📥 وظيفة واحدة: إشعارات المعاملات
"""
            send_message(chat_id, status_message)
            
    except Exception as e:
        logger.error(f"خطأ في معالجة الرسالة: {e}")

@app.route('/webhook', methods=['POST'])
def webhook():
    """استقبال التحديثات من Telegram"""
    try:
        update = request.get_json()
        
        if 'message' in update:
            handle_message(update['message'])
            
        return jsonify({'status': 'ok'})
        
    except Exception as e:
        logger.error(f"خطأ في webhook: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    """فحص صحة البوت"""
    return jsonify({
        'status': 'active',
        'function': 'transaction_notifications_only',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/', methods=['GET'])
def home():
    """الصفحة الرئيسية"""
    return jsonify({
        'message': 'Wardly Notifications Bot - Transaction Alerts Only',
        'function': 'Receive and notify about new transactions',
        'status': 'active',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)
