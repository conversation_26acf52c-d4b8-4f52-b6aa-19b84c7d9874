# ⚡ سيناريو اختبار سريع - 5 دقائق

## 🎯 **الهدف: اختبار نظام الإشعارات في 5 دقائق**

---

## 🚀 **الخطوة 1: التحضير (دقيقة واحدة)**

### **تثبيت التبعيات:**
```bash
flutter pub get
```

### **إعداد قاعدة البيانات:**
```sql
-- في Supabase SQL Editor، انسخ والصق:
-- محتوى ملف: database_setup/create_notifications_tables.sql
-- اضغط "Run"
```

---

## 📱 **الخطوة 2: اختبار الإشعارات التجريبية (دقيقة واحدة)**

### **تشغيل التطبيق:**
```bash
flutter run
```

### **اختبار الإشعار:**
```
1. سجل دخول للتطبيق
2. القائمة الجانبية → "إعدادات الإشعارات"
3. اضغط "إرسال إشعار تجريبي"
4. ✅ يجب أن تحصل على إشعار فوري!
```

**✅ النتيجة المتوقعة:**
```
📱 إشعار في الجهاز:
"🧪 إشعار تجريبي
هذا إشعار تجريبي للتأكد من عمل النظام"
```

---

## 🔄 **الخطوة 3: اختبار مراقبة المعاملات (دقيقتان)**

### **إنشاء معاملة:**
```
1. في التطبيق: اضغط "بدء التوريد"
2. اختر شركة ومبلغ
3. ارفع صورة وهمية
4. اضغط "إرسال المعاملة"
```

### **محاكاة تحديث من المسؤول:**
```
1. اذهب لـ Supabase Dashboard
2. Table Editor → transactions
3. ابحث عن معاملتك الجديدة
4. غير الـ status من "pending" إلى "approved"
5. اضغط Save
```

**✅ النتيجة المتوقعة:**
```
📱 إشعار فوري في الجهاز:
"✅ تم قبول معاملة التوريد
المعاملة #TXN123 بقيمة 100.00 ج.م
تم تحديث الحالة إلى: مقبولة"
```

---

## 🛡️ **الخطوة 4: اختبار صفحة المسؤول (دقيقة واحدة)**

### **الدخول كمسؤول:**
```
1. في الصفحة الرئيسية
2. اضغط على عنوان "وردلي تك" 7 مرات سريعاً
3. أدخل PIN: 123456
4. ✅ يجب أن تفتح صفحة تحكم المسؤول
```

### **اختبار تعطيل الخدمات:**
```
1. في صفحة المسؤول
2. عطل "خدمة التوريد"
3. اكتب رسالة: "صيانة مؤقتة"
4. اضغط حفظ
```

**✅ النتيجة المتوقعة:**
```
📱 إشعار فوري:
"⚠️ تعطيل خدمة التوريد
صيانة مؤقتة"

🚫 عند محاولة الدخول للتوريد:
صفحة "الخدمة غير متاحة"
```

---

## ⚙️ **الخطوة 5: اختبار الإعدادات (دقيقة واحدة)**

### **تخصيص الإشعارات:**
```
1. إعدادات الإشعارات
2. عطل "العروض والترويج"
3. فعل "إشعارات الصيانة"
4. اضغط حفظ
```

### **اختبار الإعدادات:**
```
1. ارجع لصفحة المسؤول
2. أرسل إشعار ترويجي وهمي
3. ✅ يجب ألا تحصل على الإشعار (معطل)
4. أرسل إشعار صيانة
5. ✅ يجب أن تحصل على الإشعار (مفعل)
```

---

## 📊 **نتائج الاختبار المتوقعة:**

### **✅ إذا نجحت جميع الخطوات:**
```
🎉 النظام يعمل بشكل مثالي!
✅ الإشعارات التجريبية تعمل
✅ مراقبة المعاملات تعمل
✅ صفحة المسؤول تعمل
✅ تعطيل الخدمات يعمل
✅ تخصيص الإشعارات يعمل
```

### **❌ إذا فشلت أي خطوة:**

#### **لا تظهر إشعارات:**
```
🔧 الحلول:
1. تحقق من أذونات الإشعارات في الجهاز
2. أعد تشغيل التطبيق
3. تأكد من تشغيل ملف SQL
```

#### **لا تعمل المراقبة:**
```
🔧 الحلول:
1. تحقق من اتصال الإنترنت
2. راجع console للأخطاء
3. تأكد من صحة إعدادات Supabase
```

#### **لا تفتح صفحة المسؤول:**
```
🔧 الحلول:
1. اضغط 7 مرات سريعاً (خلال 3 ثوان)
2. تأكد من PIN: 123456
3. راجع console للأخطاء
```

---

## 🎯 **ملخص الاختبار:**

| الخطوة | الوقت | الحالة |
|--------|-------|--------|
| **التحضير** | 1 دقيقة | ⏳ |
| **الإشعارات التجريبية** | 1 دقيقة | ⏳ |
| **مراقبة المعاملات** | 2 دقيقة | ⏳ |
| **صفحة المسؤول** | 1 دقيقة | ⏳ |
| **الإعدادات** | 1 دقيقة | ⏳ |
| **المجموع** | **5 دقائق** | **⏳** |

---

## 📞 **الدعم:**

إذا واجهت مشاكل:
1. راجع `docs/notification_system_scenario.md` للسيناريو الشامل
2. راجع `docs/notification_system_guide.md` للدليل التقني
3. تحقق من logs التطبيق
4. تواصل مع فريق التطوير

---

**⚡ اختبار سريع في 5 دقائق - جرب الآن! 🚀**
