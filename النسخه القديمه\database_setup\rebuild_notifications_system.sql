-- ========================================
-- 🔄 إعادة بناء شاملة لنظام الإشعارات المبسط
-- ========================================
-- 🚀 هذا الملف يحذف كل شيء ويبني النظام من الصفر
-- ⚠️ تحذير: سيتم حذف جميع البيانات الموجودة!

-- ========================================
-- المرحلة 1: حذف شامل لكل شيء
-- ========================================

DO $$
BEGIN
    RAISE NOTICE '🗑️ بدء عملية الحذف الشامل...';
END $$;

-- 1.1 حذف جميع السياسات (Policies)
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    -- حذف سياسات notification_logs
    FOR policy_record IN 
        SELECT policyname FROM pg_policies 
        WHERE tablename = 'notification_logs'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON notification_logs CASCADE';
    END LOOP;
    
    -- حذف سياسات user_notification_settings
    FOR policy_record IN 
        SELECT policyname FROM pg_policies 
        WHERE tablename = 'user_notification_settings'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON user_notification_settings CASCADE';
    END LOOP;
    
    RAISE NOTICE '✅ تم حذف جميع السياسات';
END $$;

-- 1.2 حذف جميع الدوال
DROP FUNCTION IF EXISTS cleanup_old_notifications() CASCADE;
DROP FUNCTION IF EXISTS cleanup_old_notifications(INTEGER) CASCADE;
DROP FUNCTION IF EXISTS cleanup_old_notifications(INTERVAL) CASCADE;
DROP FUNCTION IF EXISTS log_notification(UUID, VARCHAR, TEXT, TEXT, JSONB) CASCADE;
DROP FUNCTION IF EXISTS log_notification(UUID, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS log_notification(UUID, VARCHAR, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS get_notification_stats(UUID) CASCADE;
DROP FUNCTION IF EXISTS get_notification_stats() CASCADE;
DROP FUNCTION IF EXISTS get_user_notification_settings(UUID) CASCADE;
DROP FUNCTION IF EXISTS update_user_notification_setting(UUID, VARCHAR, BOOLEAN) CASCADE;
DROP FUNCTION IF EXISTS create_default_notification_settings(UUID) CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- 1.3 حذف جميع الـ Triggers
DROP TRIGGER IF EXISTS update_user_notification_settings_updated_at ON user_notification_settings CASCADE;
DROP TRIGGER IF EXISTS update_notification_logs_updated_at ON notification_logs CASCADE;

-- 1.4 حذف جميع الفهارس
DROP INDEX IF EXISTS idx_user_notification_settings_user_id CASCADE;
DROP INDEX IF EXISTS idx_user_notification_settings_type CASCADE;
DROP INDEX IF EXISTS idx_notification_logs_user_id CASCADE;
DROP INDEX IF EXISTS idx_notification_logs_type CASCADE;
DROP INDEX IF EXISTS idx_notification_logs_sent_at CASCADE;

-- 1.5 حذف جميع الجداول
DROP TABLE IF EXISTS notification_logs CASCADE;
DROP TABLE IF EXISTS user_notification_settings CASCADE;

DO $$
BEGIN
    RAISE NOTICE '🧹 تم الانتهاء من الحذف الشامل';
    RAISE NOTICE '';
    RAISE NOTICE '🔨 بدء إعادة البناء...';
END $$;

-- ========================================
-- المرحلة 2: إعادة البناء من الصفر
-- ========================================

-- 2.1 إنشاء جدول سجل الإشعارات الجديد
CREATE TABLE notification_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL,
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    payload JSONB,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2.2 إنشاء الفهارس للأداء
CREATE INDEX idx_notification_logs_user_id ON notification_logs(user_id);
CREATE INDEX idx_notification_logs_type ON notification_logs(notification_type);
CREATE INDEX idx_notification_logs_sent_at ON notification_logs(sent_at);

-- 2.3 تفعيل Row Level Security
ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;

-- 2.4 إنشاء السياسات الجديدة
CREATE POLICY "Users can view their own notification logs" ON notification_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert notification logs" ON notification_logs
    FOR INSERT WITH CHECK (true);

-- 2.5 إنشاء دالة تسجيل الإشعارات
CREATE OR REPLACE FUNCTION log_notification(
    p_user_id UUID,
    p_notification_type VARCHAR(50),
    p_title TEXT,
    p_body TEXT,
    p_payload JSONB DEFAULT NULL
)
RETURNS BIGINT AS $$
DECLARE
    notification_id BIGINT;
BEGIN
    INSERT INTO notification_logs (user_id, notification_type, title, body, payload)
    VALUES (p_user_id, p_notification_type, p_title, p_body, p_payload)
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2.6 إنشاء دالة تنظيف الإشعارات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
    DELETE FROM notification_logs
    WHERE sent_at < NOW() - INTERVAL '90 days';

    RAISE NOTICE 'تم تنظيف الإشعارات القديمة';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2.7 إنشاء دالة إحصائيات الإشعارات
CREATE OR REPLACE FUNCTION get_notification_stats(p_user_id UUID DEFAULT NULL)
RETURNS TABLE(
    notification_type VARCHAR(50),
    total_count BIGINT,
    today_count BIGINT,
    this_week_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        nl.notification_type,
        COUNT(*) as total_count,
        COUNT(*) FILTER (WHERE DATE(nl.sent_at) = CURRENT_DATE) as today_count,
        COUNT(*) FILTER (WHERE nl.sent_at >= DATE_TRUNC('week', NOW())) as this_week_count
    FROM notification_logs nl
    WHERE (p_user_id IS NULL OR nl.user_id = p_user_id)
    GROUP BY nl.notification_type
    ORDER BY total_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- المرحلة 3: منح الأذونات
-- ========================================

-- منح أذونات للمستخدمين المصادق عليهم
GRANT SELECT ON notification_logs TO authenticated;
GRANT EXECUTE ON FUNCTION log_notification TO authenticated;
GRANT EXECUTE ON FUNCTION get_notification_stats TO authenticated;

-- منح أذونات للخدمة (service_role)
GRANT ALL ON notification_logs TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_old_notifications TO service_role;

-- منح أذونات على sequence
GRANT USAGE, SELECT ON SEQUENCE notification_logs_id_seq TO authenticated;
GRANT ALL ON SEQUENCE notification_logs_id_seq TO service_role;

-- ========================================
-- المرحلة 4: إدراج بيانات تجريبية (اختياري)
-- ========================================

-- إدراج إشعار تجريبي للتأكد من عمل النظام
DO $$
DECLARE
    test_user_id UUID;
BEGIN
    -- البحث عن أول مستخدم في النظام
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;

    IF test_user_id IS NOT NULL THEN
        -- إدراج إشعار تجريبي
        PERFORM log_notification(
            test_user_id,
            'system_test',
            '🧪 إشعار تجريبي',
            'تم إعداد نظام الإشعارات بنجاح! هذا إشعار تجريبي للتأكد من عمل النظام.',
            '{"test": true, "setup_time": "' || NOW() || '"}'::jsonb
        );

        RAISE NOTICE '✅ تم إدراج إشعار تجريبي للمستخدم: %', test_user_id;
    ELSE
        RAISE NOTICE 'ℹ️ لا يوجد مستخدمين في النظام - لم يتم إدراج إشعار تجريبي';
    END IF;
END $$;

-- ========================================
-- المرحلة 5: رسائل التأكيد النهائية
-- ========================================

DO $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
    policy_count INTEGER;
BEGIN
    -- عد الجداول
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_name = 'notification_logs' AND table_schema = 'public';

    -- عد الدوال
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines
    WHERE routine_name IN ('log_notification', 'cleanup_old_notifications', 'get_notification_stats')
    AND routine_schema = 'public';

    -- عد السياسات
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies
    WHERE tablename = 'notification_logs';

    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '✅ تم إعادة بناء نظام الإشعارات بنجاح!';
    RAISE NOTICE '🎉 ===============================================';
    RAISE NOTICE '';
    RAISE NOTICE '📊 إحصائيات النظام الجديد:';
    RAISE NOTICE '   📋 الجداول: % جدول', table_count;
    RAISE NOTICE '   ⚙️ الدوال: % دالة', function_count;
    RAISE NOTICE '   🛡️ السياسات: % سياسة', policy_count;
    RAISE NOTICE '';
    RAISE NOTICE '🎯 الميزات المتاحة:';
    RAISE NOTICE '   • إرسال إشعارات تلقائية عند تغيير حالة المعاملات';
    RAISE NOTICE '   • إرسال إشعارات عند تعطيل/تفعيل الخدمات';
    RAISE NOTICE '   • تسجيل جميع الإشعارات المرسلة';
    RAISE NOTICE '   • إحصائيات مفصلة للإشعارات';
    RAISE NOTICE '   • تنظيف تلقائي للإشعارات القديمة';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 النظام جاهز للاستخدام!';
    RAISE NOTICE '📱 جميع الإشعارات مفعلة تلقائياً بدون إعدادات مستخدم';
    RAISE NOTICE '';
END $$;
