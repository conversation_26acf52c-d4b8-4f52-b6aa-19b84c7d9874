-- إنشاء جدول حالة الخدمات لتطبيق وردلي تك
-- Create service status table for Wardly Tech app
-- 🚀 شغل هذا الملف في Supabase SQL Editor

-- ========================================
-- 1. إنشاء جدول حالة الخدمات
-- ========================================

CREATE TABLE IF NOT EXISTS service_status (
    id BIGSERIAL PRIMARY KEY,
    service_name VARCHAR(50) UNIQUE NOT NULL,
    is_enabled BOOLEAN DEFAULT true,
    disabled_message TEXT DEFAULT 'الخدمة غير متاحة حالياً، يرجى المحاولة لاحقاً',
    custom_title TEXT DEFAULT 'الخدمة غير متاحة',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by VARCHAR(100) DEFAULT 'system'
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_service_status_name ON service_status(service_name);
CREATE INDEX IF NOT EXISTS idx_service_status_enabled ON service_status(is_enabled);

-- ========================================
-- 2. إنشاء دالة تحديث updated_at تلقائياً
-- ========================================

CREATE OR REPLACE FUNCTION update_service_status_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث updated_at
DROP TRIGGER IF EXISTS trigger_update_service_status_updated_at ON service_status;
CREATE TRIGGER trigger_update_service_status_updated_at
    BEFORE UPDATE ON service_status
    FOR EACH ROW
    EXECUTE FUNCTION update_service_status_updated_at();

-- ========================================
-- 3. إدراج البيانات الافتراضية
-- ========================================

INSERT INTO service_status (service_name, is_enabled, disabled_message, custom_title) VALUES
('supply_transactions', true, 'خدمة التوريد غير متاحة حالياً. نعتذر عن الإزعاج ونعمل على إعادة تفعيلها في أقرب وقت ممكن.', 'خدمة التوريد غير متاحة'),
('recharge_transactions', true, 'خدمة الشحن غير متاحة حالياً. نعتذر عن الإزعاج ونعمل على إعادة تفعيلها في أقرب وقت ممكن.', 'خدمة الشحن غير متاحة')
ON CONFLICT (service_name) DO NOTHING;

-- ========================================
-- 4. إعداد Row Level Security
-- ========================================

-- تفعيل RLS
ALTER TABLE service_status ENABLE ROW LEVEL SECURITY;

-- سياسة للقراءة (يمكن لأي شخص قراءة حالة الخدمات)
DROP POLICY IF EXISTS "Allow read access to service status" ON service_status;
CREATE POLICY "Allow read access to service status" ON service_status 
FOR SELECT USING (true);

-- سياسة للكتابة (يمكن للمدير فقط تحديث حالة الخدمات)
DROP POLICY IF EXISTS "Allow admin to update service status" ON service_status;
CREATE POLICY "Allow admin to update service status" ON service_status 
FOR ALL USING (true); -- مؤقت للتطوير - يجب تشديده لاحقاً

-- ========================================
-- 5. إنشاء دوال مساعدة
-- ========================================

-- دالة للحصول على حالة خدمة معينة
CREATE OR REPLACE FUNCTION get_service_status(service_name_param VARCHAR)
RETURNS TABLE(
    service_name VARCHAR,
    is_enabled BOOLEAN,
    disabled_message TEXT,
    custom_title TEXT,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.service_name,
        s.is_enabled,
        s.disabled_message,
        s.custom_title,
        s.updated_at
    FROM service_status s
    WHERE s.service_name = service_name_param;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث حالة خدمة معينة
CREATE OR REPLACE FUNCTION update_service_status(
    service_name_param VARCHAR,
    is_enabled_param BOOLEAN,
    disabled_message_param TEXT DEFAULT NULL,
    custom_title_param TEXT DEFAULT NULL,
    updated_by_param VARCHAR DEFAULT 'admin'
)
RETURNS BOOLEAN AS $$
DECLARE
    rows_affected INTEGER;
BEGIN
    UPDATE service_status 
    SET 
        is_enabled = is_enabled_param,
        disabled_message = COALESCE(disabled_message_param, disabled_message),
        custom_title = COALESCE(custom_title_param, custom_title),
        updated_by = updated_by_param
    WHERE service_name = service_name_param;
    
    GET DIAGNOSTICS rows_affected = ROW_COUNT;
    
    RETURN rows_affected > 0;
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على جميع حالات الخدمات
CREATE OR REPLACE FUNCTION get_all_service_statuses()
RETURNS TABLE(
    service_name VARCHAR,
    is_enabled BOOLEAN,
    disabled_message TEXT,
    custom_title TEXT,
    updated_at TIMESTAMP WITH TIME ZONE,
    updated_by VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.service_name,
        s.is_enabled,
        s.disabled_message,
        s.custom_title,
        s.updated_at,
        s.updated_by
    FROM service_status s
    ORDER BY s.service_name;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 6. إنشاء جدول سجل التغييرات (اختياري)
-- ========================================

CREATE TABLE IF NOT EXISTS service_status_log (
    id BIGSERIAL PRIMARY KEY,
    service_name VARCHAR(50) NOT NULL,
    old_status BOOLEAN,
    new_status BOOLEAN,
    old_message TEXT,
    new_message TEXT,
    changed_by VARCHAR(100) DEFAULT 'admin',
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    change_reason TEXT
);

-- فهرس لسجل التغييرات
CREATE INDEX IF NOT EXISTS idx_service_status_log_service ON service_status_log(service_name);
CREATE INDEX IF NOT EXISTS idx_service_status_log_date ON service_status_log(changed_at);

-- دالة لتسجيل التغييرات
CREATE OR REPLACE FUNCTION log_service_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- تسجيل التغيير فقط إذا تغيرت الحالة أو الرسالة
    IF OLD.is_enabled != NEW.is_enabled OR OLD.disabled_message != NEW.disabled_message THEN
        INSERT INTO service_status_log (
            service_name,
            old_status,
            new_status,
            old_message,
            new_message,
            changed_by
        ) VALUES (
            NEW.service_name,
            OLD.is_enabled,
            NEW.is_enabled,
            OLD.disabled_message,
            NEW.disabled_message,
            NEW.updated_by
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- trigger لتسجيل التغييرات
DROP TRIGGER IF EXISTS trigger_log_service_status_change ON service_status;
CREATE TRIGGER trigger_log_service_status_change
    AFTER UPDATE ON service_status
    FOR EACH ROW
    EXECUTE FUNCTION log_service_status_change();

-- ========================================
-- 7. اختبار النظام
-- ========================================

-- عرض الحالة الحالية
SELECT '📊 حالة الخدمات الحالية:' as "النتيجة";
SELECT * FROM get_all_service_statuses();

-- اختبار تحديث حالة خدمة
SELECT '🧪 اختبار تحديث حالة خدمة التوريد:' as "الاختبار";
SELECT update_service_status('supply_transactions', false, 'خدمة التوريد معطلة للصيانة', 'صيانة مجدولة');

-- عرض الحالة بعد التحديث
SELECT '📊 حالة الخدمات بعد التحديث:' as "النتيجة";
SELECT * FROM get_all_service_statuses();

-- إعادة تفعيل الخدمة
SELECT '🔄 إعادة تفعيل خدمة التوريد:' as "الاختبار";
SELECT update_service_status('supply_transactions', true);

-- عرض سجل التغييرات
SELECT '📋 سجل التغييرات:' as "السجل";
SELECT 
    service_name as "الخدمة",
    old_status as "الحالة السابقة",
    new_status as "الحالة الجديدة",
    changed_by as "تم التغيير بواسطة",
    changed_at as "وقت التغيير"
FROM service_status_log 
ORDER BY changed_at DESC 
LIMIT 5;

-- رسالة النجاح
SELECT '🎉 تم إعداد نظام حالة الخدمات بنجاح!' as "النتيجة النهائية";
