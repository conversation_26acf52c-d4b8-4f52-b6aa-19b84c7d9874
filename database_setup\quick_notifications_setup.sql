-- ========================================
-- 📱 إعداد سريع لنظام الإشعارات المبسط
-- ========================================
-- 🚀 شغل هذا الملف في Supabase SQL Editor

-- حذف الجداول والدوال القديمة إذا كانت موجودة
DROP FUNCTION IF EXISTS cleanup_old_notifications();
DROP FUNCTION IF EXISTS log_notification(UUID, VARCHAR, TEXT, TEXT, JSONB);
DROP FUNCTION IF EXISTS get_notification_stats(UUID);
DROP TABLE IF EXISTS user_notification_settings CASCADE;
DROP TABLE IF EXISTS notification_logs CASCADE;

-- ========================================
-- إن<PERSON>اء جدول سجل الإشعارات
-- ========================================

CREATE TABLE notification_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL,
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    payload JSONB,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس للأداء
CREATE INDEX idx_notification_logs_user_id ON notification_logs(user_id);
CREATE INDEX idx_notification_logs_type ON notification_logs(notification_type);
CREATE INDEX idx_notification_logs_sent_at ON notification_logs(sent_at);

-- ========================================
-- إعداد Row Level Security
-- ========================================

-- تفعيل RLS
ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;

-- سياسة القراءة: المستخدمون يرون سجلاتهم فقط
CREATE POLICY "Users can view their own notification logs" ON notification_logs
    FOR SELECT USING (auth.uid() = user_id);

-- سياسة الإدراج: النظام فقط يمكنه إدراج الإشعارات
CREATE POLICY "System can insert notification logs" ON notification_logs
    FOR INSERT WITH CHECK (true);

-- ========================================
-- دالة تسجيل إشعار جديد
-- ========================================

CREATE OR REPLACE FUNCTION log_notification(
    p_user_id UUID,
    p_notification_type VARCHAR(50),
    p_title TEXT,
    p_body TEXT,
    p_payload JSONB DEFAULT NULL
)
RETURNS BIGINT AS $$
DECLARE
    notification_id BIGINT;
BEGIN
    INSERT INTO notification_logs (user_id, notification_type, title, body, payload)
    VALUES (p_user_id, p_notification_type, p_title, p_body, p_payload)
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- منح الأذونات
-- ========================================

GRANT SELECT ON notification_logs TO authenticated;
GRANT EXECUTE ON FUNCTION log_notification TO authenticated;
GRANT ALL ON notification_logs TO service_role;

-- ========================================
-- رسالة تأكيد
-- ========================================

DO $$
BEGIN
    RAISE NOTICE '✅ تم إعداد نظام الإشعارات المبسط بنجاح!';
    RAISE NOTICE '📱 النظام جاهز لإرسال الإشعارات التلقائية';
    RAISE NOTICE '🎯 جميع الإشعارات مفعلة تلقائياً بدون إعدادات مستخدم';
END $$;
