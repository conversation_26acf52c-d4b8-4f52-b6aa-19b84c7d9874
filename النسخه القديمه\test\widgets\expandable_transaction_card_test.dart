import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:wardlytec_app/models/transaction.dart';
import 'package:wardlytec_app/widgets/expandable_transaction_card.dart';

void main() {
  group('ExpandableTransactionCard Widget Tests', () {
    late Transaction testTransaction;

    setUp(() {
      testTransaction = Transaction(
        id: 'test_tx_123',
        userId: 'test_user_456',
        talabatAccountNumber: '*********',
        senderWalletNumber: '***********',
        amount: 500.0,
        commission: 2.5,
        totalAmount: 502.5,
        walletType: 'Vodafone Cash',
        date: DateTime.parse('2023-01-01T12:00:00.000Z'),
        status: 'جاري',
      );
    });

    Widget createTestWidget(Transaction transaction) {
      return MaterialApp(
        home: Scaffold(
          body: ExpandableTransactionCard(transaction: transaction),
        ),
      );
    }

    testWidgets('Should display transaction basic info', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testTransaction));

      // التحقق من عرض المعلومات الأساسية
      expect(find.text('*********'), findsOneWidget); // رقم حساب طلبات
      expect(find.text('500.0 جنيه'), findsOneWidget); // المبلغ
      expect(find.text('جاري'), findsOneWidget); // الحالة
    });

    testWidgets('Should be collapsed by default', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testTransaction));

      // التحقق من أن البطاقة مطوية افتراضياً
      expect(find.text('***********'), findsNothing); // رقم المحفظة لا يظهر
      expect(find.text('2.5 جنيه'), findsNothing); // العمولة لا تظهر
    });

    testWidgets('Should expand when tapped', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testTransaction));

      // النقر على البطاقة
      await tester.tap(find.byType(ExpandableTransactionCard));
      await tester.pumpAndSettle();

      // التحقق من ظهور التفاصيل
      expect(find.text('***********'), findsOneWidget); // رقم المحفظة
      expect(find.text('2.5 جنيه'), findsOneWidget); // العمولة
      expect(find.text('502.5 جنيه'), findsOneWidget); // المجموع
    });

    testWidgets('Should collapse when tapped again', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testTransaction));

      // النقر لتوسيع البطاقة
      await tester.tap(find.byType(ExpandableTransactionCard));
      await tester.pumpAndSettle();

      // النقر مرة أخرى لطي البطاقة
      await tester.tap(find.byType(ExpandableTransactionCard));
      await tester.pumpAndSettle();

      // التحقق من إخفاء التفاصيل
      expect(find.text('***********'), findsNothing);
      expect(find.text('2.5 جنيه'), findsNothing);
    });

    testWidgets('Should display correct status color for completed transaction', (WidgetTester tester) async {
      final completedTransaction = testTransaction.copyWith(status: 'تم');
      await tester.pumpWidget(createTestWidget(completedTransaction));

      // البحث عن النص مع اللون الأخضر (تم)
      expect(find.text('تم'), findsOneWidget);
    });

    testWidgets('Should display correct status color for rejected transaction', (WidgetTester tester) async {
      final rejectedTransaction = testTransaction.copyWith(status: 'مرفوض');
      await tester.pumpWidget(createTestWidget(rejectedTransaction));

      // البحث عن النص مع اللون الأحمر (مرفوض)
      expect(find.text('مرفوض'), findsOneWidget);
    });

    testWidgets('Should display correct status color for pending transaction', (WidgetTester tester) async {
      final pendingTransaction = testTransaction.copyWith(status: 'جاري');
      await tester.pumpWidget(createTestWidget(pendingTransaction));

      // البحث عن النص مع اللون البرتقالي (جاري)
      expect(find.text('جاري'), findsOneWidget);
    });

    testWidgets('Should format date correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testTransaction));

      // النقر لتوسيع البطاقة وعرض التاريخ
      await tester.tap(find.byType(ExpandableTransactionCard));
      await tester.pumpAndSettle();

      // التحقق من تنسيق التاريخ
      expect(find.textContaining('2023'), findsOneWidget);
    });

    testWidgets('Should handle different wallet types', (WidgetTester tester) async {
      final etisalatTransaction = testTransaction.copyWith(walletType: 'Etisalat Cash');
      await tester.pumpWidget(createTestWidget(etisalatTransaction));

      // النقر لتوسيع البطاقة
      await tester.tap(find.byType(ExpandableTransactionCard));
      await tester.pumpAndSettle();

      expect(find.text('Etisalat Cash'), findsOneWidget);
    });
  });
}
