import 'package:flutter_test/flutter_test.dart';
import 'package:wardlytec_app/models/user.dart';

void main() {
  group('UserModel Tests', () {
    test('UserModel should be created with required fields', () {
      final user = UserModel(
        id: 1,
        phoneNumber: '***********',
        name: 'أحمد محمد',
        createdAt: DateTime.now(),
        favoriteAccounts: [],
      );

      expect(user.id, 1);
      expect(user.phoneNumber, '***********');
      expect(user.name, 'أحمد محمد');
      expect(user.favoriteAccounts, isEmpty);
      expect(user.adViewsRemaining, 20); // القيمة الافتراضية
    });

    test('UserModel.fromMap should parse data correctly', () {
      final testData = {
        'user_id': '123',
        'phone_number': '***********',
        'name': 'سارة أحمد',
        'secret_code': '123456',
        'created_at': '2023-01-01T00:00:00.000Z',
        'last_login': '2023-01-02T00:00:00.000Z',
        'favorite_accounts': ['account1', 'account2'],
        'ad_views_remaining': 15,
      };

      final user = UserModel.fromMap(testData, 123);

      expect(user.id, 123);
      expect(user.phoneNumber, '***********');
      expect(user.name, 'سارة أحمد');
      expect(user.secretCode, '123456');
      expect(user.favoriteAccounts, ['account1', 'account2']);
      expect(user.adViewsRemaining, 15);
    });

    test('UserModel.fromMap should handle missing fields', () {
      final testData = {
        'phone_number': '***********',
      };

      final user = UserModel.fromMap(testData, 456);

      expect(user.id, 456);
      expect(user.phoneNumber, '***********');
      expect(user.name, null);
      expect(user.favoriteAccounts, isEmpty);
      expect(user.adViewsRemaining, 20); // القيمة الافتراضية
    });

    test('UserModel.toMap should convert to correct format', () {
      final user = UserModel(
        id: 1,
        phoneNumber: '***********',
        name: 'محمد علي',
        secretCode: '654321',
        createdAt: DateTime.parse('2023-01-01T00:00:00.000Z'),
        lastLogin: DateTime.parse('2023-01-02T00:00:00.000Z'),
        favoriteAccounts: ['acc1', 'acc2'],
        adViewsRemaining: 10,
      );

      final map = user.toMap();

      expect(map['phone_number'], '***********');
      expect(map['name'], 'محمد علي');
      expect(map['secret_code'], '654321');
      expect(map['favorite_accounts'], ['acc1', 'acc2']);
      expect(map['ad_views_remaining'], 10);
    });

    test('UserModel.copyWith should create modified copy', () {
      final originalUser = UserModel(
        id: 1,
        phoneNumber: '***********',
        name: 'الاسم الأصلي',
        createdAt: DateTime.now(),
        favoriteAccounts: [],
        adViewsRemaining: 20,
      );

      final modifiedUser = originalUser.copyWith(
        name: 'الاسم الجديد',
        adViewsRemaining: 15,
      );

      expect(modifiedUser.id, originalUser.id);
      expect(modifiedUser.phoneNumber, originalUser.phoneNumber);
      expect(modifiedUser.name, 'الاسم الجديد');
      expect(modifiedUser.adViewsRemaining, 15);
    });
  });
}
