import 'package:flutter/material.dart';
import 'package:wardlytec_app/utils/network_helper.dart';

/// زر يمنع الضغط المتعدد ويظهر حالة التحميل
class SingleTapButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;
  final String? loadingText;
  final bool enabled;
  final Widget? icon;
  final bool checkConnection; // فحص الاتصال قبل التنفيذ
  final String? connectionTitle; // عنوان dialog عدم الاتصال
  final String? connectionMessage; // رسالة dialog عدم الاتصال
  final bool resetLoadingOnError; // إعادة تعيين حالة التحميل عند الخطأ

  const SingleTapButton({
    Key? key,
    required this.onPressed,
    required this.child,
    this.style,
    this.loadingText,
    this.enabled = true,
    this.icon,
    this.checkConnection = false,
    this.connectionTitle,
    this.connectionMessage,
    this.resetLoadingOnError = true,
  }) : super(key: key);

  @override
  State<SingleTapButton> createState() => SingleTapButtonState();

  /// دالة static للوصول إلى دالة reactivateButton من الخارج
  static void reactivateButton(GlobalKey<State<SingleTapButton>> key) {
    final state = key.currentState;
    if (state is SingleTapButtonState) {
      state.reactivateButton();
    }
  }
}

class SingleTapButtonState extends State<SingleTapButton> {
  bool _isLoading = false;

  @override
  void didUpdateWidget(SingleTapButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    // إعادة تعيين حالة التحميل إذا تم تعطيل الزر من الخارج
    if (!widget.enabled && _isLoading) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _handleTap() async {
    if (_isLoading || !widget.enabled || widget.onPressed == null) return;

    // فحص الاتصال إذا كان مطلوب
    if (widget.checkConnection) {
      print('🔍 SingleTapButton: فحص الاتصال مطلوب');
      final bool hasConnection = await NetworkHelper.checkConnectionWithDialog(
        context,
        title: widget.connectionTitle,
        message: widget.connectionMessage,
        onRetry: () {
          // إعادة تنفيذ العملية بعد استعادة الاتصال
          // استخدام Future.delayed لتجنب الاستدعاء المتداخل
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) {
              _executeOperation();
            }
          });
        },
      );

      if (!hasConnection) {
        print('❌ SingleTapButton: لا يوجد اتصال - تم إلغاء العملية');
        return;
      }
    }

    // تنفيذ العملية
    await _executeOperation();
  }

  /// تنفيذ العملية الفعلية
  Future<void> _executeOperation() async {
    if (_isLoading || !widget.enabled || widget.onPressed == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تنفيذ العملية مع timeout
      await Future.any([
        Future(() => widget.onPressed!()),
        Future.delayed(const Duration(seconds: 30)), // timeout بعد 30 ثانية
      ]);

      // انتظار قصير للسماح للعملية بالبدء
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      print('❌ خطأ في تنفيذ العملية: $e');
      // في حالة الخطأ، نعيد تعيين الحالة فوراً
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// إعادة تعيين حالة التحميل (للاستخدام من الخارج)
  void resetLoadingState() {
    if (mounted && _isLoading) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// إعادة تفعيل الزر بعد فشل العملية (للاستخدام من الخارج)
  void reactivateButton() {
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDisabled = _isLoading || !widget.enabled || widget.onPressed == null;
    
    if (widget.icon != null) {
      // زر مع أيقونة
      return ElevatedButton.icon(
        onPressed: isDisabled ? null : _handleTap,
        style: widget.style,
        icon: _isLoading 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : widget.icon!,
        label: _isLoading 
          ? Text(widget.loadingText ?? 'جاري...')
          : widget.child,
      );
    } else {
      // زر عادي
      return ElevatedButton(
        onPressed: isDisabled ? null : _handleTap,
        style: widget.style,
        child: _isLoading 
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 8),
                Text(widget.loadingText ?? 'جاري...'),
              ],
            )
          : widget.child,
      );
    }
  }
}

/// زر نصي يمنع الضغط المتعدد
class SingleTapTextButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;
  final String? loadingText;
  final bool enabled;

  const SingleTapTextButton({
    Key? key,
    required this.onPressed,
    required this.child,
    this.style,
    this.loadingText,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<SingleTapTextButton> createState() => _SingleTapTextButtonState();
}

class _SingleTapTextButtonState extends State<SingleTapTextButton> {
  bool _isLoading = false;

  Future<void> _handleTap() async {
    if (_isLoading || !widget.enabled || widget.onPressed == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      widget.onPressed!();
      
      // انتظار قصير للسماح للعملية بالبدء
      await Future.delayed(const Duration(milliseconds: 500));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDisabled = _isLoading || !widget.enabled || widget.onPressed == null;
    
    return TextButton(
      onPressed: isDisabled ? null : _handleTap,
      style: widget.style,
      child: _isLoading 
        ? Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
              const SizedBox(width: 8),
              Text(widget.loadingText ?? 'جاري...'),
            ],
          )
        : widget.child,
    );
  }
}

/// زر أيقونة يمنع الضغط المتعدد
class SingleTapIconButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final Widget icon;
  final ButtonStyle? style;
  final String? tooltip;
  final bool enabled;

  const SingleTapIconButton({
    Key? key,
    required this.onPressed,
    required this.icon,
    this.style,
    this.tooltip,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<SingleTapIconButton> createState() => _SingleTapIconButtonState();
}

class _SingleTapIconButtonState extends State<SingleTapIconButton> {
  bool _isLoading = false;

  Future<void> _handleTap() async {
    if (_isLoading || !widget.enabled || widget.onPressed == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      widget.onPressed!();
      
      // انتظار قصير للسماح للعملية بالبدء
      await Future.delayed(const Duration(milliseconds: 300));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDisabled = _isLoading || !widget.enabled || widget.onPressed == null;
    
    return IconButton(
      onPressed: isDisabled ? null : _handleTap,
      style: widget.style,
      tooltip: widget.tooltip,
      icon: _isLoading 
        ? const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
            ),
          )
        : widget.icon,
    );
  }
}
