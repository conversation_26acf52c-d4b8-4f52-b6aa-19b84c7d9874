import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:wardlytec_app/providers/supabase_auth_provider.dart';
import 'package:wardlytec_app/screens/auth/login_screen.dart';

void main() {
  group('LoginScreen Widget Tests', () {
    late SupabaseAuthProvider mockAuthProvider;

    setUp(() {
      mockAuthProvider = SupabaseAuthProvider();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<SupabaseAuthProvider>(
          create: (_) => mockAuthProvider,
          child: const LoginScreen(),
        ),
      );
    }

    testWidgets('Should display both buttons', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // التحقق من وجود الزرين
      expect(find.text('إنشاء حساب جديد'), findsOneWidget);
      expect(find.text('تسجيل الدخول'), findsOneWidget);
    });

    testWidgets('Create account button should be disabled when fields are empty', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // العثور على زر إنشاء الحساب
      final createAccountButton = find.ancestor(
        of: find.text('إنشاء حساب جديد'),
        matching: find.byType(ElevatedButton),
      );

      expect(createAccountButton, findsOneWidget);
      
      // التحقق من أن الزر معطل (onPressed = null)
      final ElevatedButton button = tester.widget(createAccountButton);
      expect(button.onPressed, isNull);
    });

    testWidgets('Login button should always be enabled', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // العثور على زر تسجيل الدخول
      final loginButton = find.ancestor(
        of: find.text('تسجيل الدخول'),
        matching: find.byType(ElevatedButton),
      );

      expect(loginButton, findsOneWidget);
      
      // التحقق من أن الزر مفعل دائماً
      final ElevatedButton button = tester.widget(loginButton);
      expect(button.onPressed, isNotNull);
    });

    testWidgets('Create account button should be enabled when fields are valid', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // ملء حقل الاسم
      final nameField = find.byKey(const Key('name_field'));
      await tester.enterText(nameField, 'أحمد محمد علي');
      await tester.pump();

      // ملء حقل الهاتف
      final phoneField = find.byKey(const Key('phone_field'));
      await tester.enterText(phoneField, '***********');
      await tester.pump();

      // العثور على زر إنشاء الحساب
      final createAccountButton = find.ancestor(
        of: find.text('إنشاء حساب جديد'),
        matching: find.byType(ElevatedButton),
      );

      // التحقق من أن الزر مفعل الآن
      final ElevatedButton button = tester.widget(createAccountButton);
      expect(button.onPressed, isNotNull);
    });

    testWidgets('Both buttons should have same design when enabled', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // ملء الحقول لتفعيل زر إنشاء الحساب
      final nameField = find.byKey(const Key('name_field'));
      await tester.enterText(nameField, 'أحمد محمد علي');
      await tester.pump();

      final phoneField = find.byKey(const Key('phone_field'));
      await tester.enterText(phoneField, '***********');
      await tester.pump();

      // التحقق من أن كلا الزرين من نوع ElevatedButton
      expect(find.byType(ElevatedButton), findsNWidgets(2));
      
      // التحقق من وجود الأيقونات
      expect(find.byIcon(Icons.person_add), findsOneWidget); // زر إنشاء الحساب
      expect(find.byIcon(Icons.login), findsOneWidget); // زر تسجيل الدخول
    });

    testWidgets('Login button should navigate when pressed', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // النقر على زر تسجيل الدخول
      await tester.tap(find.text('تسجيل الدخول'));
      await tester.pumpAndSettle();

      // التحقق من الانتقال (يمكن تحسين هذا الاختبار حسب التطبيق)
      // هنا نتوقع أن يتم الانتقال لصفحة أخرى
    });

    testWidgets('Create account button should remain disabled with invalid name', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // ملء حقل الاسم بقيمة غير صحيحة (قصيرة)
      final nameField = find.byKey(const Key('name_field'));
      await tester.enterText(nameField, 'أح'); // أقل من 3 أحرف
      await tester.pump();

      // ملء حقل الهاتف بقيمة صحيحة
      final phoneField = find.byKey(const Key('phone_field'));
      await tester.enterText(phoneField, '***********');
      await tester.pump();

      // العثور على زر إنشاء الحساب
      final createAccountButton = find.ancestor(
        of: find.text('إنشاء حساب جديد'),
        matching: find.byType(ElevatedButton),
      );

      // التحقق من أن الزر ما زال معطل
      final ElevatedButton button = tester.widget(createAccountButton);
      expect(button.onPressed, isNull);
    });

    testWidgets('Create account button should remain disabled with invalid phone', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // ملء حقل الاسم بقيمة صحيحة
      final nameField = find.byKey(const Key('name_field'));
      await tester.enterText(nameField, 'أحمد محمد علي');
      await tester.pump();

      // ملء حقل الهاتف بقيمة غير صحيحة
      final phoneField = find.byKey(const Key('phone_field'));
      await tester.enterText(phoneField, '123'); // رقم قصير
      await tester.pump();

      // العثور على زر إنشاء الحساب
      final createAccountButton = find.ancestor(
        of: find.text('إنشاء حساب جديد'),
        matching: find.byType(ElevatedButton),
      );

      // التحقق من أن الزر ما زال معطل
      final ElevatedButton button = tester.widget(createAccountButton);
      expect(button.onPressed, isNull);
    });

    testWidgets('Login button should remain enabled regardless of field states', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // اختبار 1: حقول فارغة
      final loginButton = find.ancestor(
        of: find.text('تسجيل الدخول'),
        matching: find.byType(ElevatedButton),
      );
      
      ElevatedButton button = tester.widget(loginButton);
      expect(button.onPressed, isNotNull);

      // اختبار 2: ملء الحقول
      final nameField = find.byKey(const Key('name_field'));
      await tester.enterText(nameField, 'أحمد محمد علي');
      await tester.pump();

      button = tester.widget(loginButton);
      expect(button.onPressed, isNotNull);

      // اختبار 3: مسح الحقول
      await tester.enterText(nameField, '');
      await tester.pump();

      button = tester.widget(loginButton);
      expect(button.onPressed, isNotNull);
    });
  });
}
