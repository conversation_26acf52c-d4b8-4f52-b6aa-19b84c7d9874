-- ========================================
-- 📱 نظام الإشعارات المبسط - بدون إعدادات مستخدم
-- ========================================
-- 🚀 شغل هذا الملف في Supabase SQL Editor

-- ========================================
-- 1. جدول سجل الإشعارات المرسلة فقط
-- ========================================

CREATE TABLE IF NOT EXISTS notification_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL,
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    payload JSONB,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_logs_user_id ON notification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_logs_type ON notification_logs(notification_type);
CREATE INDEX IF NOT EXISTS idx_notification_logs_sent_at ON notification_logs(sent_at);

-- ========================================
-- 2. إعداد Row Level Security
-- ========================================

-- تفعيل RLS
ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;

-- سياسة القراءة: المستخدمون يرون سجلاتهم فقط
CREATE POLICY "Users can view their own notification logs" ON notification_logs
    FOR SELECT USING (auth.uid() = user_id);

-- سياسة الإدراج: النظام فقط يمكنه إدراج الإشعارات
CREATE POLICY "System can insert notification logs" ON notification_logs
    FOR INSERT WITH CHECK (true);

-- ========================================
-- 3. دالة تنظيف الإشعارات القديمة
-- ========================================

-- حذف الدالة إذا كانت موجودة
DROP FUNCTION IF EXISTS cleanup_old_notifications();

CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
    -- حذف الإشعارات أكثر من 90 يوم
    DELETE FROM notification_logs 
    WHERE sent_at < NOW() - INTERVAL '90 days';
    
    RAISE NOTICE 'تم تنظيف الإشعارات القديمة';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 4. دالة تسجيل إشعار جديد
-- ========================================

-- حذف الدالة إذا كانت موجودة
DROP FUNCTION IF EXISTS log_notification(UUID, VARCHAR, TEXT, TEXT, JSONB);

CREATE OR REPLACE FUNCTION log_notification(
    p_user_id UUID,
    p_notification_type VARCHAR(50),
    p_title TEXT,
    p_body TEXT,
    p_payload JSONB DEFAULT NULL
)
RETURNS BIGINT AS $$
DECLARE
    notification_id BIGINT;
BEGIN
    -- إدراج الإشعار في السجل
    INSERT INTO notification_logs (user_id, notification_type, title, body, payload)
    VALUES (p_user_id, p_notification_type, p_title, p_body, p_payload)
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 5. دالة إحصائيات الإشعارات
-- ========================================

-- حذف الدالة إذا كانت موجودة
DROP FUNCTION IF EXISTS get_notification_stats(UUID);

CREATE OR REPLACE FUNCTION get_notification_stats(p_user_id UUID DEFAULT NULL)
RETURNS TABLE(
    notification_type VARCHAR(50),
    total_count BIGINT,
    today_count BIGINT,
    this_week_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        nl.notification_type,
        COUNT(*) as total_count,
        COUNT(*) FILTER (WHERE DATE(nl.sent_at) = CURRENT_DATE) as today_count,
        COUNT(*) FILTER (WHERE nl.sent_at >= DATE_TRUNC('week', NOW())) as this_week_count
    FROM notification_logs nl
    WHERE (p_user_id IS NULL OR nl.user_id = p_user_id)
    GROUP BY nl.notification_type
    ORDER BY total_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 6. إعداد تنظيف تلقائي (اختياري)
-- ========================================

-- يمكن إعداد cron job لتشغيل هذا يومياً
-- SELECT cron.schedule('cleanup-old-notifications', '0 2 * * *', 'SELECT cleanup_old_notifications();');

-- ========================================
-- 7. منح الأذونات
-- ========================================

-- منح أذونات للمستخدمين المصادق عليهم
GRANT SELECT ON notification_logs TO authenticated;
GRANT EXECUTE ON FUNCTION log_notification TO authenticated;
GRANT EXECUTE ON FUNCTION get_notification_stats TO authenticated;

-- منح أذونات للخدمة (service_role)
GRANT ALL ON notification_logs TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_old_notifications TO service_role;

-- ========================================
-- انتهى الإعداد
-- ========================================

-- رسالة تأكيد
DO $$
BEGIN
    RAISE NOTICE '✅ تم إعداد نظام الإشعارات المبسط بنجاح!';
    RAISE NOTICE '📱 الميزات المتاحة:';
    RAISE NOTICE '   • إرسال إشعارات تلقائية عند تغيير حالة المعاملات';
    RAISE NOTICE '   • إرسال إشعارات عند تعطيل/تفعيل الخدمات';
    RAISE NOTICE '   • تسجيل جميع الإشعارات المرسلة';
    RAISE NOTICE '   • تنظيف تلقائي للإشعارات القديمة';
    RAISE NOTICE '   • إحصائيات مفصلة للإشعارات';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 النظام مبسط بدون إعدادات مستخدم - جميع الإشعارات مفعلة تلقائياً';
END $$;
