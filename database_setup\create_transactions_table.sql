-- إنشاء جدول المعاملات لتطبيق وردلي تك
-- Create transactions table for Wardly Tech app

CREATE TABLE IF NOT EXISTS transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    commission DECIMAL(10,2) DEFAULT 0,
    wallet_type VARCHAR(50) NOT NULL,
    talabat_account_number VARCHAR(50) NOT NULL,
    sender_wallet_number VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    transaction_type VARCHAR(20) DEFAULT 'supply', -- 'supply' للتوريد، 'recharge' للشحن
    proof_image_url TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

-- إضافة قيود للتحقق من صحة البيانات
DO $$
BEGIN
    -- التحقق من المبلغ الموجب
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'check_amount_positive'
                   AND table_name = 'transactions') THEN
        ALTER TABLE transactions ADD CONSTRAINT check_amount_positive CHECK (amount > 0);
    END IF;

    -- التحقق من العمولة غير السالبة
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'check_commission_non_negative'
                   AND table_name = 'transactions') THEN
        ALTER TABLE transactions ADD CONSTRAINT check_commission_non_negative CHECK (commission >= 0);
    END IF;

    -- التحقق من صحة الحالة
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'check_status_valid'
                   AND table_name = 'transactions') THEN
        ALTER TABLE transactions ADD CONSTRAINT check_status_valid CHECK (
            status IN ('pending', 'processing', 'completed', 'cancelled', 'failed')
        );
    END IF;

    -- التحقق من نوع المعاملة
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints
                   WHERE constraint_name = 'check_transaction_type_valid'
                   AND table_name = 'transactions') THEN
        ALTER TABLE transactions ADD CONSTRAINT check_transaction_type_valid CHECK (
            transaction_type IN ('supply', 'recharge')
        );
    END IF;
END $$;

-- إضافة تعليقات للأعمدة
COMMENT ON TABLE transactions IS 'جدول المعاملات المالية';
COMMENT ON COLUMN transactions.id IS 'معرف المعاملة الفريد';
COMMENT ON COLUMN transactions.user_id IS 'معرف المستخدم';
COMMENT ON COLUMN transactions.amount IS 'مبلغ المعاملة';
COMMENT ON COLUMN transactions.commission IS 'العمولة المخصومة';
COMMENT ON COLUMN transactions.wallet_type IS 'نوع المحفظة (فودافون، اتصالات، إلخ)';
COMMENT ON COLUMN transactions.talabat_account_number IS 'رقم حساب طلبات';
COMMENT ON COLUMN transactions.sender_wallet_number IS 'رقم المحفظة المرسلة';
COMMENT ON COLUMN transactions.status IS 'حالة المعاملة';
COMMENT ON COLUMN transactions.transaction_type IS 'نوع المعاملة (توريد أو شحن)';
COMMENT ON COLUMN transactions.proof_image_url IS 'رابط صورة إثبات التحويل';
COMMENT ON COLUMN transactions.notes IS 'ملاحظات إضافية';

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        NEW.completed_at = NOW();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث updated_at عند تحديث البيانات
CREATE TRIGGER trigger_update_updated_at
    BEFORE UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

-- إدراج بيانات تجريبية (اختيارية)
INSERT INTO transactions (
    user_id, amount, commission, wallet_type, 
    talabat_account_number, sender_wallet_number, 
    transaction_type, status
) VALUES
(1, 1000.00, 5.00, 'Vodafone Cash', '*********', '0*********0', 'supply', 'completed'),
(2, 500.00, 2.50, 'Etisalat Cash', '*********', '01*********', 'recharge', 'pending'),
(3, 750.00, 0.00, 'Orange Cash', '*********', '***********', 'supply', 'processing')
ON CONFLICT DO NOTHING;

-- عرض النتيجة
SELECT 
    t.id as "رقم المعاملة",
    u.name as "اسم المستخدم",
    t.amount as "المبلغ",
    t.commission as "العمولة",
    t.wallet_type as "نوع المحفظة",
    t.transaction_type as "نوع المعاملة",
    t.status as "الحالة",
    t.created_at as "تاريخ الإنشاء"
FROM transactions t
JOIN users u ON t.user_id = u.id
ORDER BY t.created_at DESC;
