# 📱 دليل إعداد نظام الإشعارات المبسط

## 🎯 نظرة عامة

هذا المجلد يحتوي على ملفات SQL لإعداد نظام الإشعارات المبسط في Supabase.

---

## 📁 الملفات المتاحة

### **1. الملف الشامل (الموصى به) 🌟**
**`rebuild_notifications_system.sql`**
- 🗑️ **حذف شامل** لجميع الجداول والدوال والسياسات القديمة
- 🔨 **إعادة بناء كاملة** للنظام من الصفر
- ✅ **إعداد تلقائي** لجميع الأذونات والفهارس
- 🧪 **إشعار تجريبي** للتأكد من عمل النظام
- 📊 **تقرير مفصل** عن حالة النظام

### **2. الم<PERSON><PERSON> السريع**
**`quick_notifications_setup.sql`**
- ⚡ إعداد سريع ومبسط
- 🧹 حذف الجداول القديمة
- 📋 إنشاء الجداول الأساسية فقط

### **3. الملف الشامل مع الدوال**
**`create_simple_notifications.sql`**
- 📚 إعداد شامل مع جميع الدوال المساعدة
- 🔧 دوال التنظيف والإحصائيات
- ⚙️ إعدادات متقدمة

### **4. ملف التنظيف فقط**
**`cleanup_old_notifications.sql`**
- 🧹 تنظيف شامل للنظام القديم فقط
- 🗑️ حذف جميع البقايا
- 💡 يُستخدم قبل الإعداد الجديد

---

## 🚀 طريقة الاستخدام

### **الطريقة الموصى بها (للجميع):**

#### **الخطوة 1: تشغيل الملف الشامل**
```sql
-- في Supabase SQL Editor
-- انسخ والصق محتوى: rebuild_notifications_system.sql
-- اضغط "Run"
```

#### **الخطوة 2: التحقق من النجاح**
ابحث عن هذه الرسائل في النتائج:
```
✅ تم إعادة بناء نظام الإشعارات بنجاح!
📊 إحصائيات النظام الجديد:
   📋 الجداول: 1 جدول
   ⚙️ الدوال: 3 دالة
   🛡️ السياسات: 2 سياسة
🚀 النظام جاهز للاستخدام!
```

---

## 🔧 للحالات الخاصة

### **إذا كان لديك نظام قديم معقد:**
```sql
-- الخطوة 1: تنظيف شامل
-- شغل: cleanup_old_notifications.sql

-- الخطوة 2: إعداد جديد
-- شغل: quick_notifications_setup.sql
```

### **للإعداد السريع فقط:**
```sql
-- شغل: quick_notifications_setup.sql
```

### **للإعداد الشامل مع الدوال:**
```sql
-- شغل: create_simple_notifications.sql
```

---

## 📋 ما يتم إنشاؤه

### **الجداول:**
- `notification_logs` - سجل جميع الإشعارات المرسلة

### **الدوال:**
- `log_notification()` - تسجيل إشعار جديد
- `cleanup_old_notifications()` - تنظيف الإشعارات القديمة
- `get_notification_stats()` - إحصائيات الإشعارات

### **السياسات:**
- المستخدمون يرون إشعاراتهم فقط
- النظام يمكنه إدراج الإشعارات

### **الفهارس:**
- فهرس على `user_id` للأداء
- فهرس على `notification_type` للتصنيف
- فهرس على `sent_at` للترتيب الزمني

---

## 🎯 أنواع الإشعارات المدعومة

### **إشعارات المعاملات:**
- `transaction_update` - تحديث حالة المعاملة
- `transaction_approved` - قبول المعاملة
- `transaction_completed` - إنجاز المعاملة
- `transaction_rejected` - رفض المعاملة

### **إشعارات الخدمات:**
- `service_disabled` - تعطيل خدمة
- `service_enabled` - تفعيل خدمة
- `maintenance_notification` - إشعار صيانة

### **إشعارات النظام:**
- `system_test` - إشعار تجريبي
- `system_update` - تحديث النظام

---

## 🛠️ استكشاف الأخطاء

### **خطأ: "table already exists"**
```sql
-- الحل: شغل ملف التنظيف أولاً
-- cleanup_old_notifications.sql
```

### **خطأ: "function already exists"**
```sql
-- الحل: شغل الملف الشامل
-- rebuild_notifications_system.sql
-- (يحتوي على حذف تلقائي للدوال القديمة)
```

### **خطأ: "permission denied"**
```sql
-- تأكد من أنك تستخدم service_role key في Supabase
-- أو تأكد من أن المستخدم له صلاحيات كافية
```

---

## 📞 الدعم

للمساعدة:
1. تحقق من رسائل الخطأ في Supabase SQL Editor
2. راجع logs التطبيق
3. تأكد من صحة إعدادات Supabase
4. تواصل مع فريق التطوير

---

**🎉 نظام إشعارات مبسط وقوي جاهز للاستخدام! 🚀**
