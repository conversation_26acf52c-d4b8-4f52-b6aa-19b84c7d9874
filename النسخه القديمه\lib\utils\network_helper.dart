import 'package:flutter/material.dart';
import 'package:wardlytec_app/services/connectivity_service.dart';
import 'package:wardlytec_app/widgets/no_internet_dialog.dart';
import 'package:wardlytec_app/utils/app_theme.dart';

/// مساعد للعمليات التي تتطلب اتصال بالإنترنت مع دعم الإنترنت الضعيف
class NetworkHelper {
  static final ConnectivityService _connectivityService = ConnectivityService();

  /// التحقق من الاتصال قبل تنفيذ عملية
  static Future<bool> checkConnectionBeforeAction(BuildContext context) async {
    final bool isConnected = await _connectivityService.checkConnection();

    // لا نظهر أي إشعارات - ConnectivityWrapper سيتولى الأمر
    return isConnected;
  }

  /// التحقق من إمكانية تنفيذ عملية حسب جودة الاتصال
  static Future<bool> canPerformAction(
    BuildContext context, {
    required ActionType actionType,
    bool showDialog = true,
  }) async {
    final canPerform = await _connectivityService.canPerformAction(
      actionType: actionType,
      forceCheck: true,
    );

    if (!canPerform && showDialog) {
      final connectionInfo = await _connectivityService.getDetailedConnectionInfo();
      await _showQualityDialog(context, connectionInfo, actionType);
    }

    return canPerform;
  }

  /// التحقق من الاتصال مع إظهار dialog إذا لم يكن متوفر
  static Future<bool> checkConnectionWithDialog(
    BuildContext context, {
    String? title,
    String? message,
    VoidCallback? onRetry,
    bool forceCheck = false,
  }) async {
    print('🔍 NetworkHelper: فحص الاتصال قبل العملية... (forceCheck: $forceCheck)');
    final bool isConnected = await _connectivityService.checkConnection(forceCheck: forceCheck);

    if (isConnected) {
      print('✅ NetworkHelper: الاتصال متوفر');
      return true;
    }

    print('❌ NetworkHelper: لا يوجد اتصال - سيتم عرض dialog');

    // إظهار dialog عدم الاتصال
    if (!context.mounted) return false;

    final bool? result = await showNoInternetDialog(
      context,
      title: title,
      message: message,
      onRetry: onRetry,
    );

    // إذا ضغط المستخدم على "إعادة المحاولة" ونجح الاتصال
    return result == true;
  }



  /// تنفيذ عملية مع التحقق من الاتصال
  static Future<T?> executeWithConnectivityCheck<T>(
    BuildContext context,
    Future<T> Function() operation, {
    String? loadingMessage,
    String? errorMessage,
  }) async {
    // التحقق من الاتصال أولاً
    if (!await checkConnectionBeforeAction(context)) {
      return null;
    }

    try {
      // إظهار مؤشر التحميل إذا تم تحديد رسالة
      if (loadingMessage != null && context.mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Row(
              children: [
                const CircularProgressIndicator(),
                const SizedBox(width: 16),
                Expanded(child: Text(loadingMessage)),
              ],
            ),
          ),
        );
      }

      // تنفيذ العملية
      final result = await operation();

      // إغلاق مؤشر التحميل
      if (loadingMessage != null && context.mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      return result;
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (loadingMessage != null && context.mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      // إظهار رسالة الخطأ
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    errorMessage ?? 'حدث خطأ أثناء تنفيذ العملية',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }

      return null;
    }
  }

  /// الحصول على حالة الاتصال الحالية
  static bool get isConnected => _connectivityService.isConnected;

  /// الحصول على نوع الاتصال
  static Future<String> getConnectionType() async {
    return await _connectivityService.getConnectionType();
  }

  /// إظهار إشعار حالة الاتصال
  static void showConnectionStatus(BuildContext context) async {
    final connectionType = await getConnectionType();
    final isConnected = _connectivityService.isConnected;

    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isConnected ? Icons.wifi : Icons.wifi_off,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                isConnected
                  ? 'متصل عبر $connectionType'
                  : 'غير متصل بالإنترنت',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: isConnected ? Colors.green[600] : Colors.red[600],
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  /// فحص سريع للاتصال (مع cache)
  static Future<bool> quickConnectionCheck() async {
    return await _connectivityService.checkConnection(forceCheck: false);
  }

  /// فحص قوي للاتصال (بدون cache)
  static Future<bool> forceConnectionCheck() async {
    return await _connectivityService.checkConnection(forceCheck: true);
  }

  /// إظهار dialog جودة الاتصال
  static Future<void> _showQualityDialog(
    BuildContext context,
    ConnectionInfo connectionInfo,
    ActionType actionType,
  ) async {
    if (!context.mounted) return;

    String actionText = _getActionText(actionType);
    String qualityMessage = _connectivityService.getQualityMessage(connectionInfo.quality);

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.signal_wifi_bad,
              color: Color(_connectivityService.getQualityColor(connectionInfo.quality)),
              size: 28,
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'جودة الاتصال غير كافية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'العملية المطلوبة: $actionText',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'جودة الاتصال الحالية: ${connectionInfo.qualityText}',
              style: TextStyle(
                fontSize: 14,
                color: Color(_connectivityService.getQualityColor(connectionInfo.quality)),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'سرعة الاستجابة: ${connectionInfo.responseTime}ms',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            Text(
              'نوع الاتصال: ${connectionInfo.connectionType}',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            Text(
              qualityMessage,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            const Text(
              'يُنصح بالانتظار حتى تحسن جودة الاتصال أو الاتصال بشبكة WiFi أقوى.',
              style: TextStyle(
                fontSize: 13,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('المتابعة رغم ذلك'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  /// الحصول على نص العملية
  static String _getActionText(ActionType actionType) {
    switch (actionType) {
      case ActionType.lightBrowsing:
        return 'تصفح خفيف';
      case ActionType.normalBrowsing:
        return 'تصفح عادي';
      case ActionType.dataUpload:
        return 'رفع بيانات';
      case ActionType.imageUpload:
        return 'رفع صور';
      case ActionType.videoStreaming:
        return 'بث فيديو';
    }
  }

  /// الحصول على معلومات مفصلة عن الاتصال
  static Future<ConnectionInfo> getDetailedConnectionInfo() async {
    return await _connectivityService.getDetailedConnectionInfo();
  }

  /// إظهار إشعار جودة الاتصال
  static void showQualityStatus(BuildContext context) async {
    final connectionInfo = await _connectivityService.getDetailedConnectionInfo();

    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getQualityIcon(connectionInfo.quality),
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '${connectionInfo.qualityText} - ${connectionInfo.speedText} (${connectionInfo.responseTime}ms)',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Color(_connectivityService.getQualityColor(connectionInfo.quality)),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  /// الحصول على أيقونة جودة الاتصال
  static IconData _getQualityIcon(ConnectionQuality quality) {
    switch (quality) {
      case ConnectionQuality.none:
        return Icons.wifi_off;
      case ConnectionQuality.poor:
        return Icons.wifi_1_bar;
      case ConnectionQuality.slow:
        return Icons.wifi_2_bar;
      case ConnectionQuality.moderate:
        return Icons.network_wifi;
      case ConnectionQuality.good:
        return Icons.wifi;
    }
  }
}
