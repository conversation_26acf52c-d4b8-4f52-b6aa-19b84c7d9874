import 'package:flutter/material.dart';
import 'package:wardlytec_app/utils/app_theme.dart';
import 'package:wardlytec_app/widgets/single_tap_button.dart';
import 'package:wardlytec_app/screens/new_recharge_screen.dart';

class NetworkSelectionScreen extends StatefulWidget {
  const NetworkSelectionScreen({Key? key}) : super(key: key);

  @override
  State<NetworkSelectionScreen> createState() => _NetworkSelectionScreenState();
}

class _NetworkSelectionScreenState extends State<NetworkSelectionScreen> {
  String? _selectedNetwork;

  // قائمة الشبكات المتاحة
  final List<Map<String, dynamic>> _networks = [
    {
      'name': 'فودافون',
      'color': Colors.red,
      'icon': Icons.phone_android,
      'description': 'شحن رصيد فودافون',
      'isAvailable': true,
    },
    {
      'name': 'إتصالات',
      'color': Colors.black, // سيتم تكييفه في الكود
      'icon': Icons.phone_android,
      'description': 'شحن رصيد إتصالات',
      'isAvailable': true,
    },
    {
      'name': 'أورانج',
      'color': Colors.deepOrange,
      'icon': Icons.phone_android,
      'description': 'شحن رصيد أورانج',
      'isAvailable': true,
    },
    {
      'name': 'WE',
      'color': Colors.purple,
      'icon': Icons.phone_android,
      'description': 'شحن رصيد WE',
      'isAvailable': false,
      'comingSoonMessage': 'ستكون متاحة قريباً',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('شحن رصيد الهاتف'),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الصفحة (مثل صفحة اختيار الشركة)
            const Text(
              'اختر شبكة الهاتف',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اختر الشبكة التي تريد شحن رصيدها',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
            const SizedBox(height: 32),

            // قائمة الشبكات
            Expanded(
              child: ListView.builder(
                itemCount: _networks.length,
                itemBuilder: (context, index) {
                  final network = _networks[index];
                  final isSelected = _selectedNetwork == network['name'];
                  final isAvailable = network['isAvailable'] ?? true;

                  // تكييف لون إتصالات حسب الوضع
                  Color networkColor = network['color'];
                  if (network['name'] == 'إتصالات') {
                    networkColor = Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey[300]! // رمادي فاتح في الوضع الغامق
                        : Colors.black; // أسود في الوضع الفاتح
                  }

                  // تخفيف اللون للشبكات غير المتاحة
                  if (!isAvailable) {
                    networkColor = networkColor.withValues(alpha: 0.5);
                  }

                  return Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                      child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: isAvailable
                              ? AppTheme.primaryColor.withOpacity(0.1)
                              : Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: Center(
                          child: Icon(
                            network['icon'],
                            size: 24,
                            color: isAvailable ? networkColor : Colors.grey,
                          ),
                        ),
                      ),
                      title: Text(
                        network['name'],
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: null,
                      trailing: Icon(
                        isAvailable
                            ? Icons.arrow_forward_ios
                            : Icons.schedule,
                        color: isAvailable
                            ? AppTheme.primaryColor
                            : Colors.orange,
                      ),
                      onTap: isAvailable
                          ? () => _selectNetwork(network['name'])
                          : () => _showComingSoonDialog(network['name'], network['comingSoonMessage']),
                    ),
                  );
                },
              ),
            ),

            // زر المتابعة
            if (_selectedNetwork != null) ...[
              const SizedBox(height: 16),
              SingleTapButton(
                onPressed: () => _proceedWithNetwork(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  minimumSize: const Size(double.infinity, 50),
                ),
                icon: const Icon(Icons.arrow_forward, size: 20),
                loadingText: 'جاري المتابعة...',
                child: Text(
                  'متابعة مع $_selectedNetwork',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // اختيار الشبكة
  void _selectNetwork(String networkName) {
    setState(() {
      _selectedNetwork = networkName;
    });
  }

  // عرض رسالة أن الشبكة ستكون متاحة قريباً (مثل مرسول)
  void _showComingSoonDialog(String networkName, String? message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              const Text('📱', style: TextStyle(fontSize: 24)),
              const SizedBox(width: 8),
              Text(
                networkName,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.construction,
                size: 48,
                color: Colors.orange,
              ),
              const SizedBox(height: 16),
              Text(
                message ?? 'الخدمة ستكون متاحة قريباً',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                'نعمل على إضافة هذه الشبكة قريباً. ترقبوا التحديثات!',
                style: TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            // زر حسناً في المنتصف مع تصميم محسن (مثل مرسول)
            Center(
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 2,
                ),
                child: const Text(
                  'حسناً',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // المتابعة مع الشبكة المختارة
  void _proceedWithNetwork() {
    if (_selectedNetwork == null) return;

    // الانتقال لصفحة الشحن
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => NewRechargeScreen(selectedNetwork: _selectedNetwork!),
      ),
    );
  }
}
