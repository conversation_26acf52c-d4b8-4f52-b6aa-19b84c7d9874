import 'dart:async';
import 'dart:io' show InternetAddress;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

/// تعداد جودة الاتصال
enum ConnectionQuality {
  none,     // لا يوجد اتصال
  poor,     // اتصال ضعيف جداً (> 2000ms)
  slow,     // اتصال بطيء (1000-2000ms)
  moderate, // اتصال متوسط (500-1000ms)
  good,     // اتصال جيد (< 500ms)
}

/// تعداد أنواع العمليات حسب متطلبات الاتصال
enum ActionType {
  lightBrowsing,   // تصفح خفيف - يعمل مع أي اتصال
  normalBrowsing,  // تصفح عادي - يحتاج اتصال متوسط أو أفضل
  dataUpload,      // رفع بيانات - يحتاج اتصال متوسط أو أفضل
  imageUpload,     // رفع صور - يحتاج اتصال جيد
  videoStreaming,  // بث فيديو - يحتاج اتصال جيد
}

/// معلومات تفصيلية عن جودة الاتصال
class ConnectionInfo {
  final ConnectionQuality quality;
  final int responseTime; // بالميلي ثانية
  final String connectionType;
  final bool isStable;
  final DateTime timestamp;

  ConnectionInfo({
    required this.quality,
    required this.responseTime,
    required this.connectionType,
    required this.isStable,
    required this.timestamp,
  });

  String get qualityText {
    switch (quality) {
      case ConnectionQuality.none:
        return 'لا يوجد اتصال';
      case ConnectionQuality.poor:
        return 'اتصال ضعيف جداً';
      case ConnectionQuality.slow:
        return 'اتصال بطيء';
      case ConnectionQuality.moderate:
        return 'اتصال متوسط';
      case ConnectionQuality.good:
        return 'اتصال جيد';
    }
  }

  String get speedText {
    if (responseTime < 500) return 'سريع';
    if (responseTime < 1000) return 'متوسط';
    if (responseTime < 2000) return 'بطيء';
    return 'بطيء جداً';
  }
}

/// خدمة التحقق من الاتصال بالإنترنت مع دعم الإنترنت الضعيف
class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Stream للاستماع لتغييرات الاتصال
  final StreamController<bool> _connectionStatusController = StreamController<bool>.broadcast();
  final StreamController<ConnectionQuality> _connectionQualityController = StreamController<ConnectionQuality>.broadcast();

  Stream<bool> get connectionStatus => _connectionStatusController.stream;
  Stream<ConnectionQuality> get connectionQuality => _connectionQualityController.stream;

  bool _isConnected = true;
  bool _isInitialized = false;
  DateTime? _lastCheckTime;
  ConnectionQuality _currentQuality = ConnectionQuality.good;
  static const Duration _cacheTimeout = Duration(seconds: 10); // cache لمدة 10 ثوان

  bool get isConnected => _isConnected;
  ConnectionQuality get currentQuality => _currentQuality;

  /// بدء مراقبة الاتصال
  void initialize() {
    if (_isInitialized) {
      if (kDebugMode) {
        print('⚠️ ConnectivityService: الخدمة مُهيأة مسبقاً');
      }
      return;
    }

    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        _updateConnectionStatus(results);
      },
    );

    _isInitialized = true;

    // فحص الحالة الأولية
    _checkInitialConnection();

    if (kDebugMode) {
      print('✅ ConnectivityService: تم تهيئة الخدمة');
    }
  }

  /// فحص الحالة الأولية للاتصال
  Future<void> _checkInitialConnection() async {
    try {
      final List<ConnectivityResult> results = await _connectivity.checkConnectivity();
      _updateConnectionStatus(results);
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في فحص الاتصال الأولي: $e');
      }
      _updateConnectionStatus([ConnectivityResult.none]);
    }
  }

  /// تحديث حالة الاتصال
  void _updateConnectionStatus(List<ConnectivityResult> results) async {
    bool hasConnection = false;
    
    // التحقق من وجود أي نوع من الاتصال
    for (ConnectivityResult result in results) {
      if (result != ConnectivityResult.none) {
        hasConnection = true;
        break;
      }
    }

    // إذا كان هناك اتصال، نتحقق من الإنترنت الفعلي
    if (hasConnection) {
      hasConnection = await _hasInternetConnection();
    }

    if (_isConnected != hasConnection) {
      _isConnected = hasConnection;
      _connectionStatusController.add(_isConnected);
      
      if (kDebugMode) {
        print('حالة الاتصال تغيرت: ${_isConnected ? "متصل" : "غير متصل"}');
      }
    }
  }

  /// التحقق من الاتصال الفعلي بالإنترنت
  Future<bool> _hasInternetConnection() async {
    // قائمة بالخوادم للاختبار (للتأكد من عدم الاعتماد على خادم واحد)
    final List<String> testHosts = ['google.com', '*******', 'cloudflare.com'];

    for (String host in testHosts) {
      try {
        if (kDebugMode) {
          print('🔍 ConnectivityService: محاولة الاتصال بـ $host...');
        }

        final result = await InternetAddress.lookup(host)
            .timeout(const Duration(seconds: 8)); // زيادة timeout

        if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
          if (kDebugMode) {
            print('✅ ConnectivityService: نجح الاتصال بـ $host');
          }
          return true;
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ ConnectivityService: فشل الاتصال بـ $host: $e');
        }
        continue; // جرب الخادم التالي
      }
    }

    if (kDebugMode) {
      print('❌ ConnectivityService: فشل في الاتصال بجميع الخوادم');
    }

    return false;
  }

  /// فحص جودة الاتصال بالإنترنت مع قياس الأداء
  Future<ConnectionInfo> checkInternetQuality() async {
    if (kDebugMode) {
      print('🌐 ConnectivityService: فحص جودة الاتصال بالإنترنت...');
    }

    // قائمة بالخوادم للاختبار مع أولوية
    final List<Map<String, dynamic>> testHosts = [
      {'host': '*******', 'timeout': 3}, // سريع - DNS Google
      {'host': 'google.com', 'timeout': 5}, // متوسط
      {'host': 'cloudflare.com', 'timeout': 8}, // بطيء
    ];

    int totalResponseTime = 0;
    int successfulTests = 0;
    bool hasConnection = false;
    List<int> responseTimes = [];

    for (Map<String, dynamic> hostInfo in testHosts) {
      try {
        final String host = hostInfo['host'];
        final int timeout = hostInfo['timeout'];

        if (kDebugMode) {
          print('🔍 ConnectivityService: اختبار $host (timeout: ${timeout}s)...');
        }

        final Stopwatch stopwatch = Stopwatch()..start();

        final result = await InternetAddress.lookup(host)
            .timeout(Duration(seconds: timeout));

        stopwatch.stop();
        final responseTime = stopwatch.elapsedMilliseconds;

        if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
          hasConnection = true;
          totalResponseTime += responseTime;
          successfulTests++;
          responseTimes.add(responseTime);

          if (kDebugMode) {
            print('✅ ConnectivityService: نجح الاتصال بـ $host في ${responseTime}ms');
          }

          // إذا كان الاتصال سريع، لا نحتاج لاختبار باقي الخوادم
          if (responseTime < 1000 && successfulTests >= 1) {
            break;
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ ConnectivityService: فشل الاتصال بـ ${hostInfo['host']}: $e');
        }
        // إضافة وقت استجابة عالي للفشل
        responseTimes.add(9999);
        continue;
      }
    }

    // حساب متوسط وقت الاستجابة
    final int avgResponseTime = successfulTests > 0
        ? (totalResponseTime / successfulTests).round()
        : 9999;

    // تحديد جودة الاتصال
    ConnectionQuality quality;
    if (!hasConnection) {
      quality = ConnectionQuality.none;
    } else if (avgResponseTime < 500) {
      quality = ConnectionQuality.good;
    } else if (avgResponseTime < 1000) {
      quality = ConnectionQuality.moderate;
    } else if (avgResponseTime < 2000) {
      quality = ConnectionQuality.slow;
    } else {
      quality = ConnectionQuality.poor;
    }

    // تحديد نوع الاتصال
    final connectivityResults = await _connectivity.checkConnectivity();
    String connectionType = 'غير معروف';
    for (var result in connectivityResults) {
      if (result == ConnectivityResult.wifi) {
        connectionType = 'WiFi';
        break;
      } else if (result == ConnectivityResult.mobile) {
        connectionType = 'بيانات الجوال';
        break;
      }
    }

    // تحديد استقرار الاتصال
    bool isStable = successfulTests >= 2;
    if (responseTimes.length >= 2) {
      // حساب التباين في أوقات الاستجابة
      final variance = responseTimes.map((time) => (time - avgResponseTime).abs()).reduce((a, b) => a + b) / responseTimes.length;
      isStable = variance < 1000; // مستقر إذا كان التباين أقل من ثانية
    }

    final connectionInfo = ConnectionInfo(
      quality: quality,
      responseTime: avgResponseTime,
      connectionType: connectionType,
      isStable: isStable,
      timestamp: DateTime.now(),
    );

    // تحديث الحالة الحالية
    _currentQuality = quality;
    _connectionQualityController.add(quality);

    if (kDebugMode) {
      print('📊 ConnectivityService: جودة الاتصال = ${connectionInfo.qualityText} (${avgResponseTime}ms, ${connectionInfo.connectionType})');
    }

    return connectionInfo;
  }

  /// فحص الاتصال يدوياً مع تقييم الجودة
  Future<bool> checkConnection({bool forceCheck = false, bool checkQuality = false}) async {
    try {
      // استخدام cache إذا لم يكن forceCheck
      if (!forceCheck && _lastCheckTime != null) {
        final timeSinceLastCheck = DateTime.now().difference(_lastCheckTime!);
        if (timeSinceLastCheck < _cacheTimeout) {
          if (kDebugMode) {
            print('📋 ConnectivityService: استخدام نتيجة cache = $_isConnected (${_currentQuality.name})');
          }
          return _isConnected;
        }
      }

      if (kDebugMode) {
        print('🔍 ConnectivityService: بدء فحص الاتصال...');
      }
      final List<ConnectivityResult> results = await _connectivity.checkConnectivity();
      if (kDebugMode) {
        print('📡 ConnectivityService: نتائج الاتصال = $results');
      }

      bool hasConnection = false;
      for (ConnectivityResult result in results) {
        if (result != ConnectivityResult.none) {
          hasConnection = true;
          break;
        }
      }
      if (kDebugMode) {
        print('📶 ConnectivityService: يوجد اتصال شبكة = $hasConnection');
      }

      if (hasConnection) {
        if (checkQuality) {
          // فحص مفصل لجودة الاتصال
          if (kDebugMode) {
            print('🌐 ConnectivityService: فحص جودة الاتصال...');
          }
          final connectionInfo = await checkInternetQuality();
          hasConnection = connectionInfo.quality != ConnectionQuality.none;
          if (kDebugMode) {
            print('🌍 ConnectivityService: جودة الاتصال = ${connectionInfo.qualityText}');
          }
        } else {
          // فحص بسيط للاتصال
          if (kDebugMode) {
            print('🌐 ConnectivityService: فحص الاتصال الفعلي بالإنترنت...');
          }
          hasConnection = await _hasInternetConnection();
          if (kDebugMode) {
            print('🌍 ConnectivityService: يوجد اتصال إنترنت فعلي = $hasConnection');
          }
        }
      } else {
        // لا يوجد اتصال شبكة
        _currentQuality = ConnectionQuality.none;
        _connectionQualityController.add(_currentQuality);
      }

      _isConnected = hasConnection;
      _lastCheckTime = DateTime.now();
      _connectionStatusController.add(_isConnected);
      if (kDebugMode) {
        print('✅ ConnectivityService: الحالة النهائية = $_isConnected');
      }

      return _isConnected;
    } catch (e) {
      if (kDebugMode) {
        print('❌ ConnectivityService: خطأ في فحص الاتصال: $e');
      }
      _isConnected = false;
      _lastCheckTime = DateTime.now();
      _connectionStatusController.add(_isConnected);
      return false;
    }
  }

  /// الحصول على معلومات مفصلة عن الاتصال
  Future<ConnectionInfo> getDetailedConnectionInfo() async {
    return await checkInternetQuality();
  }

  /// فحص سريع لجودة الاتصال (مع cache)
  Future<ConnectionQuality> getConnectionQuality({bool forceCheck = false}) async {
    if (!forceCheck && _lastCheckTime != null) {
      final timeSinceLastCheck = DateTime.now().difference(_lastCheckTime!);
      if (timeSinceLastCheck < _cacheTimeout) {
        return _currentQuality;
      }
    }

    final connectionInfo = await checkInternetQuality();
    return connectionInfo.quality;
  }

  /// التحقق من إمكانية تنفيذ عملية حسب جودة الاتصال
  Future<bool> canPerformAction({
    required ActionType actionType,
    bool forceCheck = false,
  }) async {
    final quality = await getConnectionQuality(forceCheck: forceCheck);

    switch (actionType) {
      case ActionType.lightBrowsing:
        return quality != ConnectionQuality.none && quality != ConnectionQuality.poor;

      case ActionType.normalBrowsing:
        return quality == ConnectionQuality.good ||
               quality == ConnectionQuality.moderate;

      case ActionType.dataUpload:
        return quality == ConnectionQuality.good ||
               quality == ConnectionQuality.moderate;

      case ActionType.imageUpload:
        return quality == ConnectionQuality.good;

      case ActionType.videoStreaming:
        return quality == ConnectionQuality.good;
    }
  }

  /// الحصول على رسالة توضيحية حسب جودة الاتصال
  String getQualityMessage(ConnectionQuality quality) {
    switch (quality) {
      case ConnectionQuality.none:
        return 'لا يوجد اتصال بالإنترنت';

      case ConnectionQuality.poor:
        return 'اتصال ضعيف جداً - قد تواجه صعوبة في استخدام التطبيق';

      case ConnectionQuality.slow:
        return 'اتصال بطيء - بعض العمليات قد تستغرق وقت أطول';

      case ConnectionQuality.moderate:
        return 'اتصال متوسط - يمكن استخدام معظم الميزات';

      case ConnectionQuality.good:
        return 'اتصال جيد - جميع الميزات متاحة';
    }
  }

  /// الحصول على لون مناسب لجودة الاتصال
  int getQualityColor(ConnectionQuality quality) {
    switch (quality) {
      case ConnectionQuality.none:
        return 0xFFE53E3E; // أحمر

      case ConnectionQuality.poor:
        return 0xFFDD6B20; // برتقالي محمر

      case ConnectionQuality.slow:
        return 0xFFF59E0B; // برتقالي

      case ConnectionQuality.moderate:
        return 0xFF10B981; // أخضر فاتح

      case ConnectionQuality.good:
        return 0xFF059669; // أخضر
    }
  }

  /// إنهاء الخدمة
  void dispose() {
    if (kDebugMode) {
      print('🔄 ConnectivityService: إنهاء الخدمة...');
    }

    _connectivitySubscription?.cancel();
    _connectivitySubscription = null;

    if (!_connectionStatusController.isClosed) {
      _connectionStatusController.close();
    }

    if (!_connectionQualityController.isClosed) {
      _connectionQualityController.close();
    }

    _isInitialized = false;

    if (kDebugMode) {
      print('✅ ConnectivityService: تم إنهاء الخدمة');
    }
  }

  /// إعادة تهيئة الخدمة
  void reset() {
    dispose();
    initialize();
  }

  /// الحصول على نوع الاتصال الحالي
  Future<String> getConnectionType() async {
    try {
      final List<ConnectivityResult> results = await _connectivity.checkConnectivity();
      
      if (results.isEmpty || results.contains(ConnectivityResult.none)) {
        return 'غير متصل';
      }
      
      if (results.contains(ConnectivityResult.wifi)) {
        return 'واي فاي';
      } else if (results.contains(ConnectivityResult.mobile)) {
        return 'بيانات الجوال';
      } else if (results.contains(ConnectivityResult.ethernet)) {
        return 'إيثرنت';
      } else {
        return 'نوع اتصال غير معروف';
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في الحصول على نوع الاتصال: $e');
      }
      return 'غير متصل';
    }
  }
}
