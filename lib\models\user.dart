class UserModel {
  final int id;
  final String phoneNumber;
  final String? name;
  final String? secretCode;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final List<String> favoriteAccounts; // حسابات طلبات المفضلة
  final int adViewsRemaining; // عدد مرات مشاهدة الإعلان المتبقية للتوريد
  final int rechargeAdViewsRemaining; // عدد مرات مشاهدة الإعلان المتبقية للشحن

  // حقول نظام التجديد الشهري
  final DateTime? lastRewardsRenewal; // تاريخ آخر تجديد للمكافآت
  final DateTime? nextRewardsRenewal; // تاريخ التجديد القادم
  final int renewalCount; // عدد مرات التجديد
  final int monthlySupplyRewards; // عدد مكافآت التوريد الشهرية
  final int monthlyRechargeRewards; // عدد مكافآت الشحن الشهرية

  UserModel({
    required this.id,
    required this.phoneNumber,
    this.name,
    this.secretCode,
    required this.createdAt,
    this.lastLogin,
    required this.favoriteAccounts,
    this.adViewsRemaining = 10, // القيمة الافتراضية 10 للتوريد
    this.rechargeAdViewsRemaining = 10, // القيمة الافتراضية 10 للشحن
    this.lastRewardsRenewal,
    this.nextRewardsRenewal,
    this.renewalCount = 0,
    this.monthlySupplyRewards = 10,
    this.monthlyRechargeRewards = 10,
  });

  // تحويل البيانات من Supabase إلى كائن UserModel
  factory UserModel.fromMap(Map<String, dynamic> map, dynamic userId) {
    return UserModel(
      id: userId is int ? userId : int.tryParse(userId.toString()) ?? 0,
      phoneNumber: map['phone_number'] ?? '',
      name: map['name'],
      secretCode: map['secret_code'],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      lastLogin: map['last_login'] != null
          ? DateTime.parse(map['last_login'])
          : null,
      favoriteAccounts: map['favorite_accounts'] != null
          ? List<String>.from(map['favorite_accounts'])
          : [],
      adViewsRemaining: map['ad_views_remaining'] ?? 10,
      rechargeAdViewsRemaining: map['recharge_ad_views_remaining'] ?? 10,
      lastRewardsRenewal: map['last_rewards_renewal'] != null
          ? DateTime.parse(map['last_rewards_renewal'])
          : null,
      nextRewardsRenewal: map['next_rewards_renewal'] != null
          ? DateTime.parse(map['next_rewards_renewal'])
          : null,
      renewalCount: map['renewal_count'] ?? 0,
      monthlySupplyRewards: map['monthly_supply_rewards'] ?? 10,
      monthlyRechargeRewards: map['monthly_recharge_rewards'] ?? 10,
    );
  }

  // تحويل كائن UserModel إلى Map لتخزينه في Supabase
  Map<String, dynamic> toMap() {
    return {
      'phone_number': phoneNumber,
      'name': name,
      'secret_code': secretCode,
      'created_at': createdAt.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
      'favorite_accounts': favoriteAccounts,
      'ad_views_remaining': adViewsRemaining,
      'recharge_ad_views_remaining': rechargeAdViewsRemaining,
      'last_rewards_renewal': lastRewardsRenewal?.toIso8601String(),
      'next_rewards_renewal': nextRewardsRenewal?.toIso8601String(),
      'renewal_count': renewalCount,
      'monthly_supply_rewards': monthlySupplyRewards,
      'monthly_recharge_rewards': monthlyRechargeRewards,
    };
  }

  // نسخة معدلة من الكائن
  UserModel copyWith({
    int? id,
    String? phoneNumber,
    String? name,
    String? secretCode,
    DateTime? createdAt,
    DateTime? lastLogin,
    List<String>? favoriteAccounts,
    int? adViewsRemaining,
    int? rechargeAdViewsRemaining,
    DateTime? lastRewardsRenewal,
    DateTime? nextRewardsRenewal,
    int? renewalCount,
    int? monthlySupplyRewards,
    int? monthlyRechargeRewards,
  }) {
    return UserModel(
      id: id ?? this.id,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      name: name ?? this.name,
      secretCode: secretCode ?? this.secretCode,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      favoriteAccounts: favoriteAccounts ?? this.favoriteAccounts,
      adViewsRemaining: adViewsRemaining ?? this.adViewsRemaining,
      rechargeAdViewsRemaining: rechargeAdViewsRemaining ?? this.rechargeAdViewsRemaining,
      lastRewardsRenewal: lastRewardsRenewal ?? this.lastRewardsRenewal,
      nextRewardsRenewal: nextRewardsRenewal ?? this.nextRewardsRenewal,
      renewalCount: renewalCount ?? this.renewalCount,
      monthlySupplyRewards: monthlySupplyRewards ?? this.monthlySupplyRewards,
      monthlyRechargeRewards: monthlyRechargeRewards ?? this.monthlyRechargeRewards,
    );
  }

  // دوال مساعدة لنظام التجديد

  /// التحقق من استحقاق المستخدم للتجديد
  bool get isDueForRenewal {
    if (nextRewardsRenewal == null) return false;
    return DateTime.now().isAfter(nextRewardsRenewal!);
  }

  /// عدد الأيام المتبقية للتجديد القادم
  int get daysUntilRenewal {
    if (nextRewardsRenewal == null) return -1;
    final difference = nextRewardsRenewal!.difference(DateTime.now());
    return difference.inDays;
  }

  /// نص حالة التجديد
  String get renewalStatusText {
    if (nextRewardsRenewal == null) return 'غير محدد';

    final days = daysUntilRenewal;
    if (days < 0) return 'مستحق للتجديد';
    if (days == 0) return 'التجديد اليوم';
    if (days == 1) return 'التجديد غداً';
    return 'التجديد خلال $days أيام';
  }

  /// التحقق من نفاد المكافآت
  bool get hasNoRewardsLeft {
    return adViewsRemaining <= 0 && rechargeAdViewsRemaining <= 0;
  }

  /// التحقق من وجود مكافآت توريد
  bool get hasSupplyRewards {
    return adViewsRemaining > 0;
  }

  /// التحقق من وجود مكافآت شحن
  bool get hasRechargeRewards {
    return rechargeAdViewsRemaining > 0;
  }
}