import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:wardlytec_app/services/connectivity_service.dart';

/// مراقب الاتصال أثناء عرض الإعلانات
class AdConnectionMonitor {
  static Timer? _connectionTimer;
  static bool _isMonitoring = false;
  static final ConnectivityService _connectivityService = ConnectivityService();

  /// بدء مراقبة الاتصال أثناء عرض الإعلان
  static void startMonitoring({
    required VoidCallback onConnectionLost,
    Duration checkInterval = const Duration(seconds: 2),
  }) {
    if (_isMonitoring) {
      if (kDebugMode) print('⚠️ AdConnectionMonitor: المراقبة نشطة بالفعل');
      return;
    }

    _isMonitoring = true;
    if (kDebugMode) print('🔍 AdConnectionMonitor: بدء مراقبة الاتصال أثناء الإعلان');

    _connectionTimer = Timer.periodic(checkInterval, (timer) async {
      try {
        final isConnected = await _connectivityService.checkConnection();
        
        if (!isConnected) {
          if (kDebugMode) print('❌ AdConnectionMonitor: انقطع الاتصال أثناء الإعلان');
          
          // إيقاف المراقبة
          stopMonitoring();
          
          // تنفيذ callback
          onConnectionLost();
        }
      } catch (e) {
        if (kDebugMode) print('❌ AdConnectionMonitor: خطأ في فحص الاتصال: $e');
      }
    });
  }

  /// إيقاف مراقبة الاتصال
  static void stopMonitoring() {
    if (_connectionTimer != null) {
      _connectionTimer!.cancel();
      _connectionTimer = null;
    }
    _isMonitoring = false;
    
    if (kDebugMode) print('🛑 AdConnectionMonitor: تم إيقاف مراقبة الاتصال');
  }

  /// التحقق من حالة المراقبة
  static bool get isMonitoring => _isMonitoring;

  /// فحص سريع للاتصال
  static Future<bool> quickConnectionCheck() async {
    try {
      return await _connectivityService.checkConnection();
    } catch (e) {
      if (kDebugMode) print('❌ AdConnectionMonitor: خطأ في الفحص السريع: $e');
      return false;
    }
  }

  /// مراقبة محسنة مع إعادة المحاولة
  static void startEnhancedMonitoring({
    required VoidCallback onConnectionLost,
    required VoidCallback onConnectionRestored,
    Duration checkInterval = const Duration(seconds: 2),
    int maxFailures = 3, // عدد الفشل المسموح قبل اعتبار الاتصال منقطع
  }) {
    if (_isMonitoring) return;

    _isMonitoring = true;
    int failureCount = 0;
    bool wasConnected = true;

    if (kDebugMode) print('🔍 AdConnectionMonitor: بدء المراقبة المحسنة');

    _connectionTimer = Timer.periodic(checkInterval, (timer) async {
      try {
        final isConnected = await _connectivityService.checkConnection();

        if (!isConnected) {
          failureCount++;

          if (failureCount >= maxFailures && wasConnected) {
            if (kDebugMode) print('❌ AdConnectionMonitor: تأكيد انقطاع الاتصال بعد $failureCount محاولات');
            wasConnected = false;
            onConnectionLost();
          }
        } else {
          // إعادة تعيين عداد الفشل
          failureCount = 0;

          if (!wasConnected) {
            if (kDebugMode) print('✅ AdConnectionMonitor: تم استعادة الاتصال');
            wasConnected = true;
            onConnectionRestored();
          }
        }
      } catch (e) {
        failureCount++;
        if (kDebugMode) print('❌ AdConnectionMonitor: خطأ في المراقبة المحسنة: $e');
      }
    });
  }

  /// مراقبة خاصة للإعلانات مع إعادة تفعيل الزر
  static void startAdMonitoring({
    required VoidCallback onConnectionLost,
    required VoidCallback onAdButtonReactivated,
    Duration checkInterval = const Duration(seconds: 1),
  }) {
    if (_isMonitoring) return;

    _isMonitoring = true;
    bool connectionLostNotified = false;

    if (kDebugMode) print('🔍 AdConnectionMonitor: بدء مراقبة الإعلانات');

    _connectionTimer = Timer.periodic(checkInterval, (timer) async {
      try {
        final isConnected = await _connectivityService.checkConnection();

        if (!isConnected && !connectionLostNotified) {
          if (kDebugMode) print('❌ AdConnectionMonitor: انقطع الاتصال أثناء الإعلان');
          connectionLostNotified = true;

          // إيقاف المراقبة
          stopMonitoring();

          // تنفيذ callback انقطاع الاتصال
          onConnectionLost();

          // إعادة تفعيل الزر بعد فترة قصيرة
          Timer(const Duration(milliseconds: 500), () {
            onAdButtonReactivated();
          });
        }
      } catch (e) {
        if (kDebugMode) print('❌ AdConnectionMonitor: خطأ في مراقبة الإعلانات: $e');
      }
    });
  }

  /// تنظيف الموارد
  static void dispose() {
    stopMonitoring();
    if (kDebugMode) print('🧹 AdConnectionMonitor: تم تنظيف الموارد');
  }
}
