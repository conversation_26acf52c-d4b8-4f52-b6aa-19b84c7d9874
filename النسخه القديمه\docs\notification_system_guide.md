# 🔔 دليل نظام الإشعارات المحلية

## 📋 نظرة عامة

تم إنشاء نظام إشعارات محلي متكامل **بدون Firebase** يستخدم:
- **flutter_local_notifications** للإشعارات المحلية
- **Supabase Realtime** لمراقبة تغييرات قاعدة البيانات
- **Background Services** للمراقبة المستمرة

---

## 🎯 الميزات الرئيسية

### ✅ **أنواع الإشعارات المدعومة:**
1. **تحديثات المعاملات** - عند تغيير حالة معاملات التوريد والشحن
2. **تحديثات الخدمات** - عند تعطيل أو تفعيل الخدمات
3. **إشعارات الصيانة** - للصيانة المجدولة والتحديثات المهمة
4. **العروض والترويج** - للعروض الخاصة والخصومات

### ✅ **الوظائف المتقدمة:**
- 🔄 **مراقبة فورية** لتغييرات قاعدة البيانات
- ⚙️ **إعدادات قابلة للتخصيص** لكل نوع إشعار
- 📊 **سجل شامل** للإشعارات المرسلة
- 🧪 **إشعارات تجريبية** لاختبار النظام
- 🛡️ **أمان متقدم** مع Row Level Security

---

## 🏗️ بنية النظام

### **1. الخدمات الأساسية:**

#### **LocalNotificationService**
```dart
// تهيئة النظام
await LocalNotificationService.initialize();

// إرسال إشعار
await LocalNotificationService.showNotification(
  id: 1,
  title: 'عنوان الإشعار',
  body: 'محتوى الإشعار',
);
```

#### **TransactionMonitorService**
```dart
// بدء المراقبة
await TransactionMonitorService.startMonitoring(userId);

// إيقاف المراقبة
await TransactionMonitorService.stopMonitoring();
```

#### **NotificationSettingsService**
```dart
// الحصول على الإعدادات
final settings = await NotificationSettingsService.getUserSettings(userId);

// تحديث إعداد
await NotificationSettingsService.updateUserSetting(userId, 'transaction_updates', true);
```

### **2. قاعدة البيانات:**

#### **الجداول:**
- `user_notification_settings` - إعدادات الإشعارات لكل مستخدم
- `notification_logs` - سجل الإشعارات المرسلة

#### **الدوال:**
- `get_user_notification_settings()` - جلب إعدادات المستخدم
- `update_user_notification_setting()` - تحديث إعداد معين
- `log_notification()` - تسجيل إشعار مرسل
- `get_notification_stats()` - إحصائيات الإشعارات

---

## 🚀 التثبيت والإعداد

### **1. إضافة التبعيات:**
```yaml
dependencies:
  flutter_local_notifications: ^17.2.2
  timezone: ^0.9.4
```

### **2. تشغيل ملف SQL:**
```sql
-- في Supabase SQL Editor
database_setup/create_notifications_tables.sql
```

### **3. تهيئة النظام في main.dart:**
```dart
await LocalNotificationService.initialize();
```

### **4. بدء المراقبة في الصفحة الرئيسية:**
```dart
await TransactionMonitorService.startMonitoring(userId);
```

---

## 📱 كيفية الاستخدام

### **للمستخدمين:**

#### **الوصول لإعدادات الإشعارات:**
```
القائمة الجانبية → إعدادات الإشعارات
```

#### **تخصيص الإشعارات:**
- ✅/❌ تفعيل/تعطيل كل نوع إشعار
- 🧪 اختبار الإشعارات
- 📊 عرض الإحصائيات

### **للمطورين:**

#### **إرسال إشعار مخصص:**
```dart
await LocalNotificationService.showTransactionUpdateNotification(
  transactionId: 'TXN123',
  oldStatus: 'pending',
  newStatus: 'approved',
  transactionType: 'supply',
  amount: 100.0,
);
```

#### **فحص إعدادات المستخدم:**
```dart
final isEnabled = await NotificationSettingsService.isNotificationEnabled(
  userId,
  'transaction_updates',
);
```

---

## 🔧 التخصيص والتطوير

### **إضافة نوع إشعار جديد:**

1. **تحديث قاعدة البيانات:**
```sql
INSERT INTO user_notification_settings (user_id, notification_type, is_enabled)
VALUES (user_id, 'new_notification_type', true);
```

2. **إضافة في الكود:**
```dart
// في NotificationSettingsService
static bool _getDefaultSetting(String notificationType) {
  switch (notificationType) {
    case 'new_notification_type':
      return true; // أو false حسب الحاجة
    // ...
  }
}
```

3. **إضافة في واجهة الإعدادات:**
```dart
_buildNotificationSetting(
  type: 'new_notification_type',
  title: 'نوع الإشعار الجديد',
  description: 'وصف النوع الجديد',
  icon: Icons.new_icon,
  iconColor: Colors.blue,
),
```

### **تخصيص شكل الإشعارات:**
```dart
final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  'channel_id',
  'Channel Name',
  channelDescription: 'وصف القناة',
  importance: Importance.high,
  priority: Priority.high,
  icon: '@mipmap/custom_icon', // أيقونة مخصصة
  color: const Color(0xFF2196F3), // لون مخصص
  enableVibration: true,
  playSound: true,
);
```

---

## 📊 المراقبة والإحصائيات

### **إحصائيات الإشعارات:**
```dart
final stats = await NotificationSettingsService.getNotificationStats(userId);
print('إجمالي الإشعارات: ${stats['total']}');
print('غير المقروءة: ${stats['unread']}');
print('اليوم: ${stats['today']}');
print('هذا الأسبوع: ${stats['this_week']}');
```

### **سجل الإشعارات:**
```dart
final logs = await NotificationSettingsService.getUserNotificationLogs(
  userId,
  limit: 20,
);
```

---

## 🛠️ استكشاف الأخطاء

### **المشاكل الشائعة:**

#### **1. الإشعارات لا تظهر:**
- تحقق من أذونات الإشعارات في الجهاز
- تأكد من تهيئة `LocalNotificationService`
- فحص إعدادات المستخدم في قاعدة البيانات

#### **2. المراقبة لا تعمل:**
- تحقق من اتصال Supabase Realtime
- تأكد من بدء `TransactionMonitorService`
- فحص logs للأخطاء

#### **3. قاعدة البيانات:**
- تأكد من تشغيل ملف SQL
- فحص Row Level Security policies
- تحقق من صحة user_id

### **أوامر التشخيص:**
```dart
// اختبار الإشعارات
await TransactionMonitorService.sendTestNotification();

// فحص حالة المراقبة
print('المراقبة نشطة: ${TransactionMonitorService.isMonitoring}');

// تنظيف البيانات القديمة
await NotificationSettingsService.cleanupOldNotifications();
```

---

## 🔒 الأمان والخصوصية

### **Row Level Security:**
- المستخدمون يرون إعداداتهم فقط
- سجل الإشعارات محمي لكل مستخدم
- النظام يمنع الوصول غير المصرح

### **حماية البيانات:**
- تشفير البيانات الحساسة
- تنظيف البيانات القديمة تلقائياً
- عدم تخزين معلومات شخصية غير ضرورية

---

## 📈 الأداء والتحسين

### **نصائح الأداء:**
- استخدم batch updates للإعدادات المتعددة
- نظف البيانات القديمة بانتظام
- راقب استهلاك البطارية

### **حدود النظام:**
- حد أقصى 50 إشعار في اليوم لكل مستخدم
- تنظيف تلقائي للإشعارات أكثر من 90 يوم
- إعادة محاولة تلقائية عند انقطاع الاتصال

---

## 🎉 الخلاصة

نظام الإشعارات المحلية يوفر:
- ✅ **بديل قوي لـ Firebase** بدون تكلفة إضافية
- ✅ **مراقبة فورية** لتغييرات المعاملات
- ✅ **تحكم كامل** في أنواع الإشعارات
- ✅ **أمان متقدم** وحماية البيانات
- ✅ **سهولة التخصيص** والتطوير

**النظام جاهز للاستخدام الفوري! 🚀**
