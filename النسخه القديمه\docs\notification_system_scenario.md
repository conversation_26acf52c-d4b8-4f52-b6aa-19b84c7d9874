# 🎬 سيناريو شامل لنظام الإشعارات المحلية

## 👤 **الشخصيات:**
- **أحمد**: مستخدم عادي يريد توريد رصيد
- **فاطمة**: مستخدمة تريد شحن رصيد هاتف
- **محمد**: المسؤول الذي يدير النظام
- **النظام**: تطبيق وردلي تك

---

## 🎯 **السيناريو الأول: تجربة المستخدم الكاملة**

### **📱 المرحلة 1: تسجيل الدخول وتفعيل الإشعارات**

#### **🔐 أحمد يسجل دخول للتطبيق:**
```
1. أحمد يفتح التطبيق
2. يسجل دخول بحسابه
3. النظام تلقائياً:
   ✅ يهيئ خدمة الإشعارات المحلية
   ✅ يبدأ مراقبة معاملات أحمد
   ✅ ينشئ الإعدادات الافتراضية للإشعارات
```

#### **⚙️ أحمد يتحقق من إعدادات الإشعارات:**
```
1. أحمد يفتح القائمة الجانبية
2. يضغط على "إعدادات الإشعارات"
3. يرى الإعدادات الافتراضية:
   ✅ تحديثات المعاملات: مفعل
   ✅ تحديثات الخدمات: مفعل
   ✅ إشعارات الصيانة: مفعل
   ❌ العروض والترويج: معطل
4. يضغط "إرسال إشعار تجريبي"
5. يظهر إشعار: "🧪 إشعار تجريبي - هذا إشعار تجريبي للتأكد من عمل النظام"
```

### **📱 المرحلة 2: إجراء معاملة توريد**

#### **💰 أحمد يبدأ معاملة توريد:**
```
1. أحمد يضغط على "بدء التوريد"
2. النظام يفحص حالة خدمة التوريد (متاحة)
3. ينتقل لصفحة اختيار الشركة
4. يختار "فودافون" ومبلغ 100 جنيه
5. يرفع صورة الإيصال
6. يضغط "إرسال المعاملة"
7. تُحفظ المعاملة بحالة "pending" (قيد المراجعة)
```

#### **🔔 النظام يرسل إشعار فوري:**
```
📱 إشعار فوري لأحمد:
"⏳ معاملة التوريد قيد المراجعة"
"المعاملة #TXN12345 بقيمة 100.00 ج.م
تم تحديث الحالة إلى: قيد المراجعة"
```

### **📱 المرحلة 3: معالجة المعاملة من المسؤول**

#### **👨‍💼 محمد (المسؤول) يراجع المعاملة:**
```
1. محمد يدخل للوحة تحكم المسؤول (7 ضغطات على العنوان + PIN)
2. يرى معاملة أحمد في قائمة المعاملات المعلقة
3. يراجع صورة الإيصال
4. يقرر قبول المعاملة
5. يغير حالة المعاملة من "pending" إلى "approved"
```

#### **🔔 النظام يرسل إشعار تحديث:**
```
📱 إشعار فوري لأحمد:
"✅ تم قبول معاملة التوريد"
"المعاملة #TXN12345 بقيمة 100.00 ج.م
تم تحديث الحالة إلى: مقبولة"
```

#### **💳 محمد يكمل المعاملة:**
```
1. محمد يضيف الرصيد لحساب أحمد
2. يغير حالة المعاملة إلى "completed"
```

#### **🔔 النظام يرسل إشعار الإنجاز:**
```
📱 إشعار فوري لأحمد:
"🎉 تم إنجاز معاملة التوريد"
"المعاملة #TXN12345 بقيمة 100.00 ج.م
تم تحديث الحالة إلى: مكتملة"
```

---

## 🎯 **السيناريو الثاني: تعطيل الخدمات**

### **📱 المرحلة 1: فاطمة تحاول شحن رصيد**

#### **📞 فاطمة تريد شحن رصيد هاتفها:**
```
1. فاطمة تفتح التطبيق وتسجل دخول
2. تضغط على "شحن رصيد الهاتف"
3. النظام يفحص حالة خدمة الشحن (متاحة)
4. تنتقل لصفحة اختيار الشبكة
5. تختار "أورانج" ورقم هاتفها
6. تختار مبلغ 50 جنيه
7. تضغط "تأكيد الشحن"
8. تُحفظ المعاملة بحالة "pending"
```

### **📱 المرحلة 2: المسؤول يعطل خدمة الشحن**

#### **⚠️ محمد يقرر تعطيل خدمة الشحن مؤقتاً:**
```
1. محمد يدخل للوحة تحكم المسؤول
2. يرى أن هناك مشكلة تقنية في خدمة الشحن
3. يضغط على مفتاح تبديل "خدمة الشحن"
4. يكتب رسالة مخصصة:
   "خدمة الشحن معطلة مؤقتاً للصيانة. 
   نعتذر عن الإزعاج وسنعيد تفعيلها خلال ساعة."
5. يضغط "حفظ"
```

#### **🔔 النظام يرسل إشعار لجميع المستخدمين:**
```
📱 إشعار لفاطمة وجميع المستخدمين:
"⚠️ تعطيل خدمة الشحن"
"خدمة الشحن معطلة مؤقتاً للصيانة. 
نعتذر عن الإزعاج وسنعيد تفعيلها خلال ساعة."
```

### **📱 المرحلة 3: مستخدم جديد يحاول الشحن**

#### **🚫 مستخدم جديد يحاول الوصول للخدمة المعطلة:**
```
1. مستخدم جديد يفتح التطبيق
2. يضغط على "شحن رصيد الهاتف"
3. النظام يفحص حالة خدمة الشحن (معطلة)
4. بدلاً من الانتقال للشحن، يظهر:

📱 صفحة "الخدمة غير متاحة":
┌─────────────────────────────────┐
│ ⚠️ خدمة الشحن غير متاحة        │
│                                 │
│ خدمة الشحن معطلة مؤقتاً للصيانة │
│ نعتذر عن الإزعاج وسنعيد تفعيلها │
│ خلال ساعة.                     │
│                                 │
│ [إعادة المحاولة] [تواصل معنا]    │
└─────────────────────────────────┘
```

---

## 🎯 **السيناريو الثالث: تخصيص الإشعارات**

### **📱 المرحلة 1: فاطمة تخصص إعداداتها**

#### **⚙️ فاطمة لا تريد إشعارات العروض:**
```
1. فاطمة تذهب لإعدادات الإشعارات
2. تعطل "العروض والترويج"
3. تبقي باقي الإعدادات مفعلة
4. تضغط حفظ
```

#### **📢 النظام يرسل إشعار ترويجي:**
```
النظام يحاول إرسال إشعار عرض خاص:
❌ لا يصل لفاطمة (معطل في إعداداتها)
✅ يصل لأحمد (مفعل في إعداداته)

📱 إشعار لأحمد فقط:
"🎁 عرض خاص - خصم 10% على جميع معاملات التوريد اليوم!"
```

---

## 🎯 **السيناريو الرابع: حالات الأخطاء والاستثناءات**

### **📱 المرحلة 1: انقطاع الإنترنت**

#### **📶 أحمد يفقد الاتصال بالإنترنت:**
```
1. أحمد يستخدم التطبيق بشكل طبيعي
2. ينقطع الإنترنت فجأة
3. النظام:
   ❌ يفقد الاتصال مع Supabase Realtime
   ✅ يحتفظ بالإعدادات المحلية
   ✅ يستمر في عرض البيانات المحفوظة محلياً
4. عندما يعود الإنترنت:
   ✅ النظام يعيد الاتصال تلقائياً
   ✅ يستأنف مراقبة الإشعارات
```

### **📱 المرحلة 2: خطأ في قاعدة البيانات**

#### **🗄️ مشكلة مؤقتة في قاعدة البيانات:**
```
1. محمد يحاول تحديث حالة معاملة
2. قاعدة البيانات لا تستجيب
3. النظام:
   ❌ لا يرسل إشعار للمستخدم
   ✅ يسجل الخطأ في logs
   ✅ لا يتعطل التطبيق
4. عندما تعود قاعدة البيانات:
   ✅ النظام يعاود المحاولة
   ✅ يرسل الإشعارات المؤجلة
```

---

## 🎯 **السيناريو الخامس: اختبار شامل للنظام**

### **📱 المرحلة 1: اختبار جميع أنواع الإشعارات**

#### **🧪 محمد يختبر النظام:**
```
1. محمد يدخل لوحة التحكم
2. ينشئ معاملة تجريبية ويغير حالتها:
   📱 "⏳ معاملة التوريد قيد المراجعة"
   📱 "✅ تم قبول معاملة التوريد"
   📱 "🎉 تم إنجاز معاملة التوريد"

3. يعطل ويفعل الخدمات:
   📱 "⚠️ تعطيل خدمة الشحن"
   📱 "✅ تم تفعيل خدمة الشحن"

4. يرسل إشعار صيانة:
   📱 "🔧 صيانة مجدولة - سيتم إيقاف الخدمة لمدة ساعة"
```

### **📱 المرحلة 2: فحص الإحصائيات**

#### **📊 محمد يراجع إحصائيات الإشعارات:**
```
في لوحة التحكم:
┌─────────────────────────────────┐
│ 📊 إحصائيات الإشعارات         │
│                                 │
│ إجمالي الإشعارات: 1,247       │
│ المرسلة اليوم: 89              │
│ معدل القراءة: 94%              │
│ أكثر الأنواع استخداماً:        │
│ 1. تحديثات المعاملات (67%)     │
│ 2. تحديثات الخدمات (23%)       │
│ 3. إشعارات الصيانة (10%)       │
└─────────────────────────────────┘
```

---

## 🎯 **النتائج المتوقعة:**

### **✅ للمستخدمين:**
- 📱 **إشعارات فورية** عند كل تحديث
- ⚙️ **تحكم كامل** في أنواع الإشعارات
- 🔄 **تجربة سلسة** حتى مع انقطاع الإنترنت
- 🛡️ **خصوصية محمية** - كل مستخدم يرى إشعاراته فقط

### **✅ للمسؤولين:**
- 🎛️ **تحكم مركزي** في جميع الخدمات
- 📊 **إحصائيات مفصلة** لاستخدام الإشعارات
- 🔧 **أدوات اختبار** للتأكد من عمل النظام
- ⚡ **استجابة فورية** لتغييرات النظام

### **✅ للنظام:**
- 🚀 **أداء عالي** بدون اعتماد على خدمات خارجية
- 🛡️ **أمان متقدم** مع Row Level Security
- 🔄 **مقاومة الأخطاء** واستمرارية الخدمة
- 📈 **قابلية التوسع** لآلاف المستخدمين

---

---

## 🎮 **سيناريو تفاعلي خطوة بخطوة**

### **🚀 اختبر النظام بنفسك:**

#### **الخطوة 1: التحضير**
```bash
# 1. تثبيت التبعيات
flutter pub get

# 2. تشغيل ملف SQL في Supabase
# انسخ محتوى database_setup/create_notifications_tables.sql
# والصقه في Supabase SQL Editor واضغط Run
```

#### **الخطوة 2: تشغيل التطبيق**
```bash
# شغل التطبيق
flutter run

# ستظهر رسالة في console:
# ✅ تم تهيئة خدمة الإشعارات المحلية بنجاح
```

#### **الخطوة 3: اختبار الإشعارات**
```
1. سجل دخول للتطبيق
2. اذهب للقائمة الجانبية
3. اضغط "إعدادات الإشعارات"
4. اضغط "إرسال إشعار تجريبي"
5. ستظهر رسالة: "📱 تم إرسال إشعار تجريبي"
6. ستحصل على إشعار في الجهاز!
```

#### **الخطوة 4: اختبار تحديث المعاملات**
```
1. أنشئ معاملة توريد جديدة
2. في console ستظهر: "🔍 تم بدء مراقبة المعاملات للمستخدم: [USER_ID]"
3. اذهب لقاعدة البيانات وغير حالة المعاملة يدوياً
4. ستحصل على إشعار فوري بالتحديث!
```

#### **الخطوة 5: اختبار تعطيل الخدمات**
```
1. ادخل كمسؤول (7 ضغطات على "وردلي تك" + PIN: 123456)
2. عطل خدمة التوريد
3. ستحصل على إشعار: "⚠️ تعطيل خدمة التوريد"
4. حاول الدخول لصفحة التوريد
5. ستظهر صفحة "الخدمة غير متاحة"!
```

---

## 📱 **رسائل النظام المتوقعة:**

### **في Console (للمطورين):**
```
✅ تم تهيئة خدمة الإشعارات المحلية بنجاح
🔍 تم بدء مراقبة المعاملات للمستخدم: 12345678-1234-1234-1234-123456789012
📡 تم بدء مراقبة تحديثات المعاملات
📡 تم بدء مراقبة تحديثات حالة الخدمات
🔄 تحديث معاملة: {id: TXN123, status: approved, ...}
📱 تم إرسال إشعار تحديث المعاملة: TXN123
📱 تم إرسال إشعار تجريبي
```

### **للمستخدمين (إشعارات الجهاز):**
```
🧪 إشعار تجريبي
هذا إشعار تجريبي للتأكد من عمل النظام

✅ تم قبول معاملة التوريد
المعاملة #TXN123 بقيمة 100.00 ج.م
تم تحديث الحالة إلى: مقبولة

⚠️ تعطيل خدمة التوريد
خدمة التوريد معطلة مؤقتاً للصيانة...
```

### **في واجهة التطبيق:**
```
✅ تم إرسال إشعار تجريبي
✅ تم تفعيل الإشعارات
✅ تم تعطيل الإشعارات
❌ فشل في تحميل الإعدادات: [خطأ]
⚠️ الخدمة غير متاحة حالياً
```

---

## 🔧 **استكشاف الأخطاء في السيناريو:**

### **إذا لم تظهر الإشعارات:**
```
1. تحقق من أذونات الإشعارات في إعدادات الجهاز
2. تأكد من ظهور رسالة "تم تهيئة خدمة الإشعارات" في console
3. جرب إعادة تشغيل التطبيق
4. تأكد من تشغيل ملف SQL في قاعدة البيانات
```

### **إذا لم تعمل المراقبة:**
```
1. تحقق من اتصال الإنترنت
2. تأكد من صحة إعدادات Supabase
3. راجع console للأخطاء
4. تأكد من تسجيل الدخول بشكل صحيح
```

### **إذا لم تظهر صفحة المسؤول:**
```
1. تأكد من الضغط 7 مرات سريعاً على "وردلي تك"
2. أدخل PIN الصحيح: 123456
3. تأكد من وجود ملف admin_control_screen.dart
4. راجع console للأخطاء
```

---

**🎬 انتهى السيناريو التفاعلي - جرب النظام بنفسك! 🚀**
